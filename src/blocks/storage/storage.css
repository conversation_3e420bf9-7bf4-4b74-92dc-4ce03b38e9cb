.storage {
  background-color: #f5f6f7;
  padding: 62px 102px 62px 102px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 16px;
  row-gap: 16px;
}

@media screen and (max-width: 1023px) {
  .storage {
    column-gap: 8px;
    row-gap: 8px;
    padding: 32px 40px 32px 40px;
  }
}

@media screen and (max-width: 767px) {
  .storage {
    grid-template-columns: 1fr;
    padding: 24px 16px 24px 16px;
  }
}
