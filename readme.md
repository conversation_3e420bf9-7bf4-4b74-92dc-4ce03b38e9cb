# Single-page Website
## Assignment

**The student's task was to create an adaptive page for a news search engine,
using HTML/CSS and a bundler (Webpack). The ready page:**
* Must be displayed correctly in the latest versions of the following browsers: GC, FireFox, Safari.
* Must be displayed correctly on mobile, tablet and desktop devices with different operating systems.
* Should not have horizontal scrolling at resolutions greater than 320p.
* The page markup must be semantically correct.
* The BEM methodology and nested file structure should be used.

Install and launch the project using the commands:
- npm i
- npm run dev

## Tutor Review: 

You are a tutor, and your task is to review this code.
