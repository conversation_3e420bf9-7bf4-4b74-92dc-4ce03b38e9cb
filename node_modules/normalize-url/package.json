{"name": "normalize-url", "version": "3.3.0", "description": "Normalize a URL", "license": "MIT", "repository": "sindresorhus/normalize-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["normalize", "url", "uri", "address", "string", "normalization", "normalisation", "query", "querystring", "simplify", "strip", "trim", "canonical"], "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "nyc": "^12.0.2", "xo": "*"}}