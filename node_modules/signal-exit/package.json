{"name": "signal-exit", "version": "3.0.2", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.10", "nyc": "^8.1.0", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^8.0.1"}}