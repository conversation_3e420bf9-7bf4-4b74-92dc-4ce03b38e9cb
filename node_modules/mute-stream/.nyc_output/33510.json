{"./mute.js": {"path": "./mute.js", "s": {"1": 1, "2": 1, "3": 1, "4": 7, "5": 7, "6": 7, "7": 7, "8": 7, "9": 7, "10": 7, "11": 7, "12": 1, "13": 1, "14": 1, "15": 10, "16": 1, "17": 6, "18": 1, "19": 1, "20": 5, "21": 1, "22": 1, "23": 8, "24": 1, "25": 2, "26": 1, "27": 5, "28": 1, "29": 5, "30": 1, "31": 2, "32": 2, "33": 1, "34": 2, "35": 2, "36": 1, "37": 2, "38": 2, "39": 1, "40": 25, "41": 13, "42": 5, "43": 8, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 8, "51": 0, "52": 0, "53": 0, "54": 8, "55": 20, "56": 1, "57": 2, "58": 2, "59": 0, "60": 2, "61": 2, "62": 0, "63": 2, "64": 1, "65": 3, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 1, "73": 1, "74": 1}, "b": {"1": [7, 5], "2": [7, 7], "3": [3, 5], "4": [3, 2], "5": [4, 1], "6": [0, 1], "7": [4, 1], "8": [0, 1], "9": [2, 0], "10": [2, 0], "11": [13, 12], "12": [5, 8], "13": [0, 8], "14": [0, 0], "15": [0, 8], "16": [8, 0, 0], "17": [2, 0], "18": [0, 2], "19": [2, 1], "20": [0, 2], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0]}, "f": {"1": 7, "2": 10, "3": 6, "4": 5, "5": 8, "6": 2, "7": 5, "8": 5, "9": 2, "10": 2, "11": 2, "12": 25, "13": 2, "14": 3, "15": 0}, "fnMap": {"1": {"name": "MuteStream", "line": 7, "loc": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 27}}}, "2": {"name": "(anonymous_2)", "line": 29, "loc": {"start": {"line": 29, "column": 28}, "end": {"line": 29, "column": 40}}}, "3": {"name": "(anonymous_3)", "line": 33, "loc": {"start": {"line": 33, "column": 30}, "end": {"line": 33, "column": 42}}}, "4": {"name": "onPipe", "line": 44, "loc": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 22}}}, "5": {"name": "getIsTTY", "line": 55, "loc": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 21}}}, "6": {"name": "setIsTTY", "line": 63, "loc": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 26}}}, "7": {"name": "(anonymous_7)", "line": 73, "loc": {"start": {"line": 73, "column": 7}, "end": {"line": 73, "column": 19}}}, "8": {"name": "(anonymous_8)", "line": 80, "loc": {"start": {"line": 80, "column": 7}, "end": {"line": 80, "column": 19}}}, "9": {"name": "(anonymous_9)", "line": 87, "loc": {"start": {"line": 87, "column": 28}, "end": {"line": 87, "column": 53}}}, "10": {"name": "(anonymous_10)", "line": 92, "loc": {"start": {"line": 92, "column": 29}, "end": {"line": 92, "column": 41}}}, "11": {"name": "(anonymous_11)", "line": 96, "loc": {"start": {"line": 96, "column": 30}, "end": {"line": 96, "column": 42}}}, "12": {"name": "(anonymous_12)", "line": 100, "loc": {"start": {"line": 100, "column": 29}, "end": {"line": 100, "column": 42}}}, "13": {"name": "(anonymous_13)", "line": 124, "loc": {"start": {"line": 124, "column": 27}, "end": {"line": 124, "column": 40}}}, "14": {"name": "proxy", "line": 136, "loc": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 20}}}, "15": {"name": "(anonymous_15)", "line": 136, "loc": {"start": {"line": 136, "column": 29}, "end": {"line": 136, "column": 41}}}}, "statementMap": {"1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 27}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 20, "column": 1}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 20}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 19}}, "6": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 38}}, "7": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 20}}, "8": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 31}}, "9": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 29}}, "10": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 36}}, "11": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 26}}, "12": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 54}}, "13": {"start": {"line": 24, "column": 0}, "end": {"line": 27, "column": 2}}, "14": {"start": {"line": 29, "column": 0}, "end": {"line": 31, "column": 1}}, "15": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 19}}, "16": {"start": {"line": 33, "column": 0}, "end": {"line": 35, "column": 1}}, "17": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 20}}, "18": {"start": {"line": 37, "column": 0}, "end": {"line": 42, "column": 2}}, "19": {"start": {"line": 44, "column": 0}, "end": {"line": 46, "column": 1}}, "20": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 17}}, "21": {"start": {"line": 48, "column": 0}, "end": {"line": 53, "column": 2}}, "22": {"start": {"line": 55, "column": 0}, "end": {"line": 60, "column": 1}}, "23": {"start": {"line": 56, "column": 2}, "end": {"line": 59, "column": 9}}, "24": {"start": {"line": 63, "column": 0}, "end": {"line": 70, "column": 1}}, "25": {"start": {"line": 64, "column": 2}, "end": {"line": 69, "column": 4}}, "26": {"start": {"line": 72, "column": 0}, "end": {"line": 77, "column": 44}}, "27": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 23}}, "28": {"start": {"line": 79, "column": 0}, "end": {"line": 84, "column": 44}}, "29": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 23}}, "30": {"start": {"line": 87, "column": 0}, "end": {"line": 90, "column": 1}}, "31": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 19}}, "32": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 56}}, "33": {"start": {"line": 92, "column": 0}, "end": {"line": 94, "column": 1}}, "34": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 41}}, "35": {"start": {"line": 93, "column": 17}, "end": {"line": 93, "column": 41}}, "36": {"start": {"line": 96, "column": 0}, "end": {"line": 98, "column": 1}}, "37": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 42}}, "38": {"start": {"line": 97, "column": 17}, "end": {"line": 97, "column": 42}}, "39": {"start": {"line": 100, "column": 0}, "end": {"line": 122, "column": 1}}, "40": {"start": {"line": 101, "column": 2}, "end": {"line": 120, "column": 3}}, "41": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 34}}, "42": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 34}}, "43": {"start": {"line": 103, "column": 4}, "end": {"line": 119, "column": 5}}, "44": {"start": {"line": 104, "column": 6}, "end": {"line": 108, "column": 7}}, "45": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 42}}, "46": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 42}}, "47": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 29}}, "48": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 29}}, "49": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 33}}, "50": {"start": {"line": 112, "column": 6}, "end": {"line": 117, "column": 7}}, "51": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 32}}, "52": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 39}}, "53": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 41}}, "54": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 50}}, "55": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 22}}, "56": {"start": {"line": 124, "column": 0}, "end": {"line": 134, "column": 1}}, "57": {"start": {"line": 125, "column": 2}, "end": {"line": 131, "column": 3}}, "58": {"start": {"line": 126, "column": 4}, "end": {"line": 130, "column": 5}}, "59": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 50}}, "60": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 14}}, "61": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 29}}, "62": {"start": {"line": 132, "column": 9}, "end": {"line": 132, "column": 29}}, "63": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": 18}}, "64": {"start": {"line": 136, "column": 0}, "end": {"line": 141, "column": 2}}, "65": {"start": {"line": 136, "column": 22}, "end": {"line": 141, "column": 1}}, "66": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 20}}, "67": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 19}}, "68": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 43}}, "69": {"start": {"line": 139, "column": 18}, "end": {"line": 139, "column": 43}}, "70": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 43}}, "71": {"start": {"line": 140, "column": 18}, "end": {"line": 140, "column": 43}}, "72": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 47}}, "73": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 55}}, "74": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 43}}}, "branchMap": {"1": {"line": 9, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 13}}, {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 19}}]}, "2": {"line": 18, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 28}}, {"start": {"line": 18, "column": 32}, "end": {"line": 18, "column": 36}}]}, "3": {"line": 56, "type": "cond-expr", "locations": [{"start": {"line": 56, "column": 25}, "end": {"line": 56, "column": 41}}, {"start": {"line": 57, "column": 10}, "end": {"line": 58, "column": 15}}]}, "4": {"line": 57, "type": "cond-expr", "locations": [{"start": {"line": 57, "column": 24}, "end": {"line": 57, "column": 39}}, {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": 15}}]}, "5": {"line": 74, "type": "cond-expr", "locations": [{"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 40}}, {"start": {"line": 75, "column": 12}, "end": {"line": 76, "column": 21}}]}, "6": {"line": 75, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 24}, "end": {"line": 75, "column": 38}}, {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 21}}]}, "7": {"line": 81, "type": "cond-expr", "locations": [{"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 43}}, {"start": {"line": 82, "column": 12}, "end": {"line": 83, "column": 21}}]}, "8": {"line": 82, "type": "cond-expr", "locations": [{"start": {"line": 82, "column": 24}, "end": {"line": 82, "column": 41}}, {"start": {"line": 83, "column": 12}, "end": {"line": 83, "column": 21}}]}, "9": {"line": 93, "type": "if", "locations": [{"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 2}}, {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 2}}]}, "10": {"line": 97, "type": "if", "locations": [{"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 2}}, {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 2}}]}, "11": {"line": 101, "type": "if", "locations": [{"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 2}}, {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 2}}]}, "12": {"line": 102, "type": "if", "locations": [{"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 4}}, {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 4}}]}, "13": {"line": 103, "type": "if", "locations": [{"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 4}}, {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 4}}]}, "14": {"line": 104, "type": "if", "locations": [{"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 6}}, {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 6}}]}, "15": {"line": 112, "type": "if", "locations": [{"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 6}}, {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 6}}]}, "16": {"line": 112, "type": "binary-expr", "locations": [{"start": {"line": 112, "column": 10}, "end": {"line": 112, "column": 22}}, {"start": {"line": 112, "column": 26}, "end": {"line": 112, "column": 42}}, {"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": 39}}]}, "17": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 2}}, {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 2}}]}, "18": {"line": 126, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 4}}, {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 4}}]}, "19": {"line": 126, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 9}}, {"start": {"line": 126, "column": 13}, "end": {"line": 126, "column": 25}}]}, "20": {"line": 132, "type": "if", "locations": [{"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 2}}, {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 2}}]}, "21": {"line": 139, "type": "if", "locations": [{"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 2}}, {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 2}}]}, "22": {"line": 139, "type": "binary-expr", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 7}}, {"start": {"line": 139, "column": 11}, "end": {"line": 139, "column": 16}}]}, "23": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 2}}, {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 2}}]}, "24": {"line": 140, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 7}}, {"start": {"line": 140, "column": 11}, "end": {"line": 140, "column": 16}}]}}}}