<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">77.03% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>57/74</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.14% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>28/49</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">93.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>14/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.1% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>53/67</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="__root__/"><a href="__root__/index.html">__root__/</a></td>
	<td data-value="77.03" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 77%;"></div><div class="cover-empty" style="width:23%;"></div></div></td>
	<td data-value="77.03" class="pct medium">77.03%</td>
	<td data-value="74" class="abs medium">57/74</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="49" class="abs medium">28/49</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="79.1" class="pct medium">79.1%</td>
	<td data-value="67" class="abs medium">53/67</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 12 2016 22:19:00 GMT-0800 (PST)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
