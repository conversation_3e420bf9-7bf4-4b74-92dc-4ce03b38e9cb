<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for mute.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">__root__/</a> mute.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">77.03% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>57/74</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.14% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>28/49</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">93.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>14/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.1% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>53/67</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">25×</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var Stream = require('stream')
&nbsp;
module.exports = MuteStream
&nbsp;
// var out = new MuteStream(process.stdout)
// argument auto-pipes
function MuteStream (opts) {
  Stream.apply(this)
  opts = opts || {}
  this.writable = this.readable = true
  this.muted = false
  this.on('pipe', this._onpipe)
  this.replace = opts.replace
&nbsp;
  // For readline-type situations
  // This much at the start of a line being redrawn after a ctrl char
  // is seen (such as backspace) won't be redrawn as the replacement
  this._prompt = opts.prompt || null
  this._hadControl = false
}
&nbsp;
MuteStream.prototype = Object.create(Stream.prototype)
&nbsp;
Object.defineProperty(MuteStream.prototype, 'constructor', {
  value: MuteStream,
  enumerable: false
})
&nbsp;
MuteStream.prototype.mute = function () {
  this.muted = true
}
&nbsp;
MuteStream.prototype.unmute = function () {
  this.muted = false
}
&nbsp;
Object.defineProperty(MuteStream.prototype, '_onpipe', {
  value: onPipe,
  enumerable: false,
  writable: true,
  configurable: true
})
&nbsp;
function onPipe (src) {
  this._src = src
}
&nbsp;
Object.defineProperty(MuteStream.prototype, 'isTTY', {
  get: getIsTTY,
  set: setIsTTY,
  enumerable: true,
  configurable: true
})
&nbsp;
function getIsTTY () {
  return( (this._dest) ? this._dest.isTTY
        : (this._src) ? this._src.isTTY
        : false
        )
}
&nbsp;
// basically just get replace the getter/setter with a regular value
function setIsTTY (isTTY) {
  Object.defineProperty(this, 'isTTY', {
    value: isTTY,
    enumerable: true,
    writable: true,
    configurable: true
  })
}
&nbsp;
Object.defineProperty(MuteStream.prototype, 'rows', {
  get: function () {
    return( this._dest ? this._dest.rows
          : this._src ? <span class="branch-0 cbranch-no" title="branch not covered" >this._src.rows</span>
          : undefined )
  }, enumerable: true, configurable: true })
&nbsp;
Object.defineProperty(MuteStream.prototype, 'columns', {
  get: function () {
    return( this._dest ? this._dest.columns
          : this._src ? <span class="branch-0 cbranch-no" title="branch not covered" >this._src.columns</span>
          : undefined )
  }, enumerable: true, configurable: true })
&nbsp;
&nbsp;
MuteStream.prototype.pipe = function (dest, options) {
  this._dest = dest
  return Stream.prototype.pipe.call(this, dest, options)
}
&nbsp;
MuteStream.prototype.pause = function () {
  <span class="missing-if-branch" title="else path not taken" >E</span>if (this._src) return this._src.pause()
}
&nbsp;
MuteStream.prototype.resume = function () {
  <span class="missing-if-branch" title="else path not taken" >E</span>if (this._src) return this._src.resume()
}
&nbsp;
MuteStream.prototype.write = function (c) {
  if (this.muted) {
    if (!this.replace) return true
    <span class="missing-if-branch" title="if path not taken" >I</span>if (c.match(/^\u001b/)) {
<span class="cstat-no" title="statement not covered" >      if(c.indexOf(this._prompt) === 0) {</span>
<span class="cstat-no" title="statement not covered" >        c = c.substr(this._prompt.length);</span>
<span class="cstat-no" title="statement not covered" >        c = c.replace(/./g, this.replace);</span>
<span class="cstat-no" title="statement not covered" >        c = this._prompt + c;</span>
      }
<span class="cstat-no" title="statement not covered" >      this._hadControl = true</span>
<span class="cstat-no" title="statement not covered" >      return this.emit('data', c)</span>
    } else {
      <span class="missing-if-branch" title="if path not taken" >I</span>if (this._prompt &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >this._hadControl </span>&amp;&amp;
<span class="branch-2 cbranch-no" title="branch not covered" >          c.indexOf(this._prompt) === 0)</span> {
<span class="cstat-no" title="statement not covered" >        this._hadControl = false</span>
<span class="cstat-no" title="statement not covered" >        this.emit('data', this._prompt)</span>
<span class="cstat-no" title="statement not covered" >        c = c.substr(this._prompt.length)</span>
      }
      c = c.toString().replace(/./g, this.replace)
    }
  }
  this.emit('data', c)
}
&nbsp;
MuteStream.prototype.end = function (c) {
  <span class="missing-if-branch" title="else path not taken" >E</span>if (this.muted) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (c &amp;&amp; this.replace) {
<span class="cstat-no" title="statement not covered" >      c = c.toString().replace(/./g, this.replace)</span>
    } else {
      c = null
    }
  }
  <span class="missing-if-branch" title="if path not taken" >I</span>if (c) <span class="cstat-no" title="statement not covered" >this.emit('data', c)</span>
  this.emit('end')
}
&nbsp;
function proxy (fn) { return <span class="fstat-no" title="function not covered" >function () {</span>
<span class="cstat-no" title="statement not covered" >  var d = this._dest</span>
<span class="cstat-no" title="statement not covered" >  var s = this._src</span>
<span class="cstat-no" title="statement not covered" >  if (d &amp;&amp; d[fn]) <span class="cstat-no" title="statement not covered" >d[fn].apply(d, arguments)</span></span>
<span class="cstat-no" title="statement not covered" >  if (s &amp;&amp; s[fn]) <span class="cstat-no" title="statement not covered" >s[fn].apply(s, arguments)</span></span>
}}
&nbsp;
MuteStream.prototype.destroy = proxy('destroy')
MuteStream.prototype.destroySoon = proxy('destroySoon')
MuteStream.prototype.close = proxy('close')
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 12 2016 22:19:00 GMT-0800 (PST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
