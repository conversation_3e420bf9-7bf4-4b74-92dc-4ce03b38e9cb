{"name": "mute-stream", "version": "0.0.7", "main": "mute.js", "directories": {"test": "test"}, "devDependencies": {"tap": "^5.4.4"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git://github.com/isaacs/mute-stream"}, "keywords": ["mute", "stream", "pipe"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "description": "Bytes go in, but they don't come out (when muted)."}