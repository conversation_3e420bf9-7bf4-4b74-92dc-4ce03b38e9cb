{"root": true, "extends": "@ljharb", "rules": {"complexity": [2, 17], "id-length": [2, {"min": 1, "max": 30}], "max-nested-callbacks": [2, 3], "max-statements": [2, 33], "max-statements-per-line": [2, {"max": 2}], "no-invalid-this": [1], "no-magic-numbers": [1, {"ignore": [0]}], "no-restricted-syntax": [2, "BreakStatement", "ContinueStatement", "DebuggerStatement", "LabeledStatement", "WithStatement"], "no-unused-vars": [1, {"vars": "all", "args": "after-used"}]}}