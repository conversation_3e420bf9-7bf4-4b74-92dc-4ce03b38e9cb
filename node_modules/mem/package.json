{"name": "mem", "version": "4.3.0", "description": "Memoize functions - An optimization used to speed up consecutive function calls by caching the result of calls with identical input", "license": "MIT", "repository": "sindresorhus/mem", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["memoize", "function", "mem", "memoization", "cache", "caching", "optimize", "performance", "ttl", "expire", "promise"], "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}}