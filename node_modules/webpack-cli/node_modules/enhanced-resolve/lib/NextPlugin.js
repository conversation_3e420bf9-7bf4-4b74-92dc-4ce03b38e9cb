/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/
"use strict";

module.exports = class NextPlugin {
	constructor(source, target) {
		this.source = source;
		this.target = target;
	}

	apply(resolver) {
		const target = resolver.ensureHook(this.target);
		resolver.getHook(this.source).tapAsync("NextPlugin", (request, resolveContext, callback) => {
			resolver.doResolve(target, request, null, resolveContext, callback);
		});
	}
};
