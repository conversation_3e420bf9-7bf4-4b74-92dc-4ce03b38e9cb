{"name": "v8-compile-cache", "version": "2.0.3", "description": "Require hook for automatic V8 compile cache persistence", "main": "v8-compile-cache.js", "scripts": {"bench": "bench/run.sh", "lint": "eslint --max-warnings=0 .", "test": "tap test/*-test.js", "posttest": "npm run lint"}, "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/zertosh/v8-compile-cache.git"}, "files": ["v8-compile-cache.js"], "license": "MIT", "dependencies": {}, "devDependencies": {"babel-core": "6.23.1", "eslint": "^3.15.0", "flow-parser": "0.38.0", "rimraf": "^2.5.4", "rxjs": "5.2.0", "semver": "^5.3.0", "tap": "^10.1.1", "temp": "^0.8.3", "yarn": "0.20.3"}}