{"name": "webpack-cli", "version": "3.3.7", "description": "CLI for webpack & friends", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "bin": {"webpack-cli": "./bin/cli.js"}, "main": "./bin/cli.js", "engines": {"node": ">=6.11.5"}, "keywords": ["webpack", "cli", "scaffolding", "module", "bundler", "web"], "files": ["bin", "scripts"], "scripts": {"bootstrap": "npm run clean:all && npm install && lerna bootstrap", "build": "tsc", "changelog": "conventional-changelog --config ./build/changelog-generator/index.js --infile CHANGELOG.md --same-file", "clean:all": "rimraf node_modules packages/*/{node_modules}", "format": "npm run format:js && npm run format:ts", "format:js": "prettier-eslint ./bin/*.js ./bin/**/*.js ./test/**/*.js ./packages/**/**/*.js ./packages/**/*.js --write", "format:ts": "prettier-eslint ./packages/**/**/*.ts ./packages/**/*.ts ./packages/**/**/**/*.ts --write", "lint": "eslint \"./bin/*.js\" \"./bin/**/*.js\" \"./test/**/*.js\" \"packages/**/!(node_modules)/*.ts\"  \"packages/**/!(node_modules)/**/*.ts\"", "pretest": "npm run build && npm run lint", "reportCoverage": "nyc report --reporter=json && codecov -f coverage/coverage-final.json --disable=gcov", "test": "nyc jest --maxWorkers=4 --reporters=default --reporters=jest-junit", "test:cli": "nyc jest test/ --maxWorkers=4 --reporters=default --reporters=jest-junit", "test:packages": "nyc jest packages/ --maxWorkers=4 --reporters=default --reporters=jest-junit", "test:ci": "nyc jest --maxWorkers=$(nproc) --reporters=default --reporters=jest-junit", "travis:integration": "npm run build && npm run test && npm run reportCoverage", "travis:lint": "npm run build && npm run lint", "watch": "npm run build && tsc -w", "publish:monorepo": "npm run format && npm run test && lerna publish -m \"chore: monorepo version update\""}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.md": ["prettier --parser markdown --write", "git add"], "{packages,bin}/**/!(__testfixtures__)/**.js": ["eslint --fix", "git add"], "*.ts": ["npm run format:ts", "git add"], "*.js": ["npm run format:js", "git add"]}, "jest": {"testPathIgnorePatterns": ["/node_modules/"], "testEnvironment": "node", "collectCoverage": true, "coverageReporters": ["json", "html", "cobertura"], "transform": {"^.+\\.(ts)?$": "ts-jest"}, "testRegex": ["/__tests__/.*\\.(test.js|test.ts)$", "/test/.*\\.(test.js|test.ts)$"], "moduleFileExtensions": ["ts", "js", "json"]}, "nyc": {"include": ["bin/**.js", "packages/**/*.js"], "reporter": ["lcov"], "all": true}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.js"}}, "dependencies": {"chalk": "2.4.2", "cross-spawn": "6.0.5", "enhanced-resolve": "4.1.0", "findup-sync": "3.0.0", "global-modules": "2.0.0", "import-local": "2.0.0", "interpret": "1.2.0", "loader-utils": "1.2.3", "supports-color": "6.1.0", "v8-compile-cache": "2.0.3", "yargs": "13.2.4"}, "peerDependencies": {"webpack": "4.x.x"}, "devDependencies": {"@babel/preset-env": "7.4.5", "@babel/register": "7.4.4", "@commitlint/cli": "8.1.0", "@commitlint/config-lerna-scopes": "8.0.0", "@commitlint/travis-cli": "8.0.0", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1", "@types/jest": "24.0.15", "@types/node": "12.0.8", "@typescript-eslint/eslint-plugin": "1.10.2", "@typescript-eslint/parser": "1.10.2", "babel-preset-env": "1.7.0", "babel-preset-jest": "24.6.0", "codecov": "3.5.0", "commitizen": "4.0.3", "commitlint-config-cz": "0.12.0", "conventional-changelog-cli": "2.0.21", "cz-customizable": "6.2.0", "eslint": "5.16.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "esm": "3.2.25", "execa": "1.0.0", "husky": "2.4.1", "jest": "24.8.0", "jest-cli": "24.8.0", "jest-junit": "6.4.0", "lerna": "3.15.0", "lint-staged": "8.2.1", "nyc": "14.1.1", "prettier": "1.18.2", "prettier-eslint-cli": "5.0.0", "readable-stream": "3.4.0", "rimraf": "2.6.3", "schema-utils": "1.0.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "typescript": "3.5.2", "webpack": "4.x.x", "webpack-dev-server": "3.7.2"}}