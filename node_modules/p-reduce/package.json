{"name": "p-reduce", "version": "1.0.0", "description": "Reduce a list of values using promises into a promise for a value", "license": "MIT", "repository": "sindresorhus/p-reduce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "reduce", "collection", "iterable", "iterator", "async", "await", "promises", "accumulate", "bluebird"], "devDependencies": {"ava": "*", "delay": "^1.3.1", "xo": "*"}, "xo": {"esnext": true}}