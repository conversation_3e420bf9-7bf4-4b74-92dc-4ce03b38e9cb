{"name": "p-timeout", "version": "1.2.1", "description": "Timeout a promise after a specified amount of time", "license": "MIT", "repository": "sindresorhus/p-timeout", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "timeout", "error", "invalidate", "async", "await", "promises", "time", "out", "cancel", "bluebird"], "dependencies": {"p-finally": "^1.0.0"}, "devDependencies": {"ava": "*", "delay": "^2.0.0", "xo": "*"}}