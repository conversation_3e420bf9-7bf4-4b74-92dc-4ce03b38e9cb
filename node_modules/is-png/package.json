{"name": "is-png", "version": "1.1.0", "description": "Check if a Buffer/Uint8Array is a PNG image", "license": "MIT", "repository": "sindresorhus/is-png", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["png", "portable", "network", "graphics", "image", "img", "pic", "picture", "photo", "type", "detect", "check", "is", "exif", "binary", "buffer", "uint8array"], "devDependencies": {"mocha": "*", "read-chunk": "^1.0.0"}}