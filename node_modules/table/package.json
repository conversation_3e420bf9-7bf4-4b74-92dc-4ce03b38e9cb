{"author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "url": "http://gajus.com"}, "dependencies": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}, "description": "Formats data into a string table.", "devDependencies": {"@babel/cli": "^7.5.0", "@babel/core": "^7.5.4", "@babel/node": "^7.5.0", "@babel/plugin-transform-flow-strip-types": "^7.4.4", "@babel/preset-env": "^7.5.4", "@babel/register": "^7.4.4", "ajv-cli": "^3.0.0", "ajv-keywords": "^3.4.1", "babel-plugin-istanbul": "^5.1.4", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-export-default-name": "^2.0.4", "chai": "^4.2.0", "chalk": "^2.4.2", "coveralls": "^3.0.5", "eslint": "^5.13.0", "eslint-config-canonical": "^16.1.0", "flow-bin": "^0.102.0", "flow-copy-source": "^2.0.7", "gitdown": "^3.1.1", "husky": "^3.0.0", "mocha": "^6.1.4", "nyc": "^14.1.1", "semantic-release": "^15.13.18", "sinon": "^7.3.2"}, "engines": {"node": ">=6.0.0"}, "husky": {"hooks": {"post-commit": "npm run create-readme && git add README.md && git commit -m 'docs: generate docs' --no-verify", "pre-commit": "npm run lint && npm run test && npm run build"}}, "keywords": ["ascii", "text", "table", "align", "ansi"], "license": "BSD-3-<PERSON><PERSON>", "main": "./dist/index.js", "name": "table", "nyc": {"include": ["src/**/*.js"], "instrument": false, "reporter": ["text-lcov"], "require": ["@babel/register"], "sourceMap": false}, "repository": {"type": "git", "url": "https://github.com/gajus/table"}, "scripts": {"build": "rm -fr ./dist && NODE_ENV=production babel ./src --out-dir ./dist --copy-files --source-maps && npm run create-validators && flow-copy-source src dist", "create-readme": "gitdown ./.README/README.md --output-file ./README.md", "create-validators": "ajv compile --all-errors --inline-refs=false -s src/schemas/config -c ajv-keywords/keywords/typeof -o dist/validateConfig.js && ajv compile --all-errors --inline-refs=false -s src/schemas/streamConfig -c ajv-keywords/keywords/typeof -o dist/validateStreamConfig.js", "lint": "npm run build && eslint ./src ./test && flow", "test": "mocha --require @babel/register"}, "version": "5.4.6"}