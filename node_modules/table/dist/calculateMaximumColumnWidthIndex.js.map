{"version": 3, "sources": ["../src/calculateMaximumColumnWidthIndex.js"], "names": ["rows", "Error", "columns", "Array", "length", "fill", "for<PERSON>ach", "row", "columnWidthIndex", "valueWidth", "index0"], "mappings": ";;;;;;;AAAA;;;;AAEA;;;;;;yCAMgBA,I,IAAS;AACvB,MAAI,CAACA,IAAI,CAAC,CAAD,CAAT,EAAc;AACZ,UAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAMC,OAAO,GAAG,IAAIC,KAAJ,CAAUH,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAAlB,EAA0BC,IAA1B,CAA+B,CAA/B,CAAhB;AAEAL,EAAAA,IAAI,CAACM,OAAL,CAAcC,GAAD,IAAS;AACpB,UAAMC,gBAAgB,GAAG,sCAAwBD,GAAxB,CAAzB;AAEAC,IAAAA,gBAAgB,CAACF,OAAjB,CAAyB,CAACG,UAAD,EAAaC,MAAb,KAAwB;AAC/C,UAAIR,OAAO,CAACQ,MAAD,CAAP,GAAkBD,UAAtB,EAAkC;AAChCP,QAAAA,OAAO,CAACQ,MAAD,CAAP,GAAkBD,UAAlB;AACD;AACF,KAJD;AAKD,GARD;AAUA,SAAOP,OAAP;AACD,C", "sourcesContent": ["import calculateCellWidthIndex from './calculateCellWidthIndex';\n\n/**\n * Produces an array of values that describe the largest value length (width) in every column.\n *\n * @param {Array[]} rows\n * @returns {number[]}\n */\nexport default (rows) => {\n  if (!rows[0]) {\n    throw new Error('Dataset must have at least one row.');\n  }\n\n  const columns = new Array(rows[0].length).fill(0);\n\n  rows.forEach((row) => {\n    const columnWidthIndex = calculateCellWidthIndex(row);\n\n    columnWidthIndex.forEach((valueWidth, index0) => {\n      if (columns[index0] < valueWidth) {\n        columns[index0] = valueWidth;\n      }\n    });\n  });\n\n  return columns;\n};\n"], "file": "calculateMaximumColumnWidthIndex.js"}