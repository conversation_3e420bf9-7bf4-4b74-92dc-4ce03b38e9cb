{"version": 3, "sources": ["../src/drawTable.js"], "names": ["rows", "border", "columnSizeIndex", "rowSpanIndex", "drawHorizontalLine", "singleLine", "output", "realRowIndex", "rowHeight", "rowCount", "length", "for<PERSON>ach", "row", "index0"], "mappings": ";;;;;;;AAAA;;AAKA;;;;AAEA;;;;;;;;;mBASgBA,I,EAAMC,M,EAAQC,e,EAAiBC,Y,EAAcC,kB,EAAoBC,U,KAAe;AAC9F,MAAIC,MAAJ;AACA,MAAIC,YAAJ;AACA,MAAIC,SAAJ;AAEA,QAAMC,QAAQ,GAAGT,IAAI,CAACU,MAAtB;AAEAH,EAAAA,YAAY,GAAG,CAAf;AAEAD,EAAAA,MAAM,GAAG,EAAT;;AAEA,MAAIF,kBAAkB,CAACG,YAAD,EAAeE,QAAf,CAAtB,EAAgD;AAC9CH,IAAAA,MAAM,IAAI,+BAAcJ,eAAd,EAA+BD,MAA/B,CAAV;AACD;;AAEDD,EAAAA,IAAI,CAACW,OAAL,CAAa,CAACC,GAAD,EAAMC,MAAN,KAAiB;AAC5BP,IAAAA,MAAM,IAAI,sBAAQM,GAAR,EAAaX,MAAb,CAAV;;AAEA,QAAI,CAACO,SAAL,EAAgB;AACdA,MAAAA,SAAS,GAAGL,YAAY,CAACI,YAAD,CAAxB;AAEAA,MAAAA,YAAY;AACb;;AAEDC,IAAAA,SAAS;;AAET,QAAI,CAACH,UAAD,IAAeG,SAAS,KAAK,CAA7B,IAAkCK,MAAM,KAAKJ,QAAQ,GAAG,CAAxD,IAA6DL,kBAAkB,CAACG,YAAD,EAAeE,QAAf,CAAnF,EAA6G;AAC3GH,MAAAA,MAAM,IAAI,gCAAeJ,eAAf,EAAgCD,MAAhC,CAAV;AACD;AACF,GAdD;;AAgBA,MAAIG,kBAAkB,CAACG,YAAD,EAAeE,QAAf,CAAtB,EAAgD;AAC9CH,IAAAA,MAAM,IAAI,kCAAiBJ,eAAjB,EAAkCD,MAAlC,CAAV;AACD;;AAED,SAAOK,MAAP;AACD,C", "sourcesContent": ["import {\n  drawBorderTop,\n  drawBorderJoin,\n  drawBorderBottom\n} from './drawBorder';\nimport drawRow from './drawRow';\n\n/**\n * @param {Array} rows\n * @param {Object} border\n * @param {Array} columnSizeIndex\n * @param {Array} rowSpanIndex\n * @param {Function} drawHorizontalLine\n * @param {boolean} singleLine\n * @returns {string}\n */\nexport default (rows, border, columnSizeIndex, rowSpanIndex, drawHorizontalLine, singleLine) => {\n  let output;\n  let realRowIndex;\n  let rowHeight;\n\n  const rowCount = rows.length;\n\n  realRowIndex = 0;\n\n  output = '';\n\n  if (drawHorizontalLine(realRowIndex, rowCount)) {\n    output += drawBorderTop(columnSizeIndex, border);\n  }\n\n  rows.forEach((row, index0) => {\n    output += drawRow(row, border);\n\n    if (!rowHeight) {\n      rowHeight = rowSpanIndex[realRowIndex];\n\n      realRowIndex++;\n    }\n\n    rowHeight--;\n\n    if (!singleLine && rowHeight === 0 && index0 !== rowCount - 1 && drawHorizontalLine(realRowIndex, rowCount)) {\n      output += drawBorderJoin(columnSizeIndex, border);\n    }\n  });\n\n  if (drawHorizontalLine(realRowIndex, rowCount)) {\n    output += drawBorderBottom(columnSizeIndex, border);\n  }\n\n  return output;\n};\n"], "file": "drawTable.js"}