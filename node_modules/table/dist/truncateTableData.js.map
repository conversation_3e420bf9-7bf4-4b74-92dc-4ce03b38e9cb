{"version": 3, "sources": ["../src/truncateTableData.js"], "names": ["rows", "config", "map", "cells", "content", "index", "length", "columns", "truncate"], "mappings": ";;;;;;;;;;;AAEA;;;;;;2BAMgBA,I,EAAMC,M,KAAW;AAC/B,SAAOD,IAAI,CAACE,GAAL,CAAUC,KAAD,IAAW;AACzB,WAAOA,KAAK,CAACD,GAAN,CAAU,CAACE,OAAD,EAAUC,KAAV,KAAoB;AACnC,aAAO,wBAAWD,OAAX,EAAoB;AACzBE,QAAAA,MAAM,EAAEL,MAAM,CAACM,OAAP,CAAeF,KAAf,EAAsBG;AADL,OAApB,CAAP;AAGD,KAJM,CAAP;AAKD,GANM,CAAP;AAOD,C", "sourcesContent": ["import _ from 'lodash';\n\n/**\n * @todo Make it work with ASCII content.\n * @param {table~row[]} rows\n * @param {Object} config\n * @returns {table~row[]}\n */\nexport default (rows, config) => {\n  return rows.map((cells) => {\n    return cells.map((content, index) => {\n      return _.truncate(content, {\n        length: config.columns[index].truncate\n      });\n    });\n  });\n};\n"], "file": "truncateTableData.js"}