{"version": 3, "sources": ["../src/getBorderCharacters.js"], "names": ["name", "topBody", "topJoin", "topLeft", "topRight", "bottomBody", "bottomJoin", "bottomLeft", "bottomRight", "bodyLeft", "bodyRight", "bodyJoin", "joinBody", "joinLeft", "joinRight", "joinJoin", "Error"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;;;;;;;;;;;AAmBA;;;;4BAIgBA,I,IAAS;AACvB,MAAIA,IAAI,KAAK,WAAb,EAA0B;AACxB,WAAO;AACLC,MAAAA,OAAO,EAAE,GADJ;AAELC,MAAAA,OAAO,EAAE,GAFJ;AAGLC,MAAAA,OAAO,EAAE,GAHJ;AAILC,MAAAA,QAAQ,EAAE,GAJL;AAMLC,MAAAA,UAAU,EAAE,GANP;AAOLC,MAAAA,UAAU,EAAE,GAPP;AAQLC,MAAAA,UAAU,EAAE,GARP;AASLC,MAAAA,WAAW,EAAE,GATR;AAWLC,MAAAA,QAAQ,EAAE,GAXL;AAYLC,MAAAA,SAAS,EAAE,GAZN;AAaLC,MAAAA,QAAQ,EAAE,GAbL;AAeLC,MAAAA,QAAQ,EAAE,GAfL;AAgBLC,MAAAA,QAAQ,EAAE,GAhBL;AAiBLC,MAAAA,SAAS,EAAE,GAjBN;AAkBLC,MAAAA,QAAQ,EAAE;AAlBL,KAAP;AAoBD;;AAED,MAAIf,IAAI,KAAK,MAAb,EAAqB;AACnB,WAAO;AACLC,MAAAA,OAAO,EAAE,GADJ;AAELC,MAAAA,OAAO,EAAE,GAFJ;AAGLC,MAAAA,OAAO,EAAE,GAHJ;AAILC,MAAAA,QAAQ,EAAE,GAJL;AAMLC,MAAAA,UAAU,EAAE,GANP;AAOLC,MAAAA,UAAU,EAAE,GAPP;AAQLC,MAAAA,UAAU,EAAE,GARP;AASLC,MAAAA,WAAW,EAAE,GATR;AAWLC,MAAAA,QAAQ,EAAE,GAXL;AAYLC,MAAAA,SAAS,EAAE,GAZN;AAaLC,MAAAA,QAAQ,EAAE,GAbL;AAeLC,MAAAA,QAAQ,EAAE,GAfL;AAgBLC,MAAAA,QAAQ,EAAE,GAhBL;AAiBLC,MAAAA,SAAS,EAAE,GAjBN;AAkBLC,MAAAA,QAAQ,EAAE;AAlBL,KAAP;AAoBD;;AAED,MAAIf,IAAI,KAAK,OAAb,EAAsB;AACpB,WAAO;AACLC,MAAAA,OAAO,EAAE,GADJ;AAELC,MAAAA,OAAO,EAAE,GAFJ;AAGLC,MAAAA,OAAO,EAAE,GAHJ;AAILC,MAAAA,QAAQ,EAAE,GAJL;AAMLC,MAAAA,UAAU,EAAE,GANP;AAOLC,MAAAA,UAAU,EAAE,GAPP;AAQLC,MAAAA,UAAU,EAAE,GARP;AASLC,MAAAA,WAAW,EAAE,GATR;AAWLC,MAAAA,QAAQ,EAAE,GAXL;AAYLC,MAAAA,SAAS,EAAE,GAZN;AAaLC,MAAAA,QAAQ,EAAE,GAbL;AAeLC,MAAAA,QAAQ,EAAE,GAfL;AAgBLC,MAAAA,QAAQ,EAAE,GAhBL;AAiBLC,MAAAA,SAAS,EAAE,GAjBN;AAkBLC,MAAAA,QAAQ,EAAE;AAlBL,KAAP;AAoBD;;AAED,MAAIf,IAAI,KAAK,MAAb,EAAqB;AACnB,WAAO;AACLC,MAAAA,OAAO,EAAE,EADJ;AAELC,MAAAA,OAAO,EAAE,EAFJ;AAGLC,MAAAA,OAAO,EAAE,EAHJ;AAILC,MAAAA,QAAQ,EAAE,EAJL;AAMLC,MAAAA,UAAU,EAAE,EANP;AAOLC,MAAAA,UAAU,EAAE,EAPP;AAQLC,MAAAA,UAAU,EAAE,EARP;AASLC,MAAAA,WAAW,EAAE,EATR;AAWLC,MAAAA,QAAQ,EAAE,EAXL;AAYLC,MAAAA,SAAS,EAAE,EAZN;AAaLC,MAAAA,QAAQ,EAAE,EAbL;AAeLC,MAAAA,QAAQ,EAAE,EAfL;AAgBLC,MAAAA,QAAQ,EAAE,EAhBL;AAiBLC,MAAAA,SAAS,EAAE,EAjBN;AAkBLC,MAAAA,QAAQ,EAAE;AAlBL,KAAP;AAoBD;;AAED,QAAM,IAAIC,KAAJ,CAAU,8BAA8BhB,IAA9B,GAAqC,IAA/C,CAAN;AACD,C", "sourcesContent": ["/* eslint-disable sort-keys */\n\n/**\n * @typedef border\n * @property {string} topBody\n * @property {string} topJoin\n * @property {string} topLeft\n * @property {string} topRight\n * @property {string} bottomBody\n * @property {string} bottomJoin\n * @property {string} bottomLeft\n * @property {string} bottomRight\n * @property {string} bodyLeft\n * @property {string} bodyRight\n * @property {string} bodyJoin\n * @property {string} joinBody\n * @property {string} joinLeft\n * @property {string} joinRight\n * @property {string} joinJoin\n */\n\n/**\n * @param {string} name\n * @returns {border}\n */\nexport default (name) => {\n  if (name === 'honeywell') {\n    return {\n      topBody: '═',\n      topJoin: '╤',\n      topLeft: '╔',\n      topRight: '╗',\n\n      bottomBody: '═',\n      bottomJoin: '╧',\n      bottomLeft: '╚',\n      bottomRight: '╝',\n\n      bodyLeft: '║',\n      bodyRight: '║',\n      bodyJoin: '│',\n\n      joinBody: '─',\n      joinLeft: '╟',\n      joinRight: '╢',\n      joinJoin: '┼'\n    };\n  }\n\n  if (name === 'norc') {\n    return {\n      topBody: '─',\n      topJoin: '┬',\n      topLeft: '┌',\n      topRight: '┐',\n\n      bottomBody: '─',\n      bottomJoin: '┴',\n      bottomLeft: '└',\n      bottomRight: '┘',\n\n      bodyLeft: '│',\n      bodyRight: '│',\n      bodyJoin: '│',\n\n      joinBody: '─',\n      joinLeft: '├',\n      joinRight: '┤',\n      joinJoin: '┼'\n    };\n  }\n\n  if (name === 'ramac') {\n    return {\n      topBody: '-',\n      topJoin: '+',\n      topLeft: '+',\n      topRight: '+',\n\n      bottomBody: '-',\n      bottomJoin: '+',\n      bottomLeft: '+',\n      bottomRight: '+',\n\n      bodyLeft: '|',\n      bodyRight: '|',\n      bodyJoin: '|',\n\n      joinBody: '-',\n      joinLeft: '|',\n      joinRight: '|',\n      joinJoin: '|'\n    };\n  }\n\n  if (name === 'void') {\n    return {\n      topBody: '',\n      topJoin: '',\n      topLeft: '',\n      topRight: '',\n\n      bottomBody: '',\n      bottomJoin: '',\n      bottomLeft: '',\n      bottomRight: '',\n\n      bodyLeft: '',\n      bodyRight: '',\n      bodyJoin: '',\n\n      joinBody: '',\n      joinLeft: '',\n      joinRight: '',\n      joinJoin: ''\n    };\n  }\n\n  throw new Error('Unknown border template \"' + name + '\".');\n};\n"], "file": "getBorderCharacters.js"}