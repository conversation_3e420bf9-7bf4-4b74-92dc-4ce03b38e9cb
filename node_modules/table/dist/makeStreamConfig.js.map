{"version": 3, "sources": ["../src/makeStreamConfig.js"], "names": ["makeBorder", "border", "Object", "assign", "makeColumns", "columnCount", "columns", "columnDefault", "index", "alignment", "paddingLeft", "paddingRight", "truncate", "Infinity", "wrapWord", "userConfig", "config", "width", "Error"], "mappings": ";;;;;;;;;;;;;AACA;;AACA;;;;AAEA;;;;;;AAMA,MAAMA,UAAU,GAAG,CAACC,MAAM,GAAG,EAAV,KAAiB;AAClC,SAAOC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,kCAAoB,WAApB,CAAlB,EAAoDF,MAApD,CAAP;AACD,CAFD;AAIA;;;;;;;;;;;AASA,MAAMG,WAAW,GAAG,CAACC,WAAD,EAAcC,OAAO,GAAG,EAAxB,EAA4BC,aAAa,GAAG,EAA5C,KAAmD;AACrE,uBAAQF,WAAR,EAAsBG,KAAD,IAAW;AAC9B,QAAI,2BAAcF,OAAO,CAACE,KAAD,CAArB,CAAJ,EAAmC;AACjCF,MAAAA,OAAO,CAACE,KAAD,CAAP,GAAiB,EAAjB;AACD;;AAEDF,IAAAA,OAAO,CAACE,KAAD,CAAP,GAAiBN,MAAM,CAACC,MAAP,CAAc;AAC7BM,MAAAA,SAAS,EAAE,MADkB;AAE7BC,MAAAA,WAAW,EAAE,CAFgB;AAG7BC,MAAAA,YAAY,EAAE,CAHe;AAI7BC,MAAAA,QAAQ,EAAEC,QAJmB;AAK7BC,MAAAA,QAAQ,EAAE;AALmB,KAAd,EAMdP,aANc,EAMCD,OAAO,CAACE,KAAD,CANR,CAAjB;AAOD,GAZD;AAcA,SAAOF,OAAP;AACD,CAhBD;AAkBA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;0BAOgBS,UAAU,GAAG,E,KAAO;AAClC,+BAAe,mBAAf,EAAoCA,UAApC;AAEA,QAAMC,MAAM,GAAG,yBAAYD,UAAZ,CAAf;;AAEA,MAAI,CAACC,MAAM,CAACT,aAAR,IAAyB,CAACS,MAAM,CAACT,aAAP,CAAqBU,KAAnD,EAA0D;AACxD,UAAM,IAAIC,KAAJ,CAAU,iEAAV,CAAN;AACD;;AAED,MAAI,CAACF,MAAM,CAACX,WAAZ,EAAyB;AACvB,UAAM,IAAIa,KAAJ,CAAU,kCAAV,CAAN;AACD;;AAEDF,EAAAA,MAAM,CAACf,MAAP,GAAgBD,UAAU,CAACgB,MAAM,CAACf,MAAR,CAA1B;AACAe,EAAAA,MAAM,CAACV,OAAP,GAAiBF,WAAW,CAACY,MAAM,CAACX,WAAR,EAAqBW,MAAM,CAACV,OAA5B,EAAqCU,MAAM,CAACT,aAA5C,CAA5B;AAEA,SAAOS,MAAP;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport getBorder<PERSON>haracters from './getBorderCharacters';\nimport validateConfig from './validateConfig';\n\n/**\n * Merges user provided border characters with the default border (\"honeywell\") characters.\n *\n * @param {Object} border\n * @returns {Object}\n */\nconst makeBorder = (border = {}) => {\n  return Object.assign({}, getBorderCharacters('honeywell'), border);\n};\n\n/**\n * Creates a configuration for every column using default\n * values for the missing configuration properties.\n *\n * @param {number} columnCount\n * @param {Object} columns\n * @param {Object} columnDefault\n * @returns {Object}\n */\nconst makeColumns = (columnCount, columns = {}, columnDefault = {}) => {\n  _.times(columnCount, (index) => {\n    if (_.isUndefined(columns[index])) {\n      columns[index] = {};\n    }\n\n    columns[index] = Object.assign({\n      alignment: 'left',\n      paddingLeft: 1,\n      paddingRight: 1,\n      truncate: Infinity,\n      wrapWord: false\n    }, columnDefault, columns[index]);\n  });\n\n  return columns;\n};\n\n/**\n * @typedef {Object} columnConfig\n * @property {string} alignment\n * @property {number} width\n * @property {number} truncate\n * @property {number} paddingLeft\n * @property {number} paddingRight\n */\n\n/**\n * @typedef {Object} streamConfig\n * @property {columnConfig} columnDefault\n * @property {Object} border\n * @property {columnConfig[]}\n * @property {number} columnCount Number of columns in the table (required).\n */\n\n/**\n * Makes a new configuration object out of the userConfig object\n * using default values for the missing configuration properties.\n *\n * @param {streamConfig} userConfig\n * @returns {Object}\n */\nexport default (userConfig = {}) => {\n  validateConfig('streamConfig.json', userConfig);\n\n  const config = _.cloneDeep(userConfig);\n\n  if (!config.columnDefault || !config.columnDefault.width) {\n    throw new Error('Must provide config.columnDefault.width when creating a stream.');\n  }\n\n  if (!config.columnCount) {\n    throw new Error('Must provide config.columnCount.');\n  }\n\n  config.border = makeBorder(config.border);\n  config.columns = makeColumns(config.columnCount, config.columns, config.columnDefault);\n\n  return config;\n};\n"], "file": "makeStreamConfig.js"}