{"version": 3, "sources": ["../src/drawRow.js"], "names": ["columns", "border", "bodyLeft", "join", "bodyJoin", "bodyRight"], "mappings": ";;;;;;;AAAA;;;;;;;AAOA;;;;;iBAKgBA,O,EAASC,M,KAAW;AAClC,SAAOA,MAAM,CAACC,QAAP,GAAkBF,OAAO,CAACG,IAAR,CAAaF,MAAM,CAACG,QAApB,CAAlB,GAAkDH,MAAM,CAACI,SAAzD,GAAqE,IAA5E;AACD,C", "sourcesContent": ["/**\n * @typedef {Object} drawRow~border\n * @property {string} bodyLeft\n * @property {string} bodyRight\n * @property {string} bodyJoin\n */\n\n/**\n * @param {number[]} columns\n * @param {drawRow~border} border\n * @returns {string}\n */\nexport default (columns, border) => {\n  return border.bodyLeft + columns.join(border.bodyJoin) + border.bodyRight + '\\n';\n};\n"], "file": "drawRow.js"}