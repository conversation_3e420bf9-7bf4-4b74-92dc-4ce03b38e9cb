{"version": 3, "sources": ["../src/wrapCell.js"], "names": ["cellValue", "columnWidth", "useWrapWord", "cellLines", "split", "lineNr", "length", "lineChunks", "splice"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;;;;;;;;;;;kBAWgBA,S,EAAWC,W,EAAaC,W,KAAgB;AACtD;AACA,QAAMC,SAAS,GAAGH,SAAS,CAACI,KAAV,CAAgB,IAAhB,CAAlB,CAFsD,CAItD;;AACA,OAAK,IAAIC,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAGF,SAAS,CAACG,MAAxC,GAAiD;AAC/C,QAAIC,UAAJ;;AAEA,QAAIL,WAAJ,EAAiB;AACfK,MAAAA,UAAU,GAAG,uBAASJ,SAAS,CAACE,MAAD,CAAlB,EAA4BJ,WAA5B,CAAb;AACD,KAFD,MAEO;AACLM,MAAAA,UAAU,GAAG,yBAAWJ,SAAS,CAACE,MAAD,CAApB,EAA8BJ,WAA9B,CAAb;AACD,KAP8C,CAS/C;;;AACAE,IAAAA,SAAS,CAACK,MAAV,CAAiBH,MAAjB,EAAyB,CAAzB,EAA4B,GAAGE,UAA/B;AACAF,IAAAA,MAAM,IAAIE,UAAU,CAACD,MAArB;AACD;;AAED,SAAOH,SAAP;AACD,C", "sourcesContent": ["import wrapString from './wrapString';\nimport wrapWord from './wrapWord';\n\n/**\n * Wrap a single cell value into a list of lines\n *\n * Always wraps on newlines, for the remainder uses either word or string wrapping\n * depending on user configuration.\n *\n * @param {string} cellValue\n * @param {number} columnWidth\n * @param {boolean} useWrapWord\n * @returns {Array}\n */\nexport default (cellValue, columnWidth, useWrapWord) => {\n  // First split on literal newlines\n  const cellLines = cellValue.split('\\n');\n\n  // Then iterate over the list and word-wrap every remaining line if necessary.\n  for (let lineNr = 0; lineNr < cellLines.length;) {\n    let lineChunks;\n\n    if (useWrapWord) {\n      lineChunks = wrapWord(cellLines[lineNr], columnWidth);\n    } else {\n      lineChunks = wrapString(cellLines[lineNr], columnWidth);\n    }\n\n    // Replace our original array element with whatever the wrapping returned\n    cellLines.splice(lineNr, 1, ...lineChunks);\n    lineNr += lineChunks.length;\n  }\n\n  return cellLines;\n};\n"], "file": "wrapCell.js"}