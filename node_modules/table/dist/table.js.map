{"version": 3, "sources": ["../src/table.js"], "names": ["data", "userConfig", "rows", "config", "rowHeightIndex", "cellWidthIndex", "border", "drawHorizontalLine", "singleLine"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAEA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;eAOgBA,I,EAAMC,UAAU,GAAG,E,KAAO;AACxC,MAAIC,IAAJ;AAEA,kCAAkBF,IAAlB;AAEAE,EAAAA,IAAI,GAAG,iCAAmBF,IAAnB,CAAP;AAEA,QAAMG,MAAM,GAAG,yBAAWD,IAAX,EAAiBD,UAAjB,CAAf;AAEAC,EAAAA,IAAI,GAAG,gCAAkBF,IAAlB,EAAwBG,MAAxB,CAAP;AAEA,QAAMC,cAAc,GAAG,sCAAwBF,IAAxB,EAA8BC,MAA9B,CAAvB;AAEAD,EAAAA,IAAI,GAAG,yCAA2BA,IAA3B,EAAiCE,cAAjC,EAAiDD,MAAjD,CAAP;AACAD,EAAAA,IAAI,GAAG,6BAAeA,IAAf,EAAqBC,MAArB,CAAP;AACAD,EAAAA,IAAI,GAAG,2BAAaA,IAAb,EAAmBC,MAAnB,CAAP;AAEA,QAAME,cAAc,GAAG,sCAAwBH,IAAI,CAAC,CAAD,CAA5B,CAAvB;AAEA,SAAO,wBAAUA,IAAV,EAAgBC,MAAM,CAACG,MAAvB,EAA+BD,cAA/B,EAA+CD,cAA/C,EAA+DD,MAAM,CAACI,kBAAtE,EAA0FJ,MAAM,CAACK,UAAjG,CAAP;AACD,C", "sourcesContent": ["import drawTable from './drawTable';\nimport calculateCellWidthIndex from './calculateCellWidthIndex';\nimport makeConfig from './makeConfig';\nimport calculateRowHeightIndex from './calculateRowHeightIndex';\nimport mapDataUsingRowHeightIndex from './mapDataUsingRowHeightIndex';\nimport alignTableData from './alignTableData';\nimport padTableData from './padTableData';\nimport validateTableData from './validateTableData';\nimport stringifyTableData from './stringifyTableData';\nimport truncateTableData from './truncateTableData';\n\n/**\n * @typedef {string} table~cell\n */\n\n/**\n * @typedef {table~cell[]} table~row\n */\n\n/**\n * @typedef {Object} table~columns\n * @property {string} alignment Cell content alignment (enum: left, center, right) (default: left).\n * @property {number} width Column width (default: auto).\n * @property {number} truncate Number of characters are which the content will be truncated (default: Infinity).\n * @property {boolean} wrapWord When true the text is broken at the nearest space or one of the special characters\n * @property {number} paddingLeft Cell content padding width left (default: 1).\n * @property {number} paddingRight Cell content padding width right (default: 1).\n */\n\n/**\n * @typedef {Object} table~border\n * @property {string} topBody\n * @property {string} topJoin\n * @property {string} topLeft\n * @property {string} topRight\n * @property {string} bottomBody\n * @property {string} bottomJoin\n * @property {string} bottomLeft\n * @property {string} bottomRight\n * @property {string} bodyLeft\n * @property {string} bodyRight\n * @property {string} bodyJoin\n * @property {string} joinBody\n * @property {string} joinLeft\n * @property {string} joinRight\n * @property {string} joinJoin\n */\n\n/**\n * Used to tell whether to draw a horizontal line.\n * This callback is called for each non-content line of the table.\n * The default behavior is to always return true.\n *\n * @typedef {Function} drawHorizontalLine\n * @param {number} index\n * @param {number} size\n * @returns {boolean}\n */\n\n/**\n * @typedef {Object} table~config\n * @property {table~border} border\n * @property {table~columns[]} columns Column specific configuration.\n * @property {table~columns} columnDefault Default values for all columns. Column specific settings overwrite the default values.\n * @property {table~drawHorizontalLine} drawHorizontalLine\n * @property {table~singleLine} singleLine Horizontal lines inside the table are not drawn.\n */\n\n/**\n * Generates a text table.\n *\n * @param {table~row[]} data\n * @param {table~config} userConfig\n * @returns {string}\n */\nexport default (data, userConfig = {}) => {\n  let rows;\n\n  validateTableData(data);\n\n  rows = stringifyTableData(data);\n\n  const config = makeConfig(rows, userConfig);\n\n  rows = truncateTableData(data, config);\n\n  const rowHeightIndex = calculateRowHeightIndex(rows, config);\n\n  rows = mapDataUsingRowHeightIndex(rows, rowHeightIndex, config);\n  rows = alignTableData(rows, config);\n  rows = padTableData(rows, config);\n\n  const cellWidthIndex = calculateCellWidthIndex(rows[0]);\n\n  return drawTable(rows, config.border, cellWidthIndex, rowHeightIndex, config.drawHorizontalLine, config.singleLine);\n};\n"], "file": "table.js"}