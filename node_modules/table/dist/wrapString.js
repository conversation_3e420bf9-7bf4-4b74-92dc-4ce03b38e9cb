"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _sliceAnsi = _interopRequireDefault(require("slice-ansi"));

var _stringWidth = _interopRequireDefault(require("string-width"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Creates an array of strings split into groups the length of size.
 * This function works with strings that contain ASCII characters.
 *
 * wrapText is different from would-be "chunk" implementation
 * in that whitespace characters that occur on a chunk size limit are trimmed.
 *
 * @param {string} subject
 * @param {number} size
 * @returns {Array}
 */
const wrapString = (subject, size) => {
  let subjectSlice;
  subjectSlice = subject;
  const chunks = [];

  do {
    chunks.push((0, _sliceAnsi.default)(subjectSlice, 0, size));
    subjectSlice = (0, _sliceAnsi.default)(subjectSlice, size).trim();
  } while ((0, _stringWidth.default)(subjectSlice));

  return chunks;
};

var _default = wrapString;
exports.default = _default;
//# sourceMappingURL=wrapString.js.map