{"version": 3, "sources": ["../src/validateConfig.js"], "names": ["validate", "validateConfig", "validateStreamConfig", "schemaId", "config", "errors", "map", "error", "dataPath", "message", "params", "schemaPath", "console", "log", "Error"], "mappings": ";;;;;;;AACA;;AAEA;;;;AAHA;AAEA;AAGA,MAAMA,QAAQ,GAAG;AACf,iBAAeC,uBADA;AAEf,uBAAqBC;AAFN,CAAjB;AAKA;;;;;;yBAKgBC,Q,EAAUC,MAAM,GAAG,E,KAAO;AACxC,MAAI,CAACJ,QAAQ,CAACG,QAAD,CAAR,CAAmBC,MAAnB,CAAL,EAAiC;AAC/B,UAAMC,MAAM,GAAGL,QAAQ,CAACG,QAAD,CAAR,CAAmBE,MAAnB,CAA0BC,GAA1B,CAA+BC,KAAD,IAAW;AACtD,aAAO;AACLC,QAAAA,QAAQ,EAAED,KAAK,CAACC,QADX;AAELC,QAAAA,OAAO,EAAEF,KAAK,CAACE,OAFV;AAGLC,QAAAA,MAAM,EAAEH,KAAK,CAACG,MAHT;AAILC,QAAAA,UAAU,EAAEJ,KAAK,CAACI;AAJb,OAAP;AAMD,KAPc,CAAf;AASA;;AACAC,IAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBT,MAAtB;AACAQ,IAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBR,MAAtB;AACA;;AAEA,UAAM,IAAIS,KAAJ,CAAU,iBAAV,CAAN;AACD;AACF,C", "sourcesContent": ["// eslint-disable-next-line import/default\nimport validateConfig from '../dist/validateConfig';\n// eslint-disable-next-line import/default\nimport validateStreamConfig from '../dist/validateStreamConfig';\n\nconst validate = {\n  'config.json': validateConfig,\n  'streamConfig.json': validateStreamConfig\n};\n\n/**\n * @param {string} schemaId\n * @param {formatData~config} config\n * @returns {undefined}\n */\nexport default (schemaId, config = {}) => {\n  if (!validate[schemaId](config)) {\n    const errors = validate[schemaId].errors.map((error) => {\n      return {\n        dataPath: error.dataPath,\n        message: error.message,\n        params: error.params,\n        schemaPath: error.schemaPath\n      };\n    });\n\n    /* eslint-disable no-console */\n    console.log('config', config);\n    console.log('errors', errors);\n    /* eslint-enable no-console */\n\n    throw new Error('Invalid config.');\n  }\n};\n"], "file": "validateConfig.js"}