{"version": 3, "sources": ["../src/calculateCellHeight.js"], "names": ["value", "columnWidth", "useWrapWord", "TypeError", "Number", "isInteger", "Error", "length"], "mappings": ";;;;;;;;;AACA;;;;AAEA;;;;;;6BAMgBA,K,EAAOC,W,EAAaC,WAAW,GAAG,K,KAAU;AAC1D,MAAI,CAAC,wBAAWF,KAAX,CAAL,EAAwB;AACtB,UAAM,IAAIG,SAAJ,CAAc,yBAAd,CAAN;AACD;;AAED,MAAI,CAACC,MAAM,CAACC,SAAP,CAAiBJ,WAAjB,CAAL,EAAoC;AAClC,UAAM,IAAIE,SAAJ,CAAc,kCAAd,CAAN;AACD;;AAED,MAAIF,WAAW,GAAG,CAAlB,EAAqB;AACnB,UAAM,IAAIK,KAAJ,CAAU,sCAAV,CAAN;AACD;;AAED,SAAO,uBAASN,KAAT,EAAgBC,WAAhB,EAA6BC,WAA7B,EAA0CK,MAAjD;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport wrapCell from './wrapCell';\n\n/**\n * @param {string} value\n * @param {number} columnWidth\n * @param {boolean} useWrapWord\n * @returns {number}\n */\nexport default (value, columnWidth, useWrapWord = false) => {\n  if (!_.isString(value)) {\n    throw new TypeError('Value must be a string.');\n  }\n\n  if (!Number.isInteger(columnWidth)) {\n    throw new TypeError('Column width must be an integer.');\n  }\n\n  if (columnWidth < 1) {\n    throw new Error('Column width must be greater than 0.');\n  }\n\n  return wrapCell(value, columnWidth, useWrapWord).length;\n};\n"], "file": "calculateCellHeight.js"}