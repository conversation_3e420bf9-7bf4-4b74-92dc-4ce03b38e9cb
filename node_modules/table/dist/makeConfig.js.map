{"version": 3, "sources": ["../src/makeConfig.js"], "names": ["makeBorder", "border", "Object", "assign", "makeColumns", "rows", "columns", "columnDefault", "maximumColumnWidthIndex", "length", "index", "alignment", "paddingLeft", "paddingRight", "truncate", "Infinity", "width", "wrapWord", "userConfig", "config", "drawHorizontalLine", "singleLine", "undefined"], "mappings": ";;;;;;;;;;;;;AACA;;AACA;;AACA;;;;AAEA;;;;;;AAMA,MAAMA,UAAU,GAAG,CAACC,MAAM,GAAG,EAAV,KAAiB;AAClC,SAAOC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,kCAAoB,WAApB,CAAlB,EAAoDF,MAApD,CAAP;AACD,CAFD;AAIA;;;;;;;;;;;AASA,MAAMG,WAAW,GAAG,CAACC,IAAD,EAAOC,OAAO,GAAG,EAAjB,EAAqBC,aAAa,GAAG,EAArC,KAA4C;AAC9D,QAAMC,uBAAuB,GAAG,+CAAiCH,IAAjC,CAAhC;AAEA,uBAAQA,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAAhB,EAAyBC,KAAD,IAAW;AACjC,QAAI,2BAAcJ,OAAO,CAACI,KAAD,CAArB,CAAJ,EAAmC;AACjCJ,MAAAA,OAAO,CAACI,KAAD,CAAP,GAAiB,EAAjB;AACD;;AAEDJ,IAAAA,OAAO,CAACI,KAAD,CAAP,GAAiBR,MAAM,CAACC,MAAP,CAAc;AAC7BQ,MAAAA,SAAS,EAAE,MADkB;AAE7BC,MAAAA,WAAW,EAAE,CAFgB;AAG7BC,MAAAA,YAAY,EAAE,CAHe;AAI7BC,MAAAA,QAAQ,EAAEC,QAJmB;AAK7BC,MAAAA,KAAK,EAAER,uBAAuB,CAACE,KAAD,CALD;AAM7BO,MAAAA,QAAQ,EAAE;AANmB,KAAd,EAOdV,aAPc,EAOCD,OAAO,CAACI,KAAD,CAPR,CAAjB;AAQD,GAbD;AAeA,SAAOJ,OAAP;AACD,CAnBD;AAqBA;;;;;;;;;;oBAQgBD,I,EAAMa,UAAU,GAAG,E,KAAO;AACxC,+BAAe,aAAf,EAA8BA,UAA9B;AAEA,QAAMC,MAAM,GAAG,yBAAYD,UAAZ,CAAf;AAEAC,EAAAA,MAAM,CAAClB,MAAP,GAAgBD,UAAU,CAACmB,MAAM,CAAClB,MAAR,CAA1B;AACAkB,EAAAA,MAAM,CAACb,OAAP,GAAiBF,WAAW,CAACC,IAAD,EAAOc,MAAM,CAACb,OAAd,EAAuBa,MAAM,CAACZ,aAA9B,CAA5B;;AAEA,MAAI,CAACY,MAAM,CAACC,kBAAZ,EAAgC;AAC9B;;;AAGAD,IAAAA,MAAM,CAACC,kBAAP,GAA4B,MAAM;AAChC,aAAO,IAAP;AACD,KAFD;AAGD;;AAED,MAAID,MAAM,CAACE,UAAP,KAAsBC,SAA1B,EAAqC;AACnCH,IAAAA,MAAM,CAACE,UAAP,GAAoB,KAApB;AACD;;AAED,SAAOF,MAAP;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport getBorderCharacters from './getBorderCharacters';\nimport validateConfig from './validateConfig';\nimport calculateMaximumColumnWidthIndex from './calculateMaximumColumnWidthIndex';\n\n/**\n * Merges user provided border characters with the default border (\"honeywell\") characters.\n *\n * @param {Object} border\n * @returns {Object}\n */\nconst makeBorder = (border = {}) => {\n  return Object.assign({}, getBorderCharacters('honeywell'), border);\n};\n\n/**\n * Creates a configuration for every column using default\n * values for the missing configuration properties.\n *\n * @param {Array[]} rows\n * @param {Object} columns\n * @param {Object} columnDefault\n * @returns {Object}\n */\nconst makeColumns = (rows, columns = {}, columnDefault = {}) => {\n  const maximumColumnWidthIndex = calculateMaximumColumnWidthIndex(rows);\n\n  _.times(rows[0].length, (index) => {\n    if (_.isUndefined(columns[index])) {\n      columns[index] = {};\n    }\n\n    columns[index] = Object.assign({\n      alignment: 'left',\n      paddingLeft: 1,\n      paddingRight: 1,\n      truncate: Infinity,\n      width: maximumColumnWidthIndex[index],\n      wrapWord: false\n    }, columnDefault, columns[index]);\n  });\n\n  return columns;\n};\n\n/**\n * Makes a new configuration object out of the userConfig object\n * using default values for the missing configuration properties.\n *\n * @param {Array[]} rows\n * @param {Object} userConfig\n * @returns {Object}\n */\nexport default (rows, userConfig = {}) => {\n  validateConfig('config.json', userConfig);\n\n  const config = _.cloneDeep(userConfig);\n\n  config.border = makeBorder(config.border);\n  config.columns = makeColumns(rows, config.columns, config.columnDefault);\n\n  if (!config.drawHorizontalLine) {\n    /**\n         * @returns {boolean}\n         */\n    config.drawHorizontalLine = () => {\n      return true;\n    };\n  }\n\n  if (config.singleLine === undefined) {\n    config.singleLine = false;\n  }\n\n  return config;\n};\n"], "file": "makeConfig.js"}