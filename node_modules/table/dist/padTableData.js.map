{"version": 3, "sources": ["../src/padTableData.js"], "names": ["rows", "config", "map", "cells", "value", "index1", "column", "columns", "repeat", "paddingLeft", "paddingRight"], "mappings": ";;;;;;;AAAA;;;;;sBAKgBA,I,EAAMC,M,KAAW;AAC/B,SAAOD,IAAI,CAACE,GAAL,CAAUC,KAAD,IAAW;AACzB,WAAOA,KAAK,CAACD,GAAN,CAAU,CAACE,KAAD,EAAQC,MAAR,KAAmB;AAClC,YAAMC,MAAM,GAAGL,MAAM,CAACM,OAAP,CAAeF,MAAf,CAAf;AAEA,aAAO,IAAIG,MAAJ,CAAWF,MAAM,CAACG,WAAlB,IAAiCL,KAAjC,GAAyC,IAAII,MAAJ,CAAWF,MAAM,CAACI,YAAlB,CAAhD;AACD,KAJM,CAAP;AAKD,GANM,CAAP;AAOD,C", "sourcesContent": ["/**\n * @param {table~row[]} rows\n * @param {Object} config\n * @returns {table~row[]}\n */\nexport default (rows, config) => {\n  return rows.map((cells) => {\n    return cells.map((value, index1) => {\n      const column = config.columns[index1];\n\n      return ' '.repeat(column.paddingLeft) + value + ' '.repeat(column.paddingRight);\n    });\n  });\n};\n"], "file": "padTableData.js"}