{"version": 3, "sources": ["../src/alignString.js"], "names": ["alignments", "alignLeft", "subject", "width", "repeat", "alignRight", "alignCenter", "halfWidth", "Math", "floor", "containerWidth", "alignment", "TypeError", "subject<PERSON><PERSON><PERSON>", "Error", "includes", "availableWidth"], "mappings": ";;;;;;;;;;;AACA;;;;AAEA,MAAMA,UAAU,GAAG,CACjB,MADiB,EAEjB,OAFiB,EAGjB,QAHiB,CAAnB;AAMA;;;;;;AAKA,MAAMC,SAAS,GAAG,CAACC,OAAD,EAAUC,KAAV,KAAoB;AACpC,SAAOD,OAAO,GAAG,IAAIE,MAAJ,CAAWD,KAAX,CAAjB;AACD,CAFD;AAIA;;;;;;;AAKA,MAAME,UAAU,GAAG,CAACH,OAAD,EAAUC,KAAV,KAAoB;AACrC,SAAO,IAAIC,MAAJ,CAAWD,KAAX,IAAoBD,OAA3B;AACD,CAFD;AAIA;;;;;;;AAKA,MAAMI,WAAW,GAAG,CAACJ,OAAD,EAAUC,KAAV,KAAoB;AACtC,MAAII,SAAJ;AAEAA,EAAAA,SAAS,GAAGJ,KAAK,GAAG,CAApB;;AAEA,MAAII,SAAS,GAAG,CAAZ,KAAkB,CAAtB,EAAyB;AACvB,WAAO,IAAIH,MAAJ,CAAWG,SAAX,IAAwBL,OAAxB,GAAkC,IAAIE,MAAJ,CAAWG,SAAX,CAAzC;AACD,GAFD,MAEO;AACLA,IAAAA,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWF,SAAX,CAAZ;AAEA,WAAO,IAAIH,MAAJ,CAAWG,SAAX,IAAwBL,OAAxB,GAAkC,IAAIE,MAAJ,CAAWG,SAAS,GAAG,CAAvB,CAAzC;AACD;AACF,CAZD;AAcA;;;;;;;;;;;qBASgBL,O,EAASQ,c,EAAgBC,S,KAAc;AACrD,MAAI,CAAC,wBAAWT,OAAX,CAAL,EAA0B;AACxB,UAAM,IAAIU,SAAJ,CAAc,2CAAd,CAAN;AACD;;AAED,MAAI,CAAC,wBAAWF,cAAX,CAAL,EAAiC;AAC/B,UAAM,IAAIE,SAAJ,CAAc,mDAAd,CAAN;AACD;;AAED,QAAMC,YAAY,GAAG,0BAAYX,OAAZ,CAArB;;AAEA,MAAIW,YAAY,GAAGH,cAAnB,EAAmC;AACjC;AAEA,UAAM,IAAII,KAAJ,CAAU,2EAAV,CAAN;AACD;;AAED,MAAI,CAAC,wBAAWH,SAAX,CAAL,EAA4B;AAC1B,UAAM,IAAIC,SAAJ,CAAc,6CAAd,CAAN;AACD;;AAED,MAAI,CAACZ,UAAU,CAACe,QAAX,CAAoBJ,SAApB,CAAL,EAAqC;AACnC,UAAM,IAAIG,KAAJ,CAAU,4FAAV,CAAN;AACD;;AAED,MAAID,YAAY,KAAK,CAArB,EAAwB;AACtB,WAAO,IAAIT,MAAJ,CAAWM,cAAX,CAAP;AACD;;AAED,QAAMM,cAAc,GAAGN,cAAc,GAAGG,YAAxC;;AAEA,MAAIF,SAAS,KAAK,MAAlB,EAA0B;AACxB,WAAOV,SAAS,CAACC,OAAD,EAAUc,cAAV,CAAhB;AACD;;AAED,MAAIL,SAAS,KAAK,OAAlB,EAA2B;AACzB,WAAON,UAAU,CAACH,OAAD,EAAUc,cAAV,CAAjB;AACD;;AAED,SAAOV,WAAW,CAACJ,OAAD,EAAUc,cAAV,CAAlB;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport stringWidth from 'string-width';\n\nconst alignments = [\n  'left',\n  'right',\n  'center'\n];\n\n/**\n * @param {string} subject\n * @param {number} width\n * @returns {string}\n */\nconst alignLeft = (subject, width) => {\n  return subject + ' '.repeat(width);\n};\n\n/**\n * @param {string} subject\n * @param {number} width\n * @returns {string}\n */\nconst alignRight = (subject, width) => {\n  return ' '.repeat(width) + subject;\n};\n\n/**\n * @param {string} subject\n * @param {number} width\n * @returns {string}\n */\nconst alignCenter = (subject, width) => {\n  let halfWidth;\n\n  halfWidth = width / 2;\n\n  if (halfWidth % 2 === 0) {\n    return ' '.repeat(halfWidth) + subject + ' '.repeat(halfWidth);\n  } else {\n    halfWidth = Math.floor(halfWidth);\n\n    return ' '.repeat(halfWidth) + subject + ' '.repeat(halfWidth + 1);\n  }\n};\n\n/**\n * Pads a string to the left and/or right to position the subject\n * text in a desired alignment within a container.\n *\n * @param {string} subject\n * @param {number} containerWidth\n * @param {string} alignment One of the valid options (left, right, center).\n * @returns {string}\n */\nexport default (subject, containerWidth, alignment) => {\n  if (!_.isString(subject)) {\n    throw new TypeError('Subject parameter value must be a string.');\n  }\n\n  if (!_.isNumber(containerWidth)) {\n    throw new TypeError('Container width parameter value must be a number.');\n  }\n\n  const subjectWidth = stringWidth(subject);\n\n  if (subjectWidth > containerWidth) {\n    // console.log('subjectWidth', subjectWidth, 'containerWidth', containerWidth, 'subject', subject);\n\n    throw new Error('Subject parameter value width cannot be greater than the container width.');\n  }\n\n  if (!_.isString(alignment)) {\n    throw new TypeError('Alignment parameter value must be a string.');\n  }\n\n  if (!alignments.includes(alignment)) {\n    throw new Error('Alignment parameter value must be a known alignment parameter value (left, right, center).');\n  }\n\n  if (subjectWidth === 0) {\n    return ' '.repeat(containerWidth);\n  }\n\n  const availableWidth = containerWidth - subjectWidth;\n\n  if (alignment === 'left') {\n    return alignLeft(subject, availableWidth);\n  }\n\n  if (alignment === 'right') {\n    return alignRight(subject, availableWidth);\n  }\n\n  return alignCenter(subject, availableWidth);\n};\n"], "file": "alignString.js"}