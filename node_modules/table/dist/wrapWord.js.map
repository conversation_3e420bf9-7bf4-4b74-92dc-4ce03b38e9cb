{"version": 3, "sources": ["../src/wrapWord.js"], "names": ["input", "size", "subject", "chunks", "re", "RegExp", "chunk", "match", "trim", "push"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;;;;;kBAKgBA,K,EAAOC,I,KAAS;AAC9B,MAAIC,OAAJ;AAEAA,EAAAA,OAAO,GAAGF,KAAV;AAEA,QAAMG,MAAM,GAAG,EAAf,CAL8B,CAO9B;;AACA,QAAMC,EAAE,GAAG,IAAIC,MAAJ,CAAW,WAAWJ,IAAX,GAAkB,mBAAlB,IAAyCA,IAAI,GAAG,CAAhD,IAAqD,wBAAhE,CAAX;;AAEA,KAAG;AACD,QAAIK,KAAJ;AAEAA,IAAAA,KAAK,GAAGJ,OAAO,CAACK,KAAR,CAAcH,EAAd,CAAR;;AAEA,QAAIE,KAAJ,EAAW;AACTA,MAAAA,KAAK,GAAGA,KAAK,CAAC,CAAD,CAAb;AAEAJ,MAAAA,OAAO,GAAG,wBAAMA,OAAN,EAAe,0BAAYI,KAAZ,CAAf,CAAV;AAEAA,MAAAA,KAAK,GAAGA,KAAK,CAACE,IAAN,EAAR;AACD,KAND,MAMO;AACLF,MAAAA,KAAK,GAAG,wBAAMJ,OAAN,EAAe,CAAf,EAAkBD,IAAlB,CAAR;AACAC,MAAAA,OAAO,GAAG,wBAAMA,OAAN,EAAeD,IAAf,CAAV;AACD;;AAEDE,IAAAA,MAAM,CAACM,IAAP,CAAYH,KAAZ;AACD,GAjBD,QAiBS,0BAAYJ,OAAZ,CAjBT;;AAmBA,SAAOC,MAAP;AACD,C", "sourcesContent": ["import slice from 'slice-ansi';\nimport stringWidth from 'string-width';\n\n/**\n * @param {string} input\n * @param {number} size\n * @returns {Array}\n */\nexport default (input, size) => {\n  let subject;\n\n  subject = input;\n\n  const chunks = [];\n\n  // https://regex101.com/r/gY5kZ1/1\n  const re = new RegExp('(^.{1,' + size + '}(\\\\s+|$))|(^.{1,' + (size - 1) + '}(\\\\\\\\|/|_|\\\\.|,|;|-))');\n\n  do {\n    let chunk;\n\n    chunk = subject.match(re);\n\n    if (chunk) {\n      chunk = chunk[0];\n\n      subject = slice(subject, stringWidth(chunk));\n\n      chunk = chunk.trim();\n    } else {\n      chunk = slice(subject, 0, size);\n      subject = slice(subject, size);\n    }\n\n    chunks.push(chunk);\n  } while (stringWidth(subject));\n\n  return chunks;\n};\n"], "file": "wrapWord.js"}