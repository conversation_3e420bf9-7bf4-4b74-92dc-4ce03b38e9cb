{"version": 3, "sources": ["../src/calculateRowHeightIndex.js"], "names": ["rows", "config", "tableWidth", "length", "rowSpanIndex", "for<PERSON>ach", "cells", "cellHeightIndex", "Array", "fill", "value", "index1", "columns", "width", "TypeError", "wrapWord", "push"], "mappings": ";;;;;;;;;;;;;AACA;;;;AAEA;;;;;;;iCAOgBA,I,EAAMC,M,KAAW;AAC/B,QAAMC,UAAU,GAAGF,IAAI,CAAC,CAAD,CAAJ,CAAQG,MAA3B;AAEA,QAAMC,YAAY,GAAG,EAArB;AAEAJ,EAAAA,IAAI,CAACK,OAAL,CAAcC,KAAD,IAAW;AACtB,UAAMC,eAAe,GAAG,IAAIC,KAAJ,CAAUN,UAAV,EAAsBO,IAAtB,CAA2B,CAA3B,CAAxB;AAEAH,IAAAA,KAAK,CAACD,OAAN,CAAc,CAACK,KAAD,EAAQC,MAAR,KAAmB;AAC/B,UAAI,CAAC,wBAAWV,MAAM,CAACW,OAAP,CAAeD,MAAf,EAAuBE,KAAlC,CAAL,EAA+C;AAC7C,cAAM,IAAIC,SAAJ,CAAc,uCAAd,CAAN;AACD;;AAED,UAAI,CAAC,yBAAYb,MAAM,CAACW,OAAP,CAAeD,MAAf,EAAuBI,QAAnC,CAAL,EAAmD;AACjD,cAAM,IAAID,SAAJ,CAAc,2CAAd,CAAN;AACD;;AAEDP,MAAAA,eAAe,CAACI,MAAD,CAAf,GAA0B,kCAAoBD,KAApB,EAA2BT,MAAM,CAACW,OAAP,CAAeD,MAAf,EAAuBE,KAAlD,EAAyDZ,MAAM,CAACW,OAAP,CAAeD,MAAf,EAAuBI,QAAhF,CAA1B;AACD,KAVD;AAYAX,IAAAA,YAAY,CAACY,IAAb,CAAkB,mBAAMT,eAAN,CAAlB;AACD,GAhBD;AAkBA,SAAOH,YAAP;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport calculateCellHeight from './calculateCellHeight';\n\n/**\n * Calculates the vertical row span index.\n *\n * @param {Array[]} rows\n * @param {Object} config\n * @returns {number[]}\n */\nexport default (rows, config) => {\n  const tableWidth = rows[0].length;\n\n  const rowSpanIndex = [];\n\n  rows.forEach((cells) => {\n    const cellHeightIndex = new Array(tableWidth).fill(1);\n\n    cells.forEach((value, index1) => {\n      if (!_.isNumber(config.columns[index1].width)) {\n        throw new TypeError('column[index].width must be a number.');\n      }\n\n      if (!_.isBoolean(config.columns[index1].wrapWord)) {\n        throw new TypeError('column[index].wrapWord must be a boolean.');\n      }\n\n      cellHeightIndex[index1] = calculateCellHeight(value, config.columns[index1].width, config.columns[index1].wrapWord);\n    });\n\n    rowSpanIndex.push(_.max(cellHeightIndex));\n  });\n\n  return rowSpanIndex;\n};\n"], "file": "calculateRowHeightIndex.js"}