{"version": 3, "sources": ["../src/createStream.js"], "names": ["prepareData", "data", "config", "rows", "rowHeightIndex", "create", "row", "columnWidthIndex", "body", "map", "literalRow", "border", "join", "output", "process", "stdout", "write", "append", "bottom", "userConfig", "columns", "column", "width", "paddingLeft", "paddingRight", "empty", "length", "columnCount", "Error"], "mappings": ";;;;;;;;;;;;;AACA;;AACA;;AACA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAEA;;;;;AAKA,MAAMA,WAAW,GAAG,CAACC,IAAD,EAAOC,MAAP,KAAkB;AACpC,MAAIC,IAAJ;AAEAA,EAAAA,IAAI,GAAG,iCAAmBF,IAAnB,CAAP;AAEAE,EAAAA,IAAI,GAAG,gCAAkBF,IAAlB,EAAwBC,MAAxB,CAAP;AAEA,QAAME,cAAc,GAAG,sCAAwBD,IAAxB,EAA8BD,MAA9B,CAAvB;AAEAC,EAAAA,IAAI,GAAG,yCAA2BA,IAA3B,EAAiCC,cAAjC,EAAiDF,MAAjD,CAAP;AACAC,EAAAA,IAAI,GAAG,6BAAeA,IAAf,EAAqBD,MAArB,CAAP;AACAC,EAAAA,IAAI,GAAG,2BAAaA,IAAb,EAAmBD,MAAnB,CAAP;AAEA,SAAOC,IAAP;AACD,CAdD;AAgBA;;;;;;;;AAMA,MAAME,MAAM,GAAG,CAACC,GAAD,EAAMC,gBAAN,EAAwBL,MAAxB,KAAmC;AAChD,QAAMC,IAAI,GAAGH,WAAW,CAAC,CAACM,GAAD,CAAD,EAAQJ,MAAR,CAAxB;AAEA,QAAMM,IAAI,GAAGL,IAAI,CAACM,GAAL,CAAUC,UAAD,IAAgB;AACpC,WAAO,sBAAQA,UAAR,EAAoBR,MAAM,CAACS,MAA3B,CAAP;AACD,GAFY,EAEVC,IAFU,CAEL,EAFK,CAAb;AAIA,MAAIC,MAAJ;AAEAA,EAAAA,MAAM,GAAG,EAAT;AAEAA,EAAAA,MAAM,IAAI,+BAAcN,gBAAd,EAAgCL,MAAM,CAACS,MAAvC,CAAV;AACAE,EAAAA,MAAM,IAAIL,IAAV;AACAK,EAAAA,MAAM,IAAI,kCAAiBN,gBAAjB,EAAmCL,MAAM,CAACS,MAA1C,CAAV;AAEAE,EAAAA,MAAM,GAAG,uBAAUA,MAAV,CAAT;AAEAC,EAAAA,OAAO,CAACC,MAAR,CAAeC,KAAf,CAAqBH,MAArB;AACD,CAlBD;AAoBA;;;;;;;;AAMA,MAAMI,MAAM,GAAG,CAACX,GAAD,EAAMC,gBAAN,EAAwBL,MAAxB,KAAmC;AAChD,QAAMC,IAAI,GAAGH,WAAW,CAAC,CAACM,GAAD,CAAD,EAAQJ,MAAR,CAAxB;AAEA,QAAMM,IAAI,GAAGL,IAAI,CAACM,GAAL,CAAUC,UAAD,IAAgB;AACpC,WAAO,sBAAQA,UAAR,EAAoBR,MAAM,CAACS,MAA3B,CAAP;AACD,GAFY,EAEVC,IAFU,CAEL,EAFK,CAAb;AAIA,MAAIC,MAAM,GAAG,EAAb;AACA,QAAMK,MAAM,GAAG,kCAAiBX,gBAAjB,EAAmCL,MAAM,CAACS,MAA1C,CAAf;;AAEA,MAAIO,MAAM,KAAK,IAAf,EAAqB;AACnBL,IAAAA,MAAM,GAAG,YAAT;AACD;;AAEDA,EAAAA,MAAM,IAAI,gCAAeN,gBAAf,EAAiCL,MAAM,CAACS,MAAxC,CAAV;AACAE,EAAAA,MAAM,IAAIL,IAAV;AACAK,EAAAA,MAAM,IAAIK,MAAV;AAEAL,EAAAA,MAAM,GAAG,uBAAUA,MAAV,CAAT;AAEAC,EAAAA,OAAO,CAACC,MAAR,CAAeC,KAAf,CAAqBH,MAArB;AACD,CArBD;AAuBA;;;;;;sBAIgBM,UAAU,GAAG,E,KAAO;AAClC,QAAMjB,MAAM,GAAG,+BAAiBiB,UAAjB,CAAf,CADkC,CAGlC;;AACA,QAAMZ,gBAAgB,GAAG,sBAAS,yBAAYL,MAAM,CAACkB,OAAnB,EAA6BC,MAAD,IAAY;AACxE,WAAOA,MAAM,CAACC,KAAP,GAAeD,MAAM,CAACE,WAAtB,GAAoCF,MAAM,CAACG,YAAlD;AACD,GAFiC,CAAT,CAAzB;AAIA,MAAIC,KAAJ;AAEAA,EAAAA,KAAK,GAAG,IAAR;AAEA,SAAO;AACL;;;;AAIAT,IAAAA,KAAK,EAAGV,GAAD,IAAS;AACd,UAAIA,GAAG,CAACoB,MAAJ,KAAexB,MAAM,CAACyB,WAA1B,EAAuC;AACrC,cAAM,IAAIC,KAAJ,CAAU,uDAAV,CAAN;AACD;;AAED,UAAIH,KAAJ,EAAW;AACTA,QAAAA,KAAK,GAAG,KAAR;AAEA,eAAOpB,MAAM,CAACC,GAAD,EAAMC,gBAAN,EAAwBL,MAAxB,CAAb;AACD,OAJD,MAIO;AACL,eAAOe,MAAM,CAACX,GAAD,EAAMC,gBAAN,EAAwBL,MAAxB,CAAb;AACD;AACF;AAjBI,GAAP;AAmBD,C", "sourcesContent": ["import _ from 'lodash';\nimport makeStreamConfig from './makeStreamConfig';\nimport drawRow from './drawRow';\nimport {\n  drawBorderBottom,\n  drawBorderJoin,\n  drawBorderTop\n} from './drawBorder';\nimport stringifyTableData from './stringifyTableData';\nimport truncateTableData from './truncateTableData';\nimport mapDataUsingRowHeightIndex from './mapDataUsingRowHeightIndex';\nimport alignTableData from './alignTableData';\nimport padTableData from './padTableData';\nimport calculateRowHeightIndex from './calculateRowHeightIndex';\n\n/**\n * @param {Array} data\n * @param {Object} config\n * @returns {Array}\n */\nconst prepareData = (data, config) => {\n  let rows;\n\n  rows = stringifyTableData(data);\n\n  rows = truncateTableData(data, config);\n\n  const rowHeightIndex = calculateRowHeightIndex(rows, config);\n\n  rows = mapDataUsingRowHeightIndex(rows, rowHeightIndex, config);\n  rows = alignTableData(rows, config);\n  rows = padTableData(rows, config);\n\n  return rows;\n};\n\n/**\n * @param {string[]} row\n * @param {number[]} columnWidthIndex\n * @param {Object} config\n * @returns {undefined}\n */\nconst create = (row, columnWidthIndex, config) => {\n  const rows = prepareData([row], config);\n\n  const body = rows.map((literalRow) => {\n    return drawRow(literalRow, config.border);\n  }).join('');\n\n  let output;\n\n  output = '';\n\n  output += drawBorderTop(columnWidthIndex, config.border);\n  output += body;\n  output += drawBorderBottom(columnWidthIndex, config.border);\n\n  output = _.trimEnd(output);\n\n  process.stdout.write(output);\n};\n\n/**\n * @param {string[]} row\n * @param {number[]} columnWidthIndex\n * @param {Object} config\n * @returns {undefined}\n */\nconst append = (row, columnWidthIndex, config) => {\n  const rows = prepareData([row], config);\n\n  const body = rows.map((literalRow) => {\n    return drawRow(literalRow, config.border);\n  }).join('');\n\n  let output = '';\n  const bottom = drawBorderBottom(columnWidthIndex, config.border);\n\n  if (bottom !== '\\n') {\n    output = '\\r\\u001B[K';\n  }\n\n  output += drawBorderJoin(columnWidthIndex, config.border);\n  output += body;\n  output += bottom;\n\n  output = _.trimEnd(output);\n\n  process.stdout.write(output);\n};\n\n/**\n * @param {Object} userConfig\n * @returns {Object}\n */\nexport default (userConfig = {}) => {\n  const config = makeStreamConfig(userConfig);\n\n  // @todo Use 'Object.values' when Node.js v6 support is dropped.\n  const columnWidthIndex = _.values(_.mapValues(config.columns, (column) => {\n    return column.width + column.paddingLeft + column.paddingRight;\n  }));\n\n  let empty;\n\n  empty = true;\n\n  return {\n    /**\n     * @param {string[]} row\n     * @returns {undefined}\n     */\n    write: (row) => {\n      if (row.length !== config.columnCount) {\n        throw new Error('Row cell count does not match the config.columnCount.');\n      }\n\n      if (empty) {\n        empty = false;\n\n        return create(row, columnWidthIndex, config);\n      } else {\n        return append(row, columnWidthIndex, config);\n      }\n    }\n  };\n};\n"], "file": "createStream.js"}