{"version": 3, "sources": ["../src/validateTableData.js"], "names": ["rows", "Array", "isArray", "TypeError", "length", "Error", "columnNumber", "cells", "cell", "test"], "mappings": ";;;;;;;AAAA;;;;AAIA;;;;AAIA;;;;0BAIgBA,I,IAAS;AACvB,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACxB,UAAM,IAAIG,SAAJ,CAAc,8BAAd,CAAN;AACD;;AAED,MAAIH,IAAI,CAACI,MAAL,KAAgB,CAApB,EAAuB;AACrB,UAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,MAAIL,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAAR,KAAmB,CAAvB,EAA0B;AACxB,UAAM,IAAIC,KAAJ,CAAU,wCAAV,CAAN;AACD;;AAED,QAAMC,YAAY,GAAGN,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAA7B;AAbuB;AAAA;AAAA;;AAAA;AAevB,yBAAoBJ,IAApB,8HAA0B;AAAA,YAAfO,KAAe;;AACxB,UAAI,CAACN,KAAK,CAACC,OAAN,CAAcK,KAAd,CAAL,EAA2B;AACzB,cAAM,IAAIJ,SAAJ,CAAc,kCAAd,CAAN;AACD;;AAED,UAAII,KAAK,CAACH,MAAN,KAAiBE,YAArB,EAAmC;AACjC,cAAM,IAAID,KAAJ,CAAU,+CAAV,CAAN;AACD;;AAPuB;AAAA;AAAA;;AAAA;AASxB,8BAAmBE,KAAnB,mIAA0B;AAAA,gBAAfC,IAAe;;AACxB;AACA,cAAI,4CAA4CC,IAA5C,CAAiDD,IAAjD,CAAJ,EAA4D;AAC1D,kBAAM,IAAIH,KAAJ,CAAU,iDAAV,CAAN;AACD;AACF;AAduB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAezB;AA9BsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BxB,C", "sourcesContent": ["/**\n * @typedef {string} cell\n */\n\n/**\n * @typedef {cell[]} validateData~column\n */\n\n/**\n * @param {column[]} rows\n * @returns {undefined}\n */\nexport default (rows) => {\n  if (!Array.isArray(rows)) {\n    throw new TypeError('Table data must be an array.');\n  }\n\n  if (rows.length === 0) {\n    throw new Error('Table must define at least one row.');\n  }\n\n  if (rows[0].length === 0) {\n    throw new Error('Table must define at least one column.');\n  }\n\n  const columnNumber = rows[0].length;\n\n  for (const cells of rows) {\n    if (!Array.isArray(cells)) {\n      throw new TypeError('Table row data must be an array.');\n    }\n\n    if (cells.length !== columnNumber) {\n      throw new Error('Table must have a consistent number of cells.');\n    }\n\n    for (const cell of cells) {\n      // eslint-disable-next-line no-control-regex\n      if (/[\\u0001-\\u0006\\u0008-\\u0009\\u000B-\\u001A]/.test(cell)) {\n        throw new Error('Table data must not contain control characters.');\n      }\n    }\n  }\n};\n"], "file": "validateTableData.js"}