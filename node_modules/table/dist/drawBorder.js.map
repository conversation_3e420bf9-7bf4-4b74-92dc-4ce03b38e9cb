{"version": 3, "sources": ["../src/drawBorder.js"], "names": ["drawBorder", "columnSizeIndex", "parts", "columns", "map", "size", "body", "repeat", "join", "left", "right", "drawBorderTop", "border", "topBody", "topJoin", "topLeft", "topRight", "drawBorderJoin", "joinBody", "joinJoin", "joinLeft", "joinRight", "drawBorderBottom", "bottomBody", "bottomJoin", "bottomLeft", "bottomRight"], "mappings": ";;;;;;;AAAA;;;;;;;;AAQA;;;;;AAKA,MAAMA,UAAU,GAAG,CAACC,eAAD,EAAkBC,KAAlB,KAA4B;AAC7C,QAAMC,OAAO,GAAGF,eAAe,CAC5BG,GADa,CACRC,IAAD,IAAU;AACb,WAAOH,KAAK,CAACI,IAAN,CAAWC,MAAX,CAAkBF,IAAlB,CAAP;AACD,GAHa,EAIbG,IAJa,CAIRN,KAAK,CAACM,IAJE,CAAhB;AAMA,SAAON,KAAK,CAACO,IAAN,GAAaN,OAAb,GAAuBD,KAAK,CAACQ,KAA7B,GAAqC,IAA5C;AACD,CARD;AAUA;;;;;;;;AAQA;;;;;;;;;AAKA,MAAMC,aAAa,GAAG,CAACV,eAAD,EAAkBC,KAAlB,KAA4B;AAChD,QAAMU,MAAM,GAAGZ,UAAU,CAACC,eAAD,EAAkB;AACzCK,IAAAA,IAAI,EAAEJ,KAAK,CAACW,OAD6B;AAEzCL,IAAAA,IAAI,EAAEN,KAAK,CAACY,OAF6B;AAGzCL,IAAAA,IAAI,EAAEP,KAAK,CAACa,OAH6B;AAIzCL,IAAAA,KAAK,EAAER,KAAK,CAACc;AAJ4B,GAAlB,CAAzB;;AAOA,MAAIJ,MAAM,KAAK,IAAf,EAAqB;AACnB,WAAO,EAAP;AACD;;AAED,SAAOA,MAAP;AACD,CAbD;AAeA;;;;;;;;AAQA;;;;;;;;;AAKA,MAAMK,cAAc,GAAG,CAAChB,eAAD,EAAkBC,KAAlB,KAA4B;AACjD,SAAOF,UAAU,CAACC,eAAD,EAAkB;AACjCK,IAAAA,IAAI,EAAEJ,KAAK,CAACgB,QADqB;AAEjCV,IAAAA,IAAI,EAAEN,KAAK,CAACiB,QAFqB;AAGjCV,IAAAA,IAAI,EAAEP,KAAK,CAACkB,QAHqB;AAIjCV,IAAAA,KAAK,EAAER,KAAK,CAACmB;AAJoB,GAAlB,CAAjB;AAMD,CAPD;AASA;;;;;;;;AAQA;;;;;;;;;AAKA,MAAMC,gBAAgB,GAAG,CAACrB,eAAD,EAAkBC,KAAlB,KAA4B;AACnD,SAAOF,UAAU,CAACC,eAAD,EAAkB;AACjCK,IAAAA,IAAI,EAAEJ,KAAK,CAACqB,UADqB;AAEjCf,IAAAA,IAAI,EAAEN,KAAK,CAACsB,UAFqB;AAGjCf,IAAAA,IAAI,EAAEP,KAAK,CAACuB,UAHqB;AAIjCf,IAAAA,KAAK,EAAER,KAAK,CAACwB;AAJoB,GAAlB,CAAjB;AAMD,CAPD", "sourcesContent": ["/**\n * @typedef drawBorder~parts\n * @property {string} left\n * @property {string} right\n * @property {string} body\n * @property {string} join\n */\n\n/**\n * @param {number[]} columnSizeIndex\n * @param {drawBorder~parts} parts\n * @returns {string}\n */\nconst drawBorder = (columnSizeIndex, parts) => {\n  const columns = columnSizeIndex\n    .map((size) => {\n      return parts.body.repeat(size);\n    })\n    .join(parts.join);\n\n  return parts.left + columns + parts.right + '\\n';\n};\n\n/**\n * @typedef drawBorderTop~parts\n * @property {string} topLeft\n * @property {string} topRight\n * @property {string} topBody\n * @property {string} topJoin\n */\n\n/**\n * @param {number[]} columnSizeIndex\n * @param {drawBorderTop~parts} parts\n * @returns {string}\n */\nconst drawBorderTop = (columnSizeIndex, parts) => {\n  const border = drawBorder(columnSizeIndex, {\n    body: parts.topBody,\n    join: parts.topJoin,\n    left: parts.topLeft,\n    right: parts.topRight\n  });\n\n  if (border === '\\n') {\n    return '';\n  }\n\n  return border;\n};\n\n/**\n * @typedef drawBorderJoin~parts\n * @property {string} joinLeft\n * @property {string} joinRight\n * @property {string} joinBody\n * @property {string} joinJoin\n */\n\n/**\n * @param {number[]} columnSizeIndex\n * @param {drawBorderJoin~parts} parts\n * @returns {string}\n */\nconst drawBorderJoin = (columnSizeIndex, parts) => {\n  return drawBorder(columnSizeIndex, {\n    body: parts.joinBody,\n    join: parts.joinJoin,\n    left: parts.joinLeft,\n    right: parts.joinRight\n  });\n};\n\n/**\n * @typedef drawBorderBottom~parts\n * @property {string} topLeft\n * @property {string} topRight\n * @property {string} topBody\n * @property {string} topJoin\n */\n\n/**\n * @param {number[]} columnSizeIndex\n * @param {drawBorderBottom~parts} parts\n * @returns {string}\n */\nconst drawBorderBottom = (columnSizeIndex, parts) => {\n  return drawBorder(columnSizeIndex, {\n    body: parts.bottomBody,\n    join: parts.bottomJoin,\n    left: parts.bottomLeft,\n    right: parts.bottomRight\n  });\n};\n\nexport {\n  drawBorder,\n  drawBorderBottom,\n  drawBorderJoin,\n  drawBorderTop\n};\n"], "file": "drawBorder.js"}