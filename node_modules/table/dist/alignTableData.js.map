{"version": 3, "sources": ["../src/alignTableData.js"], "names": ["rows", "config", "map", "cells", "value", "index1", "column", "columns", "width", "alignment"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;;;;;wBAKgBA,I,EAAMC,M,KAAW;AAC/B,SAAOD,IAAI,CAACE,GAAL,CAAUC,KAAD,IAAW;AACzB,WAAOA,KAAK,CAACD,GAAN,CAAU,CAACE,KAAD,EAAQC,MAAR,KAAmB;AAClC,YAAMC,MAAM,GAAGL,MAAM,CAACM,OAAP,CAAeF,MAAf,CAAf;;AAEA,UAAI,0BAAYD,KAAZ,MAAuBE,MAAM,CAACE,KAAlC,EAAyC;AACvC,eAAOJ,KAAP;AACD,OAFD,MAEO;AACL,eAAO,0BAAYA,KAAZ,EAAmBE,MAAM,CAACE,KAA1B,EAAiCF,MAAM,CAACG,SAAxC,CAAP;AACD;AACF,KARM,CAAP;AASD,GAVM,CAAP;AAWD,C", "sourcesContent": ["import stringWidth from 'string-width';\nimport alignString from './alignString';\n\n/**\n * @param {table~row[]} rows\n * @param {Object} config\n * @returns {table~row[]}\n */\nexport default (rows, config) => {\n  return rows.map((cells) => {\n    return cells.map((value, index1) => {\n      const column = config.columns[index1];\n\n      if (stringWidth(value) === column.width) {\n        return value;\n      } else {\n        return alignString(value, column.width, column.alignment);\n      }\n    });\n  });\n};\n"], "file": "alignTableData.js"}