"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _isNumber2 = _interopRequireDefault(require("lodash/isNumber"));

var _isString2 = _interopRequireDefault(require("lodash/isString"));

var _stringWidth = _interopRequireDefault(require("string-width"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

const alignments = ['left', 'right', 'center'];
/**
 * @param {string} subject
 * @param {number} width
 * @returns {string}
 */

const alignLeft = (subject, width) => {
  return subject + ' '.repeat(width);
};
/**
 * @param {string} subject
 * @param {number} width
 * @returns {string}
 */


const alignRight = (subject, width) => {
  return ' '.repeat(width) + subject;
};
/**
 * @param {string} subject
 * @param {number} width
 * @returns {string}
 */


const alignCenter = (subject, width) => {
  let halfWidth;
  halfWidth = width / 2;

  if (halfWidth % 2 === 0) {
    return ' '.repeat(halfWidth) + subject + ' '.repeat(halfWidth);
  } else {
    halfWidth = Math.floor(halfWidth);
    return ' '.repeat(halfWidth) + subject + ' '.repeat(halfWidth + 1);
  }
};
/**
 * Pads a string to the left and/or right to position the subject
 * text in a desired alignment within a container.
 *
 * @param {string} subject
 * @param {number} containerWidth
 * @param {string} alignment One of the valid options (left, right, center).
 * @returns {string}
 */


const alignString = (subject, containerWidth, alignment) => {
  if (!(0, _isString2.default)(subject)) {
    throw new TypeError('Subject parameter value must be a string.');
  }

  if (!(0, _isNumber2.default)(containerWidth)) {
    throw new TypeError('Container width parameter value must be a number.');
  }

  const subjectWidth = (0, _stringWidth.default)(subject);

  if (subjectWidth > containerWidth) {
    // console.log('subjectWidth', subjectWidth, 'containerWidth', containerWidth, 'subject', subject);
    throw new Error('Subject parameter value width cannot be greater than the container width.');
  }

  if (!(0, _isString2.default)(alignment)) {
    throw new TypeError('Alignment parameter value must be a string.');
  }

  if (!alignments.includes(alignment)) {
    throw new Error('Alignment parameter value must be a known alignment parameter value (left, right, center).');
  }

  if (subjectWidth === 0) {
    return ' '.repeat(containerWidth);
  }

  const availableWidth = containerWidth - subjectWidth;

  if (alignment === 'left') {
    return alignLeft(subject, availableWidth);
  }

  if (alignment === 'right') {
    return alignRight(subject, availableWidth);
  }

  return alignCenter(subject, availableWidth);
};

var _default = alignString;
exports.default = _default;
//# sourceMappingURL=alignString.js.map