{"name": "yargs-parser", "version": "13.1.1", "description": "the mighty option parser used by yargs", "main": "index.js", "scripts": {"test": "nyc mocha test/*.js", "posttest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"url": "**************:yargs/yargs-parser.git"}, "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "devDependencies": {"chai": "^4.2.0", "coveralls": "^3.0.2", "mocha": "^5.2.0", "nyc": "^14.1.0", "standard": "^12.0.1", "standard-version": "^6.0.0"}, "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "files": ["lib", "index.js"], "engine": {"node": ">=6"}}