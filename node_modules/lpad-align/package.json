{"name": "lpad-align", "version": "1.1.2", "description": "Left pad a string to align with the longest string in an array", "license": "MIT", "repository": "kevva/lpad-align", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/kevva"}, "bin": "cli.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["cli.js", "index.js"], "keywords": ["align", "indent", "lpad"], "dependencies": {"get-stdin": "^4.0.1", "indent-string": "^2.1.0", "longest": "^1.0.0", "meow": "^3.3.0"}, "devDependencies": {"ava": "*", "xo": "*"}}