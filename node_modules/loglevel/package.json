{"name": "loglevel", "description": "Minimal lightweight logging for JavaScript, adding reliable log level methods to any available console.log methods", "version": "1.6.6", "homepage": "https://github.com/pimterry/loglevel", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tim-perry.co.uk"}, "repository": {"type": "git", "url": "git://github.com/pimterry/loglevel.git"}, "bugs": {"url": "https://github.com/pimterry/loglevel/issues"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-loglevel?utm_medium=referral&utm_source=npm_fund"}, "license": "MIT", "main": "lib/loglevel.js", "types": "./index.d.ts", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "grunt test && tsc --noEmit ./test/type-test.ts", "ci": "grunt ci", "dist": "grunt dist", "watch": "grunt watch"}, "dependencies": {}, "devDependencies": {"@types/core-js": "2.5.0", "@types/node": "^12.0.4", "grunt": "~0.4.5", "grunt-cli": "~0.1.13", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "^1.1.0", "grunt-contrib-qunit": "~0.5.2", "grunt-contrib-uglify": "~0.5.1", "grunt-contrib-watch": "~0.6.1", "grunt-coveralls": "^1.0.0", "grunt-jasmine-node": "~0.2.1", "grunt-open": "~0.2.3", "grunt-preprocess": "^4.0.0", "grunt-saucelabs": "^8.2.0", "grunt-template-jasmine-istanbul": "~0.2.5", "grunt-template-jasmine-requirejs": "~0.1.6", "qunitjs": "1.14.0", "typescript": "^3.5.1"}, "keywords": ["log", "logger", "logging", "browser"]}