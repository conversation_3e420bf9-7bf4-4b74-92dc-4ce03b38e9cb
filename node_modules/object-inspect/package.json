{"name": "object-inspect", "version": "1.7.0", "description": "string representations of objects in node and the browser", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "core-js": "^2.6.10", "eslint": "^6.6.0", "nyc": "^10.3.2", "tape": "^4.11.0"}, "scripts": {"pretest": "npm run lint", "lint": "eslint .", "test": "npm run tests-only", "pretests-only": "node test-core-js", "tests-only": "tape test/*.js", "posttest": "npx aud --production", "coverage": "nyc npm run tests-only"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/substack/object-inspect.git"}, "homepage": "https://github.com/substack/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "greenkeeper": {"ignore": ["nyc", "core-js"]}}