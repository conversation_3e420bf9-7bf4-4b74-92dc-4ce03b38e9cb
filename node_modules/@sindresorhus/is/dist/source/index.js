"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const util = require("util");
const toString = Object.prototype.toString;
const getObjectType = (value) => toString.call(value).slice(8, -1);
const isOfType = (type) => (value) => typeof value === type;
const isObjectOfType = (type) => (value) => getObjectType(value) === type;
function is(value) {
    if (value === null) {
        return 'null';
    }
    if (value === true || value === false) {
        return 'boolean';
    }
    const type = typeof value;
    if (type === 'undefined') {
        return 'undefined';
    }
    if (type === 'string') {
        return 'string';
    }
    if (type === 'number') {
        return 'number';
    }
    if (type === 'symbol') {
        return 'symbol';
    }
    if (is.function_(value)) {
        return 'Function';
    }
    if (Array.isArray(value)) {
        return 'Array';
    }
    if (Buffer.isBuffer(value)) {
        return 'Buffer';
    }
    const tagType = getObjectType(value);
    if (tagType) {
        return tagType;
    }
    if (value instanceof String || value instanceof Boolean || value instanceof Number) {
        throw new TypeError('Please don\'t use object wrappers for primitive types');
    }
    return 'Object';
}
(function (is) {
    const isObject = (value) => typeof value === 'object';
    is.undefined = isOfType('undefined');
    is.string = isOfType('string');
    is.number = isOfType('number');
    is.function_ = isOfType('function');
    is.null_ = (value) => value === null;
    is.class_ = (value) => is.function_(value) && value.toString().startsWith('class ');
    is.boolean = (value) => value === true || value === false;
    is.symbol = isOfType('symbol');
    is.array = Array.isArray;
    is.buffer = Buffer.isBuffer;
    is.nullOrUndefined = (value) => is.null_(value) || is.undefined(value);
    is.object = (value) => !is.nullOrUndefined(value) && (is.function_(value) || isObject(value));
    is.iterable = (value) => !is.nullOrUndefined(value) && is.function_(value[Symbol.iterator]);
    is.generator = (value) => is.iterable(value) && is.function_(value.next) && is.function_(value.throw);
    is.nativePromise = isObjectOfType('Promise');
    const hasPromiseAPI = (value) => !is.null_(value) &&
        isObject(value) &&
        is.function_(value.then) &&
        is.function_(value.catch);
    is.promise = (value) => is.nativePromise(value) || hasPromiseAPI(value);
    const isFunctionOfType = (type) => (value) => is.function_(value) && is.function_(value.constructor) && value.constructor.name === type;
    is.generatorFunction = isFunctionOfType('GeneratorFunction');
    is.asyncFunction = isFunctionOfType('AsyncFunction');
    is.regExp = isObjectOfType('RegExp');
    is.date = isObjectOfType('Date');
    is.error = isObjectOfType('Error');
    is.map = isObjectOfType('Map');
    is.set = isObjectOfType('Set');
    is.weakMap = isObjectOfType('WeakMap');
    is.weakSet = isObjectOfType('WeakSet');
    is.int8Array = isObjectOfType('Int8Array');
    is.uint8Array = isObjectOfType('Uint8Array');
    is.uint8ClampedArray = isObjectOfType('Uint8ClampedArray');
    is.int16Array = isObjectOfType('Int16Array');
    is.uint16Array = isObjectOfType('Uint16Array');
    is.int32Array = isObjectOfType('Int32Array');
    is.uint32Array = isObjectOfType('Uint32Array');
    is.float32Array = isObjectOfType('Float32Array');
    is.float64Array = isObjectOfType('Float64Array');
    is.arrayBuffer = isObjectOfType('ArrayBuffer');
    is.sharedArrayBuffer = isObjectOfType('SharedArrayBuffer');
    is.truthy = (value) => Boolean(value);
    is.falsy = (value) => !value;
    is.nan = (value) => Number.isNaN(value);
    const primitiveTypes = new Set([
        'undefined',
        'string',
        'number',
        'boolean',
        'symbol'
    ]);
    is.primitive = (value) => is.null_(value) || primitiveTypes.has(typeof value);
    is.integer = (value) => Number.isInteger(value);
    is.safeInteger = (value) => Number.isSafeInteger(value);
    is.plainObject = (value) => {
        let prototype;
        return getObjectType(value) === 'Object' &&
            (prototype = Object.getPrototypeOf(value), prototype === null ||
                prototype === Object.getPrototypeOf({}));
    };
    const typedArrayTypes = new Set([
        'Int8Array',
        'Uint8Array',
        'Uint8ClampedArray',
        'Int16Array',
        'Uint16Array',
        'Int32Array',
        'Uint32Array',
        'Float32Array',
        'Float64Array'
    ]);
    is.typedArray = (value) => typedArrayTypes.has(getObjectType(value));
    const isValidLength = (value) => is.safeInteger(value) && value > -1;
    is.arrayLike = (value) => !is.nullOrUndefined(value) && !is.function_(value) && isValidLength(value.length);
    is.inRange = (value, range) => {
        if (is.number(range)) {
            return value >= Math.min(0, range) && value <= Math.max(range, 0);
        }
        if (is.array(range) && range.length === 2) {
            return value >= Math.min.apply(null, range) && value <= Math.max.apply(null, range);
        }
        throw new TypeError(`Invalid range: ${util.inspect(range)}`);
    };
    const NODE_TYPE_ELEMENT = 1;
    const DOM_PROPERTIES_TO_CHECK = [
        'innerHTML',
        'ownerDocument',
        'style',
        'attributes',
        'nodeValue'
    ];
    is.domElement = (value) => is.object(value) && value.nodeType === NODE_TYPE_ELEMENT && is.string(value.nodeName) &&
        !is.plainObject(value) && DOM_PROPERTIES_TO_CHECK.every(property => property in value);
    is.infinite = (value) => value === Infinity || value === -Infinity;
    const isAbsoluteMod2 = (value) => (rem) => is.integer(rem) && Math.abs(rem % 2) === value;
    is.even = isAbsoluteMod2(0);
    is.odd = isAbsoluteMod2(1);
    const isWhiteSpaceString = (value) => is.string(value) && /\S/.test(value) === false;
    const isEmptyStringOrArray = (value) => (is.string(value) || is.array(value)) && value.length === 0;
    const isEmptyObject = (value) => !is.map(value) && !is.set(value) && is.object(value) && Object.keys(value).length === 0;
    const isEmptyMapOrSet = (value) => (is.map(value) || is.set(value)) && value.size === 0;
    is.empty = (value) => is.falsy(value) || isEmptyStringOrArray(value) || isEmptyObject(value) || isEmptyMapOrSet(value);
    is.emptyOrWhitespace = (value) => is.empty(value) || isWhiteSpaceString(value);
    const predicateOnArray = (method, predicate, args) => {
        const values = Array.prototype.slice.call(args, 1);
        if (is.function_(predicate) === false) {
            throw new TypeError(`Invalid predicate: ${util.inspect(predicate)}`);
        }
        if (values.length === 0) {
            throw new TypeError('Invalid number of values');
        }
        return method.call(values, predicate);
    };
    function any(predicate) {
        return predicateOnArray(Array.prototype.some, predicate, arguments);
    }
    is.any = any;
    function all(predicate) {
        return predicateOnArray(Array.prototype.every, predicate, arguments);
    }
    is.all = all;
})(is || (is = {}));
Object.defineProperties(is, {
    class: {
        value: is.class_
    },
    function: {
        value: is.function_
    },
    null: {
        value: is.null_
    }
});
exports.default = is;
//# sourceMappingURL=index.js.map