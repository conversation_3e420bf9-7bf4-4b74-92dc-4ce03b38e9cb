{"name": "os-locale", "version": "3.1.0", "description": "Get the system locale", "license": "MIT", "repository": "sindresorhus/os-locale", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locale", "lang", "language", "system", "os", "string", "str", "user", "country", "id", "identifier", "region"], "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "devDependencies": {"ava": "^1.0.1", "import-fresh": "^3.0.0", "xo": "^0.23.0"}}