{"name": "spdx-license-ids", "version": "3.0.5", "description": "A list of SPDX license identifiers", "repository": "shinnn/spdx-license-ids", "author": "<PERSON><PERSON><PERSON> (https://github.com/shinnn)", "license": "CC0-1.0", "scripts": {"build": "node build.js", "pretest": "eslint .", "test": "node test.js"}, "files": ["deprecated.json", "index.json"], "keywords": ["spdx", "license", "licenses", "id", "identifier", "identifiers", "json", "array", "oss"], "devDependencies": {"@shinnn/eslint-config": "^6.8.7", "chalk": "^2.4.1", "eslint": "^5.10.0", "get-spdx-license-ids": "^2.1.0", "rmfr": "^2.0.0", "tape": "^4.9.1"}, "eslintConfig": {"extends": "@shinnn"}}