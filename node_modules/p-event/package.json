{"name": "p-event", "version": "1.3.0", "description": "Promisify an event by waiting for it to be emitted", "license": "MIT", "repository": "sindresorhus/p-event", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "events", "event", "emitter", "eventemitter", "event-emitter", "emit", "emits", "listener", "promisify", "addlistener", "addeventlistener", "wait", "waits", "on", "browser", "dom", "async", "await", "promises", "bluebird"], "dependencies": {"p-timeout": "^1.1.1"}, "devDependencies": {"ava": "*", "delay": "^1.3.1", "xo": "*"}}