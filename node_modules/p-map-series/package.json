{"name": "p-map-series", "version": "1.0.0", "description": "Map over promises serially", "license": "MIT", "repository": "sindresorhus/p-map-series", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "map", "collection", "iterable", "iterator", "fulfilled", "serial", "serially", "async", "await", "promises", "bluebird"], "dependencies": {"p-reduce": "^1.0.0"}, "devDependencies": {"ava": "*", "delay": "^1.3.1", "time-span": "^1.0.0", "xo": "*"}, "xo": {"esnext": true}}