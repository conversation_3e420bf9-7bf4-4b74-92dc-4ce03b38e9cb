2.1.0 / 2019-09-09
=================
  * [New] add `auto` entry point
  * [Deps] update `function-bind`, `define-properties`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape`, `@es-shims/api`
  * [meta] clean up scripts
  * [meta] Only apps should have lockfiles
  * [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v7.10`, `v6.17`, `v5.10`, `v4.9`; use `nvm install-latest-npm`
  * [Tests] allow a name of `trimLeft` or `trimStart`
  * [Tests] fix tests for the mongolian vowel separator
  * [Tests] use `functions-have-names`
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] remove `jscs`
  * [Tests] use pretest/posttest for linting/security

2.0.0 / 2016-02-06
=================
  * [Breaking] conform to the es-shim API
  * [Deps] update `define-properties`
  * [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`
  * [Tests] up to `node` `v5.5`
  * [Tests] fix npm upgrades on older nodes

1.0.1 / 2015-07-29
=================
  * Fix deps mistakenly being dev deps

1.0.0 / 2015-07-29
=================
  * v1.0.0
