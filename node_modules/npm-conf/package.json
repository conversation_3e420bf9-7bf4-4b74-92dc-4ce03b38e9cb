{"name": "npm-conf", "version": "1.1.3", "description": "Get the npm config", "license": "MIT", "repository": "kevva/npm-conf", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"prepublish": "node lib/make.js", "test": "xo && ava"}, "files": ["index.js", "lib"], "keywords": ["conf", "config", "global", "npm", "path", "prefix", "rc"], "dependencies": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "devDependencies": {"ava": "*", "babel-generator": "^6.24.1", "babel-traverse": "^6.24.1", "babylon": "^6.17.1", "npm": "^5.0.4", "xo": "*"}, "xo": {"ignores": ["lib/defaults.js", "lib/types.js"]}}