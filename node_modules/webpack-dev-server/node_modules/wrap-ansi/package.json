{"name": "wrap-ansi", "version": "2.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": "chalk/wrap-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)", "<PERSON><PERSON> <<EMAIL>> (github.com/qix-)", "<PERSON> <<EMAIL>> (github.com/bcoe)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "devDependencies": {"ava": "^0.16.0", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^6.2.1", "strip-ansi": "^3.0.0", "xo": "*"}}