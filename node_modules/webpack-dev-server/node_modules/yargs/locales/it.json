{"Commands:": "Comandi:", "Options:": "Opzioni:", "Examples:": "Esempi:", "boolean": "booleano", "count": "contatore", "string": "stringa", "number": "numero", "array": "vettore", "required": "richiesto", "default:": "predefinito:", "choices:": "scelte:", "aliases:": "alias:", "generated-value": "valore generato", "Not enough non-option arguments: got %s, need at least %s": "Numero insufficiente di argomenti non opzione: inseriti %s, richiesti almeno %s", "Too many non-option arguments: got %s, maximum of %s": "Troppi argomenti non opzione: inseriti %s, massimo possibile %s", "Missing argument value: %s": {"one": "Argomento mancante: %s", "other": "Argomenti mancanti: %s"}, "Missing required argument: %s": {"one": "Argomento richiesto mancante: %s", "other": "Argomenti richiesti mancanti: %s"}, "Unknown argument: %s": {"one": "Argomento sconosciuto: %s", "other": "Argomenti sconosciuti: %s"}, "Invalid values:": "Valori non validi:", "Argument: %s, Given: %s, Choices: %s": "Argomento: %s, Richiesto: %s, Scelte: %s", "Argument check failed: %s": "Controllo dell'argomento fallito: %s", "Implications failed:": "Argomenti impliciti non soddisfatti:", "Not enough arguments following: %s": "Argomenti insufficienti dopo: %s", "Invalid JSON config file: %s": "File di configurazione JSON non valido: %s", "Path to JSON config file": "Percorso del file di configurazione JSON", "Show help": "Mostra la schermata di aiuto", "Show version number": "Mostra il numero di versione", "Did you mean %s?": "Intendi forse %s?"}