{"name": "webpack-dev-server", "version": "3.8.0", "description": "Serves a webpack app. Updates the browser on changes.", "bin": "bin/webpack-dev-server.js", "main": "lib/Server.js", "files": ["bin", "lib", "ssl", "client"], "engines": {"node": ">= 6.11.5"}, "scripts": {"lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint . --cache", "lint": "npm-run-all -l -p \"lint:**\"", "lint:type": "tsc --noEmit", "commitlint": "commitlint --from=master", "security": "npm audit", "test:only": "jest --force<PERSON>xit", "test:coverage": "npm run test:only -- --coverage", "test:watch": "npm run test:coverage --watch", "test": "npm run test:coverage", "pretest": "npm run lint", "prepare": "rimraf ./ssl/*.pem && npm run build:client", "build:client:default": "babel client-src/default --out-dir client --ignore \"./client-src/default/*.config.js\"", "build:client:clients": "babel client-src/clients --out-dir client/clients", "build:client:index": "webpack ./client-src/default/index.js -o client/index.bundle.js --color --config client-src/default/webpack.config.js", "build:client:live": "webpack ./client-src/live/index.js -o client/live.bundle.js --color --config client-src/live/webpack.config.js", "build:client:sockjs": "webpack ./client-src/sockjs/index.js -o client/sockjs.bundle.js --color --config client-src/sockjs/webpack.config.js", "build:client": "rimraf ./client/* && npm-run-all -s -l -p \"build:client:**\"", "webpack-dev-server": "node examples/run-example.js", "release": "standard-version"}, "dependencies": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.1.6", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "debug": "^4.1.1", "del": "^4.1.1", "express": "^4.17.1", "html-entities": "^1.2.1", "http-proxy-middleware": "^0.19.1", "import-local": "^2.0.0", "internal-ip": "^4.3.0", "ip": "^1.1.5", "is-absolute-url": "^3.0.0", "killable": "^1.0.1", "loglevel": "^1.6.3", "opn": "^5.5.0", "p-retry": "^3.0.1", "portfinder": "^1.0.21", "schema-utils": "^1.0.0", "selfsigned": "^1.10.4", "semver": "^6.3.0", "serve-index": "^1.9.1", "sockjs": "0.3.19", "sockjs-client": "1.3.0", "spdy": "^4.0.1", "strip-ansi": "^3.0.1", "supports-color": "^6.1.0", "url": "^0.11.0", "webpack-dev-middleware": "^3.7.0", "webpack-log": "^2.0.0", "ws": "^6.2.1", "yargs": "12.0.5"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/runtime": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "babel-loader": "^8.0.6", "body-parser": "^1.19.0", "commitlint-azure-pipelines-cli": "^1.0.2", "copy-webpack-plugin": "^5.0.4", "css-loader": "^2.1.1", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.18.2", "execa": "^1.0.0", "file-loader": "^3.0.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "husky": "^3.0.3", "jest": "^24.8.0", "jest-junit": "^7.0.0", "jquery": "^3.4.1", "less": "^3.9.0", "less-loader": "^5.0.0", "lint-staged": "^9.2.1", "marked": "^0.7.0", "memfs": "^2.15.5", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "puppeteer": "^1.19.0", "rimraf": "^2.6.3", "standard-version": "^7.0.0", "style-loader": "^1.0.0", "supertest": "^4.0.2", "tcp-port-used": "^1.0.1", "typescript": "^3.5.3", "url-loader": "^1.1.2", "webpack": "^4.39.1", "webpack-cli": "^3.3.6"}, "peerDependencies": {"webpack": "^4.0.0"}, "author": "<PERSON> @sokra", "bugs": "https://github.com/webpack/webpack-dev-server/issues", "homepage": "https://github.com/webpack/webpack-dev-server#readme", "repository": "https://github.com/webpack/webpack-dev-server.git", "license": "MIT"}