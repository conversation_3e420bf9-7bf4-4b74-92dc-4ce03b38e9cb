{"type": "object", "properties": {"after": {"instanceof": "Function"}, "allowedHosts": {"type": "array", "items": {"type": "string"}}, "before": {"instanceof": "Function"}, "bonjour": {"type": "boolean"}, "ca": {"anyOf": [{"type": "string"}, {"instanceof": "<PERSON><PERSON><PERSON>"}]}, "cert": {"anyOf": [{"type": "string"}, {"instanceof": "<PERSON><PERSON><PERSON>"}]}, "clientLogLevel": {"enum": ["info", "warn", "error", "debug", "trace", "silent", "none", "warning"]}, "compress": {"type": "boolean"}, "contentBase": {"anyOf": [{"enum": [false]}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 1}]}, "disableHostCheck": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}}, "filename": {"anyOf": [{"type": "string"}, {"instanceof": "RegExp"}, {"instanceof": "Function"}]}, "fs": {"type": "object"}, "headers": {"type": "object"}, "historyApiFallback": {"anyOf": [{"type": "boolean"}, {"type": "object"}]}, "host": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "hot": {"type": "boolean"}, "hotOnly": {"type": "boolean"}, "http2": {"type": "boolean"}, "https": {"anyOf": [{"type": "object"}, {"type": "boolean"}]}, "index": {"type": "string"}, "injectClient": {"anyOf": [{"type": "boolean"}, {"instanceof": "Function"}]}, "injectHot": {"anyOf": [{"type": "boolean"}, {"instanceof": "Function"}]}, "inline": {"type": "boolean"}, "key": {"anyOf": [{"type": "string"}, {"instanceof": "<PERSON><PERSON><PERSON>"}]}, "lazy": {"type": "boolean"}, "liveReload": {"type": "boolean"}, "log": {"instanceof": "Function"}, "logLevel": {"enum": ["info", "warn", "error", "debug", "trace", "silent"]}, "logTime": {"type": "boolean"}, "mimeTypes": {"type": "object"}, "noInfo": {"type": "boolean"}, "onListening": {"instanceof": "Function"}, "open": {"anyOf": [{"type": "string"}, {"type": "boolean"}]}, "openPage": {"type": "string"}, "overlay": {"anyOf": [{"type": "boolean"}, {"type": "object", "properties": {"errors": {"type": "boolean"}, "warnings": {"type": "boolean"}}}]}, "pfx": {"anyOf": [{"type": "string"}, {"instanceof": "<PERSON><PERSON><PERSON>"}]}, "pfxPassphrase": {"type": "string"}, "port": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}]}, "profile": {"type": "boolean"}, "progress": {"type": "boolean"}, "proxy": {"anyOf": [{"type": "object"}, {"type": "array", "items": {"anyOf": [{"type": "object"}, {"instanceof": "Function"}]}, "minItems": 1}]}, "public": {"type": "string"}, "publicPath": {"type": "string"}, "quiet": {"type": "boolean"}, "reporter": {"instanceof": "Function"}, "requestCert": {"type": "boolean"}, "serveIndex": {"type": "boolean"}, "serverSideRender": {"type": "boolean"}, "setup": {"instanceof": "Function"}, "sockHost": {"type": "string"}, "sockPath": {"type": "string"}, "sockPort": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}]}, "socket": {"type": "string"}, "staticOptions": {"type": "object"}, "stats": {"anyOf": [{"type": "object"}, {"type": "boolean"}, {"enum": ["none", "errors-only", "errors-warnings", "minimal", "normal", "verbose"]}]}, "transportMode": {"anyOf": [{"type": "object", "properties": {"client": {"type": "string"}, "server": {"anyOf": [{"type": "string"}, {"instanceof": "Function"}]}}, "additionalProperties": false}, {"enum": ["sockjs", "ws"]}]}, "useLocalIp": {"type": "boolean"}, "warn": {"instanceof": "Function"}, "watchContentBase": {"type": "boolean"}, "watchOptions": {"type": "object"}, "writeToDisk": {"anyOf": [{"type": "boolean"}, {"instanceof": "Function"}]}}, "errorMessage": {"properties": {"after": "should be {Function} (https://webpack.js.org/configuration/dev-server/#devserverafter)", "allowedHosts": "should be {Array} (https://webpack.js.org/configuration/dev-server/#devserverallowedhosts)", "before": "should be {Function} (https://webpack.js.org/configuration/dev-server/#devserverbefore)", "bonjour": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverbonjour)", "ca": "should be {String|Buffer}", "cert": "should be {String|Buffer}", "clientLogLevel": "should be {String} and equal to one of the allowed values\n\n [ 'none', 'silent', 'info', 'debug', 'trace', 'error', 'warning', 'warn' ]\n\n (https://webpack.js.org/configuration/dev-server/#devserverclientloglevel)", "compress": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devservercompress)", "contentBase": "should be {Number|String|Array} (https://webpack.js.org/configuration/dev-server/#devservercontentbase)", "disableHostCheck": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverdisablehostcheck)", "features": "should be {Array}", "filename": "should be {String|RegExp|Function} (https://webpack.js.org/configuration/dev-server/#devserverfilename-)", "fs": "should be {Object} (https://github.com/webpack/webpack-dev-middleware#fs)", "headers": "should be {Object} (https://webpack.js.org/configuration/dev-server/#devserverheaders-)", "historyApiFallback": "should be {<PERSON><PERSON><PERSON>|Object} (https://webpack.js.org/configuration/dev-server/#devserverhistoryapifallback)", "host": "should be {String|Null} (https://webpack.js.org/configuration/dev-server/#devserverhost)", "hot": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverhot)", "hotOnly": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverhotonly)", "http2": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverhttp2)", "https": "should be {Object|Boolean} (https://webpack.js.org/configuration/dev-server/#devserverhttps)", "index": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserverindex)", "injectClient": "should be {<PERSON><PERSON>an|Function} (https://webpack.js.org/configuration/dev-server/#devserverinjectclient)", "injectHot": "should be {<PERSON><PERSON>an|Function} (https://webpack.js.org/configuration/dev-server/#devserverinjecthot)", "inline": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverinline)", "key": "should be {String|Buffer}", "lazy": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverlazy-)", "liveReload": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverlivereload-)", "log": "should be {Function}", "logLevel": "should be {String} and equal to one of the allowed values\n\n [ 'info', 'warn', 'error', 'debug', 'trace', 'silent' ]\n\n (https://github.com/webpack/webpack-dev-middleware#loglevel)", "logTime": "should be {<PERSON><PERSON><PERSON>} (https://github.com/webpack/webpack-dev-middleware#logtime)", "mimeTypes": "should be {Object} (https://webpack.js.org/configuration/dev-server/#devservermimetypes-)", "noInfo": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devservernoinfo-)", "onListening": "should be {Function} (https://webpack.js.org/configuration/dev-server/#onlistening)", "open": "should be {String|Boolean} (https://webpack.js.org/configuration/dev-server/#devserveropen)", "openPage": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserveropenpage)", "overlay": "should be {<PERSON><PERSON><PERSON>|Object} (https://webpack.js.org/configuration/dev-server/#devserveroverlay)", "pfx": "should be {String|Buffer} (https://webpack.js.org/configuration/dev-server/#devserverpfx)", "pfxPassphrase": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserverpfxpassphrase)", "port": "should be {Number|String|Null} (https://webpack.js.org/configuration/dev-server/#devserverport)", "profile": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverprofile)", "progress": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverprogress---cli-only)", "proxy": "should be {Object|Array} (https://webpack.js.org/configuration/dev-server/#devserverproxy)", "public": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserverpublic)", "publicPath": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserverpublicpath-)", "quiet": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverquiet-)", "reporter": "should be {Function} (https://github.com/webpack/webpack-dev-middleware#reporter)", "requestCert": "should be {<PERSON><PERSON><PERSON>}", "serveIndex": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverserveindex)", "serverSideRender": "should be {<PERSON><PERSON><PERSON>} (https://github.com/webpack/webpack-dev-middleware#serversiderender)", "setup": "should be {Function} (https://webpack.js.org/configuration/dev-server/#devserversetup)", "sockHost": "should be {String|Null} (https://webpack.js.org/configuration/dev-server/#devserversockhost)", "sockPath": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserversockpath)", "sockPort": "should be {Number|String|Null} (https://webpack.js.org/configuration/dev-server/#devserversockport)", "socket": "should be {String} (https://webpack.js.org/configuration/dev-server/#devserversocket)", "staticOptions": "should be {Object} (https://webpack.js.org/configuration/dev-server/#devserverstaticoptions)", "stats": "should be {Object|Boolean} (https://webpack.js.org/configuration/dev-server/#devserverstats-)", "transportMode": "should be {String|Object} (https://webpack.js.org/configuration/dev-server/#devservertransportmode)", "useLocalIp": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserveruselocalip)", "warn": "should be {Function}", "watchContentBase": "should be {<PERSON><PERSON><PERSON>} (https://webpack.js.org/configuration/dev-server/#devserverwatchcontentbase)", "watchOptions": "should be {Object} (https://webpack.js.org/configuration/dev-server/#devserverwatchoptions-)", "writeToDisk": "should be {Bo<PERSON>an|Function} (https://webpack.js.org/configuration/dev-server/#devserverwritetodisk-)"}}, "additionalProperties": false}