{"name": "spdx-expression-parse", "description": "parse SPDX license expressions", "version": "3.0.0", "author": "<PERSON> <<EMAIL>> (http://kemitchell.com)", "files": ["AUTHORS", "index.js", "parse.js", "scan.js"], "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}, "devDependencies": {"defence-cli": "^2.0.1", "mocha": "^3.4.2", "replace-require-self": "^1.0.0", "standard": "^10.0.2"}, "keywords": ["SPDX", "law", "legal", "license", "metadata", "package", "package.json", "standards"], "license": "MIT", "repository": "jslicense/spdx-expression-parse.js", "scripts": {"lint": "standard", "test:readme": "defence -i javascript README.md | replace-require-self | node", "test:mocha": "mocha test/index.js", "test": "npm run test:mocha && npm run test:readme"}}