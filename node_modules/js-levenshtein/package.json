{"name": "js-<PERSON><PERSON><PERSON><PERSON>", "version": "1.1.6", "description": "The most efficient JS implementation calculating the Levenshtein distance, i.e. the difference between two strings.", "license": "MIT", "repository": "gustf/js-le<PERSON><PERSON>tein", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^0.25.0", "fast-levenshtein": "^2.0.6", "levenshtein-edit-distance": "^2.0.3", "matcha": "^0.7.0", "talisman": "^0.21.0", "leven": "^2.1.0", "xo": "^0.23.0"}}