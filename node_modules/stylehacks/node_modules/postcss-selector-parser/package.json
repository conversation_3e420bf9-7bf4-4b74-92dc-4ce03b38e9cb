{"name": "postcss-selector-parser", "version": "3.1.1", "devDependencies": {"ava": "^0.20.0", "babel-cli": "^6.4.0", "babel-core": "^6.4.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "coveralls": "^2.11.6", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^1.10.2", "glob": "^7.0.3", "minimist": "^1.2.0", "nyc": "^10.0.0", "postcss": "^6.0.6"}, "main": "dist/index.js", "types": "postcss-selector-parser.d.ts", "files": ["API.md", "CHANGELOG.md", "LICENSE-MIT", "dist", "postcss-selector-parser.d.ts"], "scripts": {"pretest": "eslint src", "prepublish": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "report": "nyc report --reporter=html", "test": "nyc ava src/__tests__/*.js"}, "dependencies": {"dot-prop": "^4.1.1", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "license": "MIT", "engines": {"node": ">=4"}, "homepage": "https://github.com/postcss/postcss-selector-parser", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/chrise<PERSON><PERSON>"}], "repository": "postcss/postcss-selector-parser", "ava": {"require": "babel-register", "concurrency": 5}, "nyc": {"exclude": ["node_modules", "**/__tests__"]}, "eslintConfig": {"extends": "cssnano"}}