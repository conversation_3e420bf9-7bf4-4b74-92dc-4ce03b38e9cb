{"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.25", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://searchbeam.jit.su)", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["mime", "types"], "repository": "jshttp/mime-types", "dependencies": {"mime-db": "1.42.0"}, "devDependencies": {"eslint": "6.6.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-node": "10.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "6.2.2", "nyc": "14.1.1"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}}