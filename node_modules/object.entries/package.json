{"name": "object.entries", "version": "1.1.0", "author": "<PERSON>", "description": "ES2017 spec-compliant Object.entries shim.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run audit", "tests-only": "es-shim-api && npm run --silent test:shimmed && npm run --silent test:module", "test:shimmed": "node test/shimmed.js", "test:module": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint .", "preaudit": "npm install --package-lock-only --package-lock", "audit": "npm audit", "postaudit": "rm package-lock.json"}, "repository": {"type": "git", "url": "git://github.com/es-shims/Object.entries.git"}, "keywords": ["Object.entries", "Object.values", "Object.keys", "entries", "values", "ES7", "ES8", "ES2017", "shim", "object", "keys", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.12.0", "function-bind": "^1.1.1", "has": "^1.0.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^13.1.1", "array-map": "^0.0.0", "covert": "^1.1.1", "eslint": "^5.11.1", "tape": "^4.9.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}