{"name": "isurl", "description": "Checks whether a value is a WHATWG URL.", "version": "1.0.0", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://www.svachon.com/)", "repository": "stevenvachon/isurl", "dependencies": {"has-to-string-tag-x": "^1.2.0", "is-object": "^1.0.1"}, "devDependencies": {"chai": "^4.0.2", "mocha": "^3.4.2", "semver": "^5.3.0", "universal-url": "^1.0.0"}, "engines": {"node": ">= 4"}, "scripts": {"test": "mocha test --check-leaks --bail"}, "files": ["index.js"], "keywords": ["uri", "url", "whatwg"]}