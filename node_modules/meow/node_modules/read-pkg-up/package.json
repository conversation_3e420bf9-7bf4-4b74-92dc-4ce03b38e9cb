{"name": "read-pkg-up", "version": "1.0.1", "description": "Read the closest package.json file", "license": "MIT", "repository": "sindresorhus/read-pkg-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "pkg", "package", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}