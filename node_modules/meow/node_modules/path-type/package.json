{"name": "path-type", "version": "1.1.0", "description": "Check if a path is a file, directory, or symlink", "license": "MIT", "repository": "sindresorhus/path-type", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"ignores": ["test.js"]}}