{"name": "parse-json", "version": "2.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "license": "MIT", "repository": "sindresorhus/parse-json", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js", "vendor"], "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "dependencies": {"error-ex": "^1.2.0"}, "devDependencies": {"ava": "0.0.4", "xo": "*"}, "xo": {"ignores": ["vendor/**"]}}