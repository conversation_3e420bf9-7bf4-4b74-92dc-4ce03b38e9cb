'use strict';
var path = require('path');
var fs = require('graceful-fs');
var stripBom = require('strip-bom');
var parseJson = require('parse-json');
var Promise = require('pinkie-promise');
var pify = require('pify');

function parse(x, fp) {
	return parseJson(stripBom(x), path.relative(process.cwd(), fp));
}

module.exports = function (fp) {
	return pify(fs.readFile, Promise)(fp, 'utf8').then(function (data) {
		return parse(data, fp);
	});
};

module.exports.sync = function (fp) {
	return parse(fs.readFileSync(fp, 'utf8'), fp);
};
