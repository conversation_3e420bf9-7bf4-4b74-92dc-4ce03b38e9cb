{"name": "sort-keys", "version": "1.1.2", "description": "Sort the keys of an object", "license": "MIT", "repository": "sindresorhus/sort-keys", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha"}, "files": ["index.js"], "keywords": ["sort", "object", "keys", "obj", "key", "stable", "deterministic", "deep", "recursive", "recursively"], "dependencies": {"is-plain-obj": "^1.0.0"}, "devDependencies": {"mocha": "*", "xo": "*"}}