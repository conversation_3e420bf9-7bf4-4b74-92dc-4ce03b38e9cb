{"name": "mime-db", "description": "Media Type Database", "version": "1.42.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)", "<PERSON> <<EMAIL>> (http://github.com/broofa)"], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": "jshttp/mime-db", "devDependencies": {"bluebird": "3.5.5", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.4.6", "eslint": "6.4.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-node": "10.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "6.2.0", "nyc": "14.1.1", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}}