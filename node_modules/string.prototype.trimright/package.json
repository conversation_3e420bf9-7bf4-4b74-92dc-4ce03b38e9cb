{"name": "string.prototype.trimright", "version": "2.1.0", "author": "<PERSON>", "description": "ES7 spec-compliant String.prototype.trimRight shim.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run lint && es-shim-api --bound", "test": "npm run tests-only", "posttest": "npx aud", "tests-only": "npm run test:shimmed && npm run test:module", "test:shimmed": "node test/shimmed", "test:module": "node test", "coverage": "covert test/*.js", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.trimRight.git"}, "keywords": ["String.prototype.trimRight", "string", "ES7", "shim", "trim", "trimLeft", "trimRight", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "function-bind": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^14.1.0", "covert": "^1.1.1", "eslint": "^6.3.0", "functions-have-names": "^1.1.1", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}