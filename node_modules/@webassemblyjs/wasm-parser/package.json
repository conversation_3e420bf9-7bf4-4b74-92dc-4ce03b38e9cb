{"name": "@webassemblyjs/wasm-parser", "version": "1.8.5", "keywords": ["webassembly", "javascript", "ast", "parser", "wasm"], "description": "WebAssembly binary format parser", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-api-error": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/ieee754": "1.8.5", "@webassemblyjs/leb128": "1.8.5", "@webassemblyjs/utf8": "1.8.5"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-test-framework": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5", "mamacro": "^0.0.3", "wabt": "1.0.0-nightly.20180421"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}