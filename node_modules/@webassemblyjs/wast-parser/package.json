{"name": "@webassemblyjs/wast-parser", "version": "1.8.5", "description": "WebAssembly text format parser", "keywords": ["webassembly", "javascript", "ast", "parser", "wat", "wast"], "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/floating-point-hex-parser": "1.8.5", "@webassemblyjs/helper-api-error": "1.8.5", "@webassemblyjs/helper-code-frame": "1.8.5", "@webassemblyjs/helper-fsm": "1.8.5", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.5", "mamacro": "^0.0.3"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}