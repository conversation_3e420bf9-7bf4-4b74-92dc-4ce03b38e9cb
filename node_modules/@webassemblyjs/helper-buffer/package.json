{"name": "@webassemblyjs/helper-buffer", "version": "1.8.5", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.5", "jest-diff": "^22.4.0"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}