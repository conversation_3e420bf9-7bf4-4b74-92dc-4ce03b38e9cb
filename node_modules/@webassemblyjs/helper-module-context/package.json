{"name": "@webassemblyjs/helper-module-context", "version": "1.8.5", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "dependencies": {"@webassemblyjs/ast": "1.8.5", "mamacro": "^0.0.3"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.8.5"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}