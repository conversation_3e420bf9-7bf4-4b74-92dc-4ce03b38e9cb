{"name": "@webassemblyjs/helper-wasm-bytecode", "version": "1.8.5", "description": "WASM's Bytecode constants", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}