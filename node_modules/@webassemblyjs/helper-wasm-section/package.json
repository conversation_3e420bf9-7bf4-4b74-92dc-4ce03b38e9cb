{"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.5", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5"}, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.5"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}