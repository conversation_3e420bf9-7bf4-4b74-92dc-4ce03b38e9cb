{"name": "@webassemblyjs/ast", "version": "1.8.5", "description": "AST utils for webassemblyjs", "keywords": ["webassembly", "javascript", "ast"], "main": "lib/index.js", "module": "esm/index.js", "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/helper-module-context": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.5", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.3"}, "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49"}