{"name": "semver-regex", "version": "2.0.0", "description": "Regular expression for matching semver versions", "license": "MIT", "repository": "sindresorhus/semver-regex", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["semver", "version", "versions", "regex", "regexp", "re", "match", "matching", "semantic"], "devDependencies": {"ava": "*", "xo": "*"}}