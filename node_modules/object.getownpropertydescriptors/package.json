{"name": "object.getownpropertydescriptors", "version": "2.0.3", "author": "<PERSON>", "description": "ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint && es-shim-api", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "npm run --silent test:shimmed && npm run --silent test:module", "test:shimmed": "node test/shimmed.js", "test:module": "node test/index.js", "coverage": "covert test/*.js", "coverage:quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.getownpropertydescriptors.git"}, "keywords": ["Object.getOwnPropertyDescriptors", "descriptor", "property descriptor", "ES8", "ES2017", "shim", "polyfill", "getOwnPropertyDescriptor", "es-shim API"], "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.5.1"}, "devDependencies": {"tape": "^4.6.0", "covert": "^1.1.0", "jscs": "^3.0.7", "nsp": "^2.6.1", "eslint": "^3.1.1", "@ljharb/eslint-config": "^6.0.0", "semver": "^5.3.0", "replace": "^0.3.0", "@es-shims/api": "^1.2.0"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/5.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/12.0..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.8"}}