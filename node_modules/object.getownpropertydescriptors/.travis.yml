language: node_js
node_js:
  - "6.2"
  - "6.1"
  - "6.0"
  - "5.12"
  - "5.11"
  - "5.10"
  - "5.9"
  - "5.8"
  - "5.7"
  - "5.6"
  - "5.5"
  - "5.4"
  - "5.3"
  - "5.2"
  - "5.1"
  - "5.0"
  - "4.4"
  - "4.3"
  - "4.2"
  - "4.1"
  - "4.0"
  - "iojs-v3.3"
  - "iojs-v3.2"
  - "iojs-v3.1"
  - "iojs-v3.0"
  - "iojs-v2.5"
  - "iojs-v2.4"
  - "iojs-v2.3"
  - "iojs-v2.2"
  - "iojs-v2.1"
  - "iojs-v2.0"
  - "iojs-v1.8"
  - "iojs-v1.7"
  - "iojs-v1.6"
  - "iojs-v1.5"
  - "iojs-v1.4"
  - "iojs-v1.3"
  - "iojs-v1.2"
  - "iojs-v1.1"
  - "iojs-v1.0"
  - "0.12"
  - "0.11"
  - "0.10"
  - "0.9"
  - "0.8"
  - "0.6"
  - "0.4"
before_install:
  - 'if [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then case "$(npm --version)" in 1.*) npm install -g npm@1.4.28 ;; 2.*) npm install -g npm@2 ;; esac ; fi'
  - 'if [ "${TRAVIS_NODE_VERSION}" != "0.6" ] && [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then npm install -g npm; fi'
script:
  - 'if [ "${TRAVIS_NODE_VERSION}" != "4.4" ]; then npm run tests-only ; else npm test ; fi'
sudo: false
matrix:
  fast_finish: true
  allow_failures:
    - node_js: "6.1"
    - node_js: "6.0"
    - node_js: "5.11"
    - node_js: "5.10"
    - node_js: "5.9"
    - node_js: "5.8"
    - node_js: "5.7"
    - node_js: "5.6"
    - node_js: "5.5"
    - node_js: "5.4"
    - node_js: "5.3"
    - node_js: "5.2"
    - node_js: "5.1"
    - node_js: "5.0"
    - node_js: "4.3"
    - node_js: "4.2"
    - node_js: "4.1"
    - node_js: "4.0"
    - node_js: "iojs-v3.2"
    - node_js: "iojs-v3.1"
    - node_js: "iojs-v3.0"
    - node_js: "iojs-v2.4"
    - node_js: "iojs-v2.3"
    - node_js: "iojs-v2.2"
    - node_js: "iojs-v2.1"
    - node_js: "iojs-v2.0"
    - node_js: "iojs-v1.7"
    - node_js: "iojs-v1.6"
    - node_js: "iojs-v1.5"
    - node_js: "iojs-v1.4"
    - node_js: "iojs-v1.3"
    - node_js: "iojs-v1.2"
    - node_js: "iojs-v1.1"
    - node_js: "iojs-v1.0"
    - node_js: "0.11"
    - node_js: "0.9"
    - node_js: "0.6"
    - node_js: "0.4"
