{"name": "optipng-bin", "version": "5.1.0", "description": "OptiPNG wrapper that makes it seamlessly available as a local dependency", "license": "MIT", "repository": "imagemin/optipng-bin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, {"name": "<PERSON><PERSON><PERSON>", "url": "github.com/shinnn"}], "bin": {"optipng": "cli.js"}, "engines": {"node": ">=6"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava"}, "files": ["index.js", "cli.js", "lib"], "keywords": ["imagemin", "compress", "image", "img", "minify", "optimize", "png", "optipng"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0", "logalot": "^2.0.0"}, "devDependencies": {"ava": "*", "bin-check": "^4.0.1", "compare-size": "^3.0.0", "execa": "^1.0.0", "tempy": "^0.2.1", "xo": "*"}}