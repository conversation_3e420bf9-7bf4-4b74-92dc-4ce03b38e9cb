!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).URLParse=e()}}(function(){return function s(a,i,u){function f(t,e){if(!i[t]){if(!a[t]){var o="function"==typeof require&&require;if(!e&&o)return o(t,!0);if(p)return p(t,!0);var r=new Error("Cannot find module '"+t+"'");throw r.code="MODULE_NOT_FOUND",r}var n=i[t]={exports:{}};a[t][0].call(n.exports,function(e){return f(a[t][1][e]||e)},n,n.exports,s,a,i,u)}return i[t].exports}for(var p="function"==typeof require&&require,e=0;e<u.length;e++)f(u[e]);return f}({1:[function(e,r,t){(function(s){"use strict";var h=e("requires-port"),d=e("querystringify"),a=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,o=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\S\s]*)/i,t=new RegExp("^[\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF]+");function y(e){return(e||"").toString().replace(t,"")}var m=[["#","hash"],["?","query"],function(e){return e.replace("\\","/")},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d+)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],i={hash:1,query:1};function w(e){var t,o=("undefined"!=typeof window?window:void 0!==s?s:"undefined"!=typeof self?self:{}).location||{},r={},n=typeof(e=e||o);if("blob:"===e.protocol)r=new v(unescape(e.pathname),{});else if("string"===n)for(t in r=new v(e,{}),i)delete r[t];else if("object"===n){for(t in e)t in i||(r[t]=e[t]);void 0===r.slashes&&(r.slashes=a.test(e.href))}return r}function g(e){e=y(e);var t=o.exec(e);return{protocol:t[1]?t[1].toLowerCase():"",slashes:!!t[2],rest:t[3]}}function v(e,t,o){if(e=y(e),!(this instanceof v))return new v(e,t,o);var r,n,s,a,i,u,f=m.slice(),p=typeof t,c=this,l=0;for("object"!==p&&"string"!==p&&(o=t,t=null),o&&"function"!=typeof o&&(o=d.parse),t=w(t),r=!(n=g(e||"")).protocol&&!n.slashes,c.slashes=n.slashes||r&&t.slashes,c.protocol=n.protocol||t.protocol||"",e=n.rest,n.slashes||(f[3]=[/(.*)/,"pathname"]);l<f.length;l++)"function"!=typeof(a=f[l])?(s=a[0],u=a[1],s!=s?c[u]=e:"string"==typeof s?~(i=e.indexOf(s))&&("number"==typeof a[2]?(c[u]=e.slice(0,i),e=e.slice(i+a[2])):(c[u]=e.slice(i),e=e.slice(0,i))):(i=s.exec(e))&&(c[u]=i[1],e=e.slice(0,i.index)),c[u]=c[u]||r&&a[3]&&t[u]||"",a[4]&&(c[u]=c[u].toLowerCase())):e=a(e);o&&(c.query=o(c.query)),r&&t.slashes&&"/"!==c.pathname.charAt(0)&&(""!==c.pathname||""!==t.pathname)&&(c.pathname=function(e,t){if(""===e)return t;for(var o=(t||"/").split("/").slice(0,-1).concat(e.split("/")),r=o.length,n=o[r-1],s=!1,a=0;r--;)"."===o[r]?o.splice(r,1):".."===o[r]?(o.splice(r,1),a++):a&&(0===r&&(s=!0),o.splice(r,1),a--);return s&&o.unshift(""),"."!==n&&".."!==n||o.push(""),o.join("/")}(c.pathname,t.pathname)),h(c.port,c.protocol)||(c.host=c.hostname,c.port=""),c.username=c.password="",c.auth&&(a=c.auth.split(":"),c.username=a[0]||"",c.password=a[1]||""),c.origin=c.protocol&&c.host&&"file:"!==c.protocol?c.protocol+"//"+c.host:"null",c.href=c.toString()}v.prototype={set:function(e,t,o){var r=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(o||d.parse)(t)),r[e]=t;break;case"port":r[e]=t,h(t,r.protocol)?t&&(r.host=r.hostname+":"+t):(r.host=r.hostname,r[e]="");break;case"hostname":r[e]=t,r.port&&(t+=":"+r.port),r.host=t;break;case"host":r[e]=t,/:\d+$/.test(t)?(t=t.split(":"),r.port=t.pop(),r.hostname=t.join(":")):(r.hostname=t,r.port="");break;case"protocol":r.protocol=t.toLowerCase(),r.slashes=!o;break;case"pathname":case"hash":if(t){var n="pathname"===e?"/":"#";r[e]=t.charAt(0)!==n?n+t:t}else r[e]=t;break;default:r[e]=t}for(var s=0;s<m.length;s++){var a=m[s];a[4]&&(r[a[1]]=r[a[1]].toLowerCase())}return r.origin=r.protocol&&r.host&&"file:"!==r.protocol?r.protocol+"//"+r.host:"null",r.href=r.toString(),r},toString:function(e){e&&"function"==typeof e||(e=d.stringify);var t,o=this,r=o.protocol;r&&":"!==r.charAt(r.length-1)&&(r+=":");var n=r+(o.slashes?"//":"");return o.username&&(n+=o.username,o.password&&(n+=":"+o.password),n+="@"),n+=o.host+o.pathname,(t="object"==typeof o.query?e(o.query):o.query)&&(n+="?"!==t.charAt(0)?"?"+t:t),o.hash&&(n+=o.hash),n}},v.extractProtocol=g,v.location=w,v.trimLeft=y,v.qs=d,r.exports=v}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{querystringify:2,"requires-port":3}],2:[function(e,t,o){"use strict";var n=Object.prototype.hasOwnProperty;function a(e){return decodeURIComponent(e.replace(/\+/g," "))}o.stringify=function(e,t){t=t||"";var o=[];for(var r in"string"!=typeof t&&(t="?"),e)n.call(e,r)&&o.push(encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return o.length?t+o.join("&"):""},o.parse=function(e){for(var t,o=/([^=?&]+)=?([^&]*)/g,r={};t=o.exec(e);){var n=a(t[1]),s=a(t[2]);n in r||(r[n]=s)}return r}},{}],3:[function(e,t,o){"use strict";t.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},{}]},{},[1])(1)});