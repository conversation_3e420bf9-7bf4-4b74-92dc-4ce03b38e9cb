{"name": "mozjpeg", "version": "6.0.1", "description": "mozjpeg wrapper that makes it seamlessly available as a local dependency", "license": "MIT", "repository": "imagemin/mozjpeg-bin", "bin": "cli.js", "engines": {"node": ">=6"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava", "build-linux": "docker build --tag imagemin/mozjpeg docker && docker run --rm --volume $(pwd)/vendor/linux:/src/out imagemin/mozjpeg cp cjpeg /src/out"}, "files": ["index.js", "cli.js", "lib"], "keywords": ["imagemin", "jpeg", "jpg", "img", "image", "compress", "minify", "mozjpeg", "optimize"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0", "logalot": "^2.1.0"}, "devDependencies": {"ava": "*", "bin-check": "^4.1.0", "compare-size": "^3.0.0", "execa": "^1.0.0", "tempy": "^0.2.1", "xo": "*"}, "ava": {"serial": true}}