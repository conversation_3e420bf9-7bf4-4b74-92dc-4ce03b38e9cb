{
  name: "source-map-url",
  version: "0.4.0",
  author: "<PERSON>",
  license: "MIT",
  description: "Tools for working with sourceMappingURL comments.",
  keywords: [
    "source map",
    "sourceMappingURL",
    "comment",
    "annotation"
  ],
  main: "source-map-url.js",
  overlay: {
    npm: {
      repository: "lydell/source-map-url",
      scripts: {
        lint: "jshint source-map-url.js test/ ",
        unit: "mocha",
        test: "npm run lint && npm run unit"
      },
      devDependencies: {
        "mocha": "~1.17.1",
        "expect.js": "~0.3.1",
        "jshint": "~2.4.3"
      },
      testling: {
        harness: "mocha",
        files: "test/*.js",
        browsers: [
          "ie/8..latest",
          "chrome/latest",
          "firefox/latest",
          "opera/12",
          "opera/latest",
          "safari/5",
          "iphone/6",
          "android-browser/4"
        ]
      }
    },
    component: {
      repo: "lydell/source-map-url",
      scripts: [
        "source-map-url.js"
      ]
    },
    bower: {
      authors: ["<PERSON>"],
      ignore: [
        ".*"
      ]
    }
  }
}
