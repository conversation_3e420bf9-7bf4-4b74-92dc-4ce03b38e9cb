ol#qunit-tests {
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	margin:0;
	padding:0;
	list-style-position:inside;

	font-size: smaller;
}
ol#qunit-tests li{
	padding:0.4em 0.5em 0.4em 2.5em;
	border-bottom:1px solid #fff;
	font-size:small;
	list-style-position:inside;
}
ol#qunit-tests li ol{
	box-shadow: inset 0px 2px 13px #999;
	-moz-box-shadow: inset 0px 2px 13px #999;
	-webkit-box-shadow: inset 0px 2px 13px #999;
	margin-top:0.5em;
	margin-left:0;
	padding:0.5em;
	background-color:#fff;
	border-radius:15px;
	-moz-border-radius: 15px;
	-webkit-border-radius: 15px;
}
ol#qunit-tests li li{
	border-bottom:none;
	margin:0.5em;
	background-color:#fff;
	list-style-position: inside;
	padding:0.4em 0.5em 0.4em 0.5em;
}

ol#qunit-tests li li.pass{
	border-left:26px solid #C6E746;
	background-color:#fff;
	color:#5E740B;
	}
ol#qunit-tests li li.fail{
	border-left:26px solid #EE5757;
	background-color:#fff;
	color:#710909;
}
ol#qunit-tests li.pass{
	background-color:#D2E0E6;
	color:#528CE0;
}
ol#qunit-tests li.fail{
	background-color:#EE5757;
	color:#000;
}
ol#qunit-tests li strong {
	cursor:pointer;
}
h1#qunit-header{
	background-color:#0d3349;
	margin:0;
	padding:0.5em 0 0.5em 1em;
	color:#fff;
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	border-top-right-radius:15px;
	border-top-left-radius:15px;
	-moz-border-radius-topright:15px;
	-moz-border-radius-topleft:15px;
	-webkit-border-top-right-radius:15px;
	-webkit-border-top-left-radius:15px;
	text-shadow: rgba(0, 0, 0, 0.5) 4px 4px 1px;
}
h2#qunit-banner{
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	height:5px;
	margin:0;
	padding:0;
}
h2#qunit-banner.qunit-pass{
	background-color:#C6E746;
}
h2#qunit-banner.qunit-fail, #qunit-testrunner-toolbar {
	background-color:#EE5757;
}
#qunit-testrunner-toolbar {
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	padding:0;
	/*width:80%;*/
	padding:0em 0 0.5em 2em;
	font-size: small;
}
h2#qunit-userAgent {
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	background-color:#2b81af;
	margin:0;
	padding:0;
	color:#fff;
	font-size: small;
	padding:0.5em 0 0.5em 2.5em;
	text-shadow: rgba(0, 0, 0, 0.5) 2px 2px 1px;
}
p#qunit-testresult{
	font-family:"Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
	margin:0;
	font-size: small;
	color:#2b81af;
	border-bottom-right-radius:15px;
	border-bottom-left-radius:15px;
	-moz-border-radius-bottomright:15px;
	-moz-border-radius-bottomleft:15px;
	-webkit-border-bottom-right-radius:15px;
	-webkit-border-bottom-left-radius:15px;
	background-color:#D2E0E6;
	padding:0.5em 0.5em 0.5em 2.5em;
}
strong b.fail{
	color:#710909;
	}
strong b.pass{
	color:#5E740B;
	}