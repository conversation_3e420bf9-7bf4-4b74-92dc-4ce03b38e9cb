{"name": "commander", "version": "2.8.1", "description": "the complete solution for node.js command-line programs", "keywords": ["command", "option", "parser"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "devDependencies": {"should": ">= 0.0.1", "sinon": ">= 1.14.1"}, "scripts": {"test": "make test"}, "main": "index", "engines": {"node": ">= 0.6.x"}, "files": ["index.js"], "dependencies": {"graceful-readlink": ">= 1.0.0"}}