var tape = require('tape')
var toBuffer = require('./')

tape('buffer returns buffer', function (t) {
  t.same(to<PERSON><PERSON><PERSON>(<PERSON><PERSON><PERSON>('hi')), <PERSON><PERSON><PERSON>('hi'))
  t.end()
})

tape('string returns buffer', function (t) {
  t.same(to<PERSON><PERSON><PERSON>('hi'), <PERSON><PERSON><PERSON>('hi'))
  t.end()
})

tape('string + enc returns buffer', function (t) {
  t.same(to<PERSON><PERSON><PERSON>('6869', 'hex'), <PERSON><PERSON><PERSON>('hi'))
  t.end()
})

tape('other input throws', function (t) {
  try {
    toBuffer(42)
  } catch (err) {
    t.same(err.message, 'Input should be a buffer or a string')
    t.end()
  }
})
