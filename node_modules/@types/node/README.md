# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for Node.js (http://nodejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node.

### Additional Details
 * Last updated: Mon, 25 Nov 2019 22:58:16 GMT
 * Dependencies: none
 * Global values: `<PERSON>`, `NodeJS`, `Symbol`, `__dirname`, `__filename`, `clearImmediate`, `clearInterval`, `clearTimeout`, `console`, `exports`, `global`, `module`, `process`, `queueMicrotask`, `require`, `setImmediate`, `setInterval`, `setTimeout`

# Credits
These definitions were written by <PERSON> TypeScript (https://github.com/Microsoft), DefinitelyTyped (https://github.com/DefinitelyTyped), <PERSON> (https://github.com/jkomyno), <PERSON> (https://github.com/a-<PERSON><PERSON><PERSON><PERSON>), <PERSON><PERSON> (https://github.com/alvis), <PERSON> (https://github.com/r3nya), <PERSON> (https://github.com/btoueg), <PERSON>heufler (https://github.com/brunoscheufler), Chigozirim C. (https://github.com/smac89), Christian Vaagland Tellnes (https://github.com/tellnes), David Junger (https://github.com/touffy), Deividas Bakanas (https://github.com/DeividasBakanas), Eugene Y. Q. Shen (https://github.com/eyqs), Flarna (https://github.com/Flarna), Hannes Magnusson (https://github.com/Hannes-Magnusson-CK), Hoàng Văn Khải (https://github.com/KSXGitHub), Huw (https://github.com/hoo29), Kelvin Jin (https://github.com/kjin), Klaus Meinhardt (https://github.com/ajafff), Lishude (https://github.com/islishude), Mariusz Wiktorczyk (https://github.com/mwiktorczyk), Mohsen Azimi (https://github.com/mohsen1), Nicolas Even (https://github.com/n-e), Nicolas Voigt (https://github.com/octo-sniffle), Nikita Galkin (https://github.com/galkin), Parambir Singh (https://github.com/parambirs), Sebastian Silbermann (https://github.com/eps1lon), Simon Schick (https://github.com/SimonSchick), Thomas den Hollander (https://github.com/ThomasdenH), Wilco Bakker (https://github.com/WilcoBakker), wwwy3y3 (https://github.com/wwwy3y3), Zane Hannan AU (https://github.com/ZaneHannanAU), Samuel Ainsworth (https://github.com/samuela), Kyle Uehlein (https://github.com/kuehlein), Jordi Oliveras Rovira (https://github.com/j-oliveras), Thanik Bhongbhibhat (https://github.com/bhongy), Marcin Kopacz (https://github.com/chyzwar), Trivikram Kamat (https://github.com/trivikr), Minh Son Nguyen (https://github.com/nguymin4), Junxiao Shi (https://github.com/yoursunny), and Ilia Baryshnikov (https://github.com/qwelias).
