{"name": "@types/q", "version": "1.5.2", "description": "TypeScript definitions for Q", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/bnemetchek", "githubUsername": "bnemetchek"}, {"name": "<PERSON>", "url": "https://github.com/AndrewGaspar", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/mboudreau", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "TeamworkGuy2", "url": "https://github.com/TeamworkGuy2", "githubUsername": "TeamworkGuy2"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/q"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "831f89b03c9bf944abfea1d45425872fe7bc687a3ea0ee83f6bb2bb63af9dbc2", "typeScriptVersion": "2.3"}