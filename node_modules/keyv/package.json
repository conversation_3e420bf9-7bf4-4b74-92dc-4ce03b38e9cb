{"name": "keyv", "version": "3.0.0", "description": "Simple key-value storage with support for multiple backends", "main": "src/index.js", "scripts": {"test": "xo && nyc ava test/keyv.js", "test:full": "xo && nyc ava --serial", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/keyv.git"}, "keywords": ["key", "value", "store", "cache", "ttl"], "author": "<PERSON> <<EMAIL>> (http://lukechilds.co.uk)", "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "homepage": "https://github.com/lukechilds/keyv", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"ava": "^0.22.0", "coveralls": "^3.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/postgres": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/test-suite": "*", "nyc": "^11.0.3", "this": "^1.0.2", "timekeeper": "^2.0.0", "xo": "^0.19.0"}}