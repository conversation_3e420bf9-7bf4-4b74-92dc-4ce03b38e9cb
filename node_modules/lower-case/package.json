{"name": "lower-case", "version": "1.1.4", "description": "Lowercase a string", "main": "lower-case.js", "typings": "lower-case.d.ts", "files": ["lower-case.js", "lower-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "standard": {"ignore": ["coverage/**", "node_modules/**", "bower_components/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/lower-case.git"}, "keywords": ["cases", "lower", "lowercase", "case"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/lower-case/issues"}, "homepage": "https://github.com/blakeembrey/lower-case", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "pre-commit": "^1.0.2", "standard": "^2.4.5"}, "dependencies": {}}