{"name": "@babel/helper-function-name", "version": "7.7.4", "description": "Helper function to change the property 'name' of every function", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-function-name", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-get-function-arity": "^7.7.4", "@babel/template": "^7.7.4", "@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}