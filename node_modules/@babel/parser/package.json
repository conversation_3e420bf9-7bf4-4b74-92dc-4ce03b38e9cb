{"name": "@babel/parser", "version": "7.7.4", "description": "A JavaScript parser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-parser", "main": "lib/index.js", "types": "typings/babel-parser.d.ts", "files": ["bin", "lib", "typings"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3", "charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9"}, "bin": {"parser": "./bin/babel-parser.js"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}