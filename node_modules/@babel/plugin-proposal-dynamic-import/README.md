# @babel/plugin-proposal-dynamic-import

> Transform import() expressions

See our website [@babel/plugin-proposal-dynamic-import](https://babeljs.io/docs/en/next/babel-plugin-proposal-dynamic-import.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-dynamic-import
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-dynamic-import --dev
```
