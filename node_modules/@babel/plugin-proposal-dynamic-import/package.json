{"name": "@babel/plugin-proposal-dynamic-import", "version": "7.7.4", "description": "Transform import() expressions", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-dynamic-import", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}