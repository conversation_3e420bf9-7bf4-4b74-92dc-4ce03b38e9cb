{"name": "@babel/plugin-transform-dotall-regex", "version": "7.7.4", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}