{"name": "@babel/helper-simple-access", "version": "7.7.4", "description": "Babel helper for ensuring that access to a given value is performed through simple accesses", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-simple-access", "main": "lib/index.js", "dependencies": {"@babel/template": "^7.7.4", "@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}