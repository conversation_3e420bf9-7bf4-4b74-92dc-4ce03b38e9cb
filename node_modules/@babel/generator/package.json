{"name": "@babel/generator", "version": "7.7.4", "description": "Turns an AST into code.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-generator", "main": "lib/index.js", "files": ["lib"], "dependencies": {"@babel/types": "^7.7.4", "jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0"}, "devDependencies": {"@babel/helper-fixtures": "^7.6.3", "@babel/parser": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}