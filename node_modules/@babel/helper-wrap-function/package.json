{"name": "@babel/helper-wrap-function", "version": "7.7.4", "description": "Helper to wrap functions inside a function call.", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-function-name": "^7.7.4", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}