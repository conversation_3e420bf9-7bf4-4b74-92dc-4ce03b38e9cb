{"name": "@babel/helper-module-transforms", "version": "7.7.4", "description": "Babel helper functions for implementing ES6 module transformations", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "main": "lib/index.js", "dependencies": {"@babel/helper-module-imports": "^7.7.4", "@babel/helper-simple-access": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4", "@babel/template": "^7.7.4", "@babel/types": "^7.7.4", "lodash": "^4.17.13"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}