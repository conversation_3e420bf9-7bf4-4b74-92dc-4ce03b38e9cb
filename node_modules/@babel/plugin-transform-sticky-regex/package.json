{"name": "@babel/plugin-transform-sticky-regex", "version": "7.7.4", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-sticky-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}