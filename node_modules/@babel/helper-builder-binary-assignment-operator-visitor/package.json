{"name": "@babel/helper-builder-binary-assignment-operator-visitor", "version": "7.7.4", "description": "Helper function to build binary assignment operator visitors", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-builder-binary-assignment-operator-visitor", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-explode-assignable-expression": "^7.7.4", "@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}