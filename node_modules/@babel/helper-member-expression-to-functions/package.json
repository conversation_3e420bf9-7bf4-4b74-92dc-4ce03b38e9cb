{"name": "@babel/helper-member-expression-to-functions", "version": "7.7.4", "description": "Helper function to replace certain member expressions with function calls", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-member-expression-to-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}