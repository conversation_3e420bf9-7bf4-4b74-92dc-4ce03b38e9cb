{"name": "@babel/types", "version": "7.7.4", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-types", "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"esutils": "^2.0.2", "lodash": "^4.17.13", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "^7.7.4", "@babel/parser": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}