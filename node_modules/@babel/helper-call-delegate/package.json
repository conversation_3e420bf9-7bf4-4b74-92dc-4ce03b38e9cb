{"name": "@babel/helper-call-delegate", "version": "7.7.4", "description": "Helper function to call delegate", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-call-delegate", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-hoist-variables": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/types": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}