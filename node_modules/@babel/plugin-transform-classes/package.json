{"name": "@babel/plugin-transform-classes", "version": "7.7.4", "description": "Compile ES2015 classes to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-classes", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^7.7.4", "@babel/helper-define-map": "^7.7.4", "@babel/helper-function-name": "^7.7.4", "@babel/helper-optimise-call-expression": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4", "globals": "^11.1.0"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}