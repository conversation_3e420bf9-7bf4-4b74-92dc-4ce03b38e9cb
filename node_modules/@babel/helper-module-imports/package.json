{"name": "@babel/helper-module-imports", "version": "7.7.4", "description": "Babel helper functions for inserting module loads", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "main": "lib/index.js", "dependencies": {"@babel/types": "^7.7.4"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}