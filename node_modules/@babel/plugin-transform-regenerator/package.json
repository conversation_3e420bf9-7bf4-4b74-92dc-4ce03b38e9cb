{"name": "@babel/plugin-transform-regenerator", "author": "<PERSON> <<EMAIL>>", "description": "Explode async and generator functions into a state machine.", "version": "7.7.4", "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "main": "lib/index.js", "dependencies": {"regenerator-transform": "^0.14.0"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9"}