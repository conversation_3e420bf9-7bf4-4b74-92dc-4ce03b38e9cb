{"name": "selfsigned", "version": "1.10.7", "description": "Generate self signed certificates private and public keys", "main": "index.js", "scripts": {"test": "mocha -t 5000"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/selfsigned.git"}, "keywords": ["openssl", "self", "signed", "certificates"], "author": "<PERSON> <<EMAIL>> (http://joseoncode.com)", "contributors": [{"name": "<PERSON>", "email": "pao<PERSON>@async.ly", "url": "http://async.ly"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/bushong1"}], "license": "MIT", "dependencies": {"node-forge": "0.9.0"}, "devDependencies": {"mocha": "^5.1.1"}}