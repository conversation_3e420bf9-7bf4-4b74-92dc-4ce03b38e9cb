{"name": "trim-newlines", "version": "1.0.0", "description": "Trim newlines from the start and/or end of a string", "license": "MIT", "repository": "sindresorhus/trim-newlines", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["trim", "newline", "newlines", "linebreak", "lf", "crlf", "left", "right", "start", "end", "string", "str", "remove", "delete", "strip"], "devDependencies": {"ava": "*", "xo": "*"}}