!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("source-map")):"function"==typeof define&&define.amd?define(["exports","source-map"],t):t((e=e||self).Terser={},e.sourceMap)}(this,(function(e,t){"use strict";function n(e){return e.split("")}function i(e,t){return t.includes(e)}t=t&&t.hasOwnProperty("default")?t.default:t;class r extends Error{constructor(e,t){super(),this.name="DefaultsError",this.message=e,this.defs=t}}function o(e,t,n){!0===e&&(e={});var i=e||{};if(n)for(var o in i)if(D(i,o)&&!D(t,o))throw new r("`"+o+"` is not a supported option",t);for(var o in t)D(t,o)&&(i[o]=e&&D(e,o)?e[o]:t[o]);return i}function a(){}function s(){return!1}function u(){return!0}function c(){return this}function l(){return null}var f=function(){function e(e,o,a){var s,u=[],c=[];function l(){var l=o(e[s],s),f=l instanceof r;return f&&(l=l.v),l instanceof n?(l=l.v)instanceof i?c.push.apply(c,a?l.v.slice().reverse():l.v):c.push(l):l!==t&&(l instanceof i?u.push.apply(u,a?l.v.slice().reverse():l.v):u.push(l)),f}if(Array.isArray(e))if(a){for(s=e.length;--s>=0&&!l(););u.reverse(),c.reverse()}else for(s=0;s<e.length&&!l();++s);else for(s in e)if(D(e,s)&&l())break;return c.concat(u)}e.at_top=function(e){return new n(e)},e.splice=function(e){return new i(e)},e.last=function(e){return new r(e)};var t=e.skip={};function n(e){this.v=e}function i(e){this.v=e}function r(e){this.v=e}return e}();function p(e,t){e.includes(t)||e.push(t)}function _(e,t){return e.replace(/{(.+?)}/g,(function(e,n){return t&&t[n]}))}function d(e,t){for(var n=e.length;--n>=0;)e[n]===t&&e.splice(n,1)}function m(e,t){if(e.length<2)return e.slice();return function e(n){if(n.length<=1)return n;var i=Math.floor(n.length/2),r=n.slice(0,i),o=n.slice(i);return function(e,n){for(var i=[],r=0,o=0,a=0;r<e.length&&o<n.length;)t(e[r],n[o])<=0?i[a++]=e[r++]:i[a++]=n[o++];return r<e.length&&i.push.apply(i,e.slice(r)),o<n.length&&i.push.apply(i,n.slice(o)),i}(r=e(r),o=e(o))}(e)}function E(e){return Array.isArray(e)||(e=e.split(" ")),new Set(e)}function h(e,t,n){e.has(t)?e.get(t).push(n):e.set(t,[n])}function D(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function g(e,t){return!0===e||e instanceof RegExp&&e.test(t)}var S={"\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};function A(e){return e.replace(/[\n\r\u2028\u2029]/g,(function(t,n){return("\\"==e[n-1]&&("\\"!=e[n-2]||/(?:^|[^\\])(?:\\{2})*$/.test(e.slice(0,n-1)))?"":"\\")+S[t]}))}const v="gimuy";function T(e,t){return e._annotations&t}function b(e,t){e._annotations|=t}var y="break case catch class const continue debugger default delete do else export extends finally for function if in instanceof let new return switch throw try typeof var void while with",C="false null true",O="enum implements import interface package private protected public static super this "+C+" "+y,F="return new delete throw else case yield await";y=E(y),O=E(O),F=E(F),C=E(C);var M=E(n("+-*&%=<>!?|~^")),R=/[0-9a-f]/i,N=/^0x[0-9a-f]+$/i,w=/^0[0-7]+$/,x=/^0o[0-7]+$/i,k=/^0b[01]+$/i,I=/^\d*\.?\d*(?:e[+-]?\d*(?:\d\.?|\.?\d)\d*)?$/i,L=/^(0[xob])?[0-9a-f]+n$/i,V=E(["in","instanceof","typeof","new","void","delete","++","--","+","-","!","~","&","|","^","*","**","/","%",">>","<<",">>>","<",">","<=",">=","==","===","!=","!==","?","=","+=","-=","/=","*=","**=","%=",">>=","<<=",">>>=","|=","^=","&=","&&","||"]),P=E(n("  \n\r\t\f\v​           \u2028\u2029  　\ufeff")),B=E(n("\n\r\u2028\u2029")),K=E(n(";]),:")),U=E(n("[{(,;:")),G=E(n("[]{}(),;:")),H={ID_Start:/[A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,ID_Continue:/[0-9A-Z_a-z\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};function X(e,t){var n=e.charAt(t);if(z(n)){var i=e.charAt(t+1);if(W(i))return n+i}if(W(n)){var r=e.charAt(t-1);if(z(r))return r+n}return n}function z(e){return"string"==typeof e&&(e=e.charCodeAt(0)),e>=55296&&e<=56319}function W(e){return"string"==typeof e&&(e=e.charCodeAt(0)),e>=56320&&e<=57343}function Y(e){return e>=48&&e<=57}function q(e){var t=e.charCodeAt(0);return H.ID_Start.test(e)||36==t||95==t}function $(e){var t=e.charCodeAt(0);return H.ID_Continue.test(e)||36==t||95==t||8204==t||8205==t}function j(e){return/^[a-z_$][a-z0-9_$]*$/i.test(e)}function Z(e){if(N.test(e))return parseInt(e.substr(2),16);if(w.test(e))return parseInt(e.substr(1),8);if(x.test(e))return parseInt(e.substr(2),8);if(k.test(e))return parseInt(e.substr(2),2);if(I.test(e))return parseFloat(e);var t=parseFloat(e);return t==e?t:void 0}class J extends Error{constructor(e,t,n,i,r){super(),this.name="SyntaxError",this.message=e,this.filename=t,this.line=n,this.col=i,this.pos=r}}function Q(e,t,n,i,r){throw new J(e,t,n,i,r)}function ee(e,t,n){return e.type==t&&(null==n||e.value==n)}var te={};function ne(e,t,n,i){var r={text:e,filename:t,pos:0,tokpos:0,line:1,tokline:0,col:0,tokcol:0,newline_before:!1,regex_allowed:!1,brace_counter:0,template_braces:[],comments_before:[],directives:{},directive_stack:[]};function o(){return X(r.text,r.pos)}function a(e,t){var n=X(r.text,r.pos++);if(e&&!n)throw te;return B.has(n)?(r.newline_before=r.newline_before||!t,++r.line,r.col=0,"\r"==n&&"\n"==o()&&(++r.pos,n="\n")):(n.length>1&&(++r.pos,++r.col),++r.col),n}function s(e){for(;e-- >0;)a()}function u(e){return r.text.substr(r.pos,e.length)==e}function c(e,t){var n=r.text.indexOf(e,r.pos);if(t&&-1==n)throw te;return n}function l(){r.tokline=r.line,r.tokcol=r.col,r.tokpos=r.pos}var f=!1,p=null;function _(n,i,o){r.regex_allowed="operator"==n&&!re.has(i)||"keyword"==n&&F.has(i)||"punc"==n&&U.has(i)||"arrow"==n,"punc"==n&&"."==i?f=!0:o||(f=!1);var a={type:n,value:i,line:r.tokline,col:r.tokcol,pos:r.tokpos,endline:r.line,endcol:r.col,endpos:r.pos,nlb:r.newline_before,file:t};return/^(?:num|string|regexp)$/i.test(n)&&(a.raw=e.substring(a.pos,a.endpos)),o||(a.comments_before=r.comments_before,a.comments_after=r.comments_before=[]),r.newline_before=!1,a=new le(a),o||(p=a),a}function d(){for(;P.has(o());)a()}function m(e){Q(e,t,r.tokline,r.tokcol,r.tokpos)}function E(e){var t=!1,n=!1,i=!1,r="."==e,s=!1,u=function(e){for(var t,n="",i=0;(t=o())&&e(t,i++);)n+=a();return n}((function(o,a){if(s)return!1;switch(o.charCodeAt(0)){case 98:case 66:return i=!0;case 111:case 79:case 120:case 88:return!i&&(i=!0);case 101:case 69:return!!i||!t&&(t=n=!0);case 45:return n||0==a&&!e;case 43:return n;case n=!1,46:return!(r||i||t)&&(r=!0)}return"n"===o?(s=!0,!0):R.test(o)}));if(e&&(u=e+u),w.test(u)&&K.has_directive("use strict")&&m("Legacy octal literals are not allowed in strict mode"),u.endsWith("n")){const e=Z(u.slice(0,-1));if(!r&&L.test(u)&&!isNaN(e))return _("big_int",u.replace("n",""));m("Invalid or unexpected token")}var c=Z(u);if(!isNaN(c))return _("num",c);m("Invalid syntax: "+u)}function h(e){return e>="0"&&e<="7"}function D(e,t,n){var i,s=a(!0,e);switch(s.charCodeAt(0)){case 110:return"\n";case 114:return"\r";case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 120:return String.fromCharCode(g(2,t));case 117:if("{"==o()){for(a(!0),"}"===o()&&m("Expecting hex-character between {}");"0"==o();)a(!0);var u,l=c("}",!0)-r.pos;return(l>6||(u=g(l,t))>1114111)&&m("Unicode reference out of bounds"),a(!0),(i=u)>65535?(i-=65536,String.fromCharCode(55296+(i>>10))+String.fromCharCode(i%1024+56320)):String.fromCharCode(i)}return String.fromCharCode(g(4,t));case 10:return"";case 13:if("\n"==o())return a(!0,e),""}if(h(s)){if(n&&t){"0"===s&&!h(o())||m("Octal escape sequences are not allowed in template strings")}return function(e,t){var n=o();n>="0"&&n<="7"&&(e+=a(!0))[0]<="3"&&(n=o())>="0"&&n<="7"&&(e+=a(!0));if("0"===e)return"\0";e.length>0&&K.has_directive("use strict")&&t&&m("Legacy octal escape sequences are not allowed in strict mode");return String.fromCharCode(parseInt(e,8))}(s,t)}return s}function g(e,t){for(var n=0;e>0;--e){if(!t&&isNaN(parseInt(o(),16)))return parseInt(n,16)||"";var i=a(!0);isNaN(parseInt(i,16))&&m("Invalid hex-character pattern in string"),n+=i}return parseInt(n,16)}var S=I("Unterminated string constant",(function(){for(var e=a(),t="";;){var n=a(!0,!0);if("\\"==n)n=D(!0,!0);else if("\r"==n||"\n"==n)m("Unterminated string constant");else if(n==e)break;t+=n}var i=_("string",t);return i.quote=e,i})),A=I("Unterminated template",(function(e){e&&r.template_braces.push(r.brace_counter);var t,n,i="",s="";for(a(!0,!0);"`"!=(t=a(!0,!0));){if("\r"==t)"\n"==o()&&++r.pos,t="\n";else if("$"==t&&"{"==o())return a(!0,!0),r.brace_counter++,(n=_(e?"template_head":"template_substitution",i)).raw=s,n;if(s+=t,"\\"==t){var u=r.pos;t=D(!0,!(p&&("name"===p.type||"punc"===p.type&&(")"===p.value||"]"===p.value))),!0),s+=r.text.substr(u,r.pos-u)}i+=t}return r.template_braces.pop(),(n=_(e?"template_head":"template_substitution",i)).raw=s,n.end=!0,n}));function v(e){var t,n=r.regex_allowed,i=function(){for(var e=r.text,t=r.pos,n=r.text.length;t<n;++t){var i=e[t];if(B.has(i))return t}return-1}();return-1==i?(t=r.text.substr(r.pos),r.pos=r.text.length):(t=r.text.substring(r.pos,i),r.pos=i),r.col=r.tokcol+(r.pos-r.tokpos),r.comments_before.push(_(e,t,!0)),r.regex_allowed=n,K}var T=I("Unterminated multiline comment",(function(){var e=r.regex_allowed,t=c("*/",!0),n=r.text.substring(r.pos,t).replace(/\r\n|\r|\u2028|\u2029/g,"\n");return s(function(e){for(var t=0,n=0;n<e.length;n++)z(e.charCodeAt(n))&&W(e.charCodeAt(n+1))&&(t++,n++);return e.length-t}(n)+2),r.comments_before.push(_("comment2",n,!0)),r.newline_before=r.newline_before||n.includes("\n"),r.regex_allowed=e,K})),b=I("Unterminated identifier name",(function(){var e,t,n=!1,i=function(){return n=!0,a(),"u"!==o()&&m("Expecting UnicodeEscapeSequence -- uXXXX or u{XXXX}"),D(!1,!0)};if("\\"===(e=o()))q(e=i())||m("First identifier char is an invalid identifier char");else{if(!q(e))return"";a()}for(;null!=(t=o());){if("\\"===(t=o()))$(t=i())||m("Invalid escaped identifier char");else{if(!$(t))break;a()}e+=t}return O.has(e)&&n&&m("Escaped characters are not allowed in keywords"),e})),N=I("Unterminated regular expression",(function(e){for(var t,n=!1,i=!1;t=a(!0);)if(B.has(t))m("Unexpected line terminator");else if(n)e+="\\"+t,n=!1;else if("["==t)i=!0,e+=t;else if("]"==t&&i)i=!1,e+=t;else{if("/"==t&&!i)break;"\\"==t?n=!0:e+=t}return _("regexp",{source:e,flags:b()})}));function x(e){return _("operator",function e(t){if(!o())return t;var n=t+o();return V.has(n)?(a(),e(n)):t}(e||a()))}function k(){switch(a(),o()){case"/":return a(),v("comment1");case"*":return a(),T()}return r.regex_allowed?N(""):x("/")}function I(e,t){return function(n){try{return t(n)}catch(t){if(t!==te)throw t;m(e)}}}function K(e){if(null!=e)return N(e);for(i&&0==r.pos&&u("#!")&&(l(),s(2),v("comment5"));;){if(d(),l(),n){if(u("\x3c!--")){s(4),v("comment3");continue}if(u("--\x3e")&&r.newline_before){s(3),v("comment4");continue}}var t=o();if(!t)return _("eof");var c=t.charCodeAt(0);switch(c){case 34:case 39:return S();case 46:return a(),Y(o().charCodeAt(0))?E("."):"."===o()?(a(),a(),_("expand","...")):_("punc",".");case 47:var p=k();if(p===K)continue;return p;case 61:return a(),">"===o()?(a(),_("arrow","=>")):x("=");case 96:return A(!0);case 123:r.brace_counter++;break;case 125:if(r.brace_counter--,r.template_braces.length>0&&r.template_braces[r.template_braces.length-1]===r.brace_counter)return A(!1)}if(Y(c))return E();if(G.has(t))return _("punc",a());if(M.has(t))return x();if(92==c||q(t))return h=void 0,h=b(),f?_("name",h):C.has(h)?_("atom",h):y.has(h)?V.has(h)?_("operator",h):_("keyword",h):_("name",h);break}var h;m("Unexpected character '"+t+"'")}return K.next=a,K.peek=o,K.context=function(e){return e&&(r=e),r},K.add_directive=function(e){r.directive_stack[r.directive_stack.length-1].push(e),void 0===r.directives[e]?r.directives[e]=1:r.directives[e]++},K.push_directives_stack=function(){r.directive_stack.push([])},K.pop_directives_stack=function(){for(var e=r.directive_stack[r.directive_stack.length-1],t=0;t<e.length;t++)r.directives[e[t]]--;r.directive_stack.pop()},K.has_directive=function(e){return r.directives[e]>0},K}var ie=E(["typeof","void","delete","--","++","!","~","-","+"]),re=E(["--","++"]),oe=E(["=","+=","-=","/=","*=","**=","%=",">>=","<<=",">>>=","|=","^=","&="]),ae=function(e,t){for(var n=0;n<e.length;++n)for(var i=e[n],r=0;r<i.length;++r)t[i[r]]=n+1;return t}([["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]],{}),se=E(["atom","num","big_int","string","regexp","name"]);function ue(e,t){const n=new Map;t=o(t,{bare_returns:!1,ecma:8,expression:!1,filename:null,html5_comments:!0,module:!1,shebang:!0,strict:!1,toplevel:null},!0);var i={input:"string"==typeof e?ne(e,t.filename,t.html5_comments,t.shebang):e,token:null,prev:null,peeked:null,in_function:0,in_async:-1,in_generator:-1,in_directives:!0,in_loop:0,labels:[]};function r(e,t){return ee(i.token,e,t)}function a(){return i.peeked||(i.peeked=i.input())}function s(){return i.prev=i.token,i.peeked||a(),i.token=i.peeked,i.peeked=null,i.in_directives=i.in_directives&&("string"==i.token.type||r("punc",";")),i.token}function u(){return i.prev}function c(e,t,n,r){var o=i.input.context();Q(e,o.filename,null!=t?t:o.tokline,null!=n?n:o.tokcol,null!=r?r:o.tokpos)}function l(e,t){c(t,e.line,e.col)}function f(e){null==e&&(e=i.token),l(e,"Unexpected token: "+e.type+" ("+e.value+")")}function p(e,t){if(r(e,t))return s();l(i.token,"Unexpected token "+i.token.type+" «"+i.token.value+"», expected "+e+" «"+t+"»")}function _(e){return p("punc",e)}function d(e){return e.nlb||!e.comments_before.every(e=>!e.nlb)}function m(){return!t.strict&&(r("eof")||r("punc","}")||d(i.token))}function E(){return i.in_generator===i.in_function}function h(){return i.in_async===i.in_function}function D(e){r("punc",";")?s():e||m()||f()}function g(){_("(");var e=Je(!0);return _(")"),e}function S(e){return function(...t){const n=i.token,r=e(...t);return r.start=n,r.end=u(),r}}function A(){(r("operator","/")||r("operator","/="))&&(i.peeked=null,i.token=i.input(i.token.value.substr(1)))}i.token=s();var v=S((function(e,n,o){switch(A(),i.token.type){case"string":if(i.in_directives){var E=a();!i.token.raw.includes("\\")&&(ee(E,"punc",";")||ee(E,"punc","}")||d(E)||ee(E,"eof"))?i.input.add_directive(i.token.value):i.in_directives=!1}var S=i.in_directives,b=T();return S&&b.body instanceof an?new de(b.body):b;case"template_head":case"num":case"big_int":case"regexp":case"operator":case"atom":return T();case"name":if("async"==i.token.value&&ee(a(),"keyword","function"))return s(),s(),n&&c("functions are not allowed as the body of a loop"),F(Pe,!1,!0,e);if("import"==i.token.value&&!ee(a(),"punc","(")){s();var C=function(){var e,t,n=u();r("name")&&(e=le($t));r("punc",",")&&s();((t=J(!0))||e)&&p("name","from");var o=i.token;"string"!==o.type&&f();return s(),new ct({start:n,imported_name:e,imported_names:t,module_name:new an({start:o,value:o.value,quote:o.quote,end:o}),end:i.token})}();return D(),C}return ee(a(),"punc",":")?function(){var e=le(Zt);"await"===e.name&&h()&&l(i.prev,"await cannot be used as label inside async function");i.labels.some(t=>t.name===e.name)&&c("Label "+e.name+" defined twice");_(":"),i.labels.push(e);var t=v();i.labels.pop(),t instanceof Te||e.references.forEach((function(t){t instanceof $e&&(t=t.label.start,c("Continue label `"+e.name+"` refers to non-IterationStatement.",t.line,t.col,t.pos))}));return new ve({body:t,label:e})}():T();case"punc":switch(i.token.value){case"{":return new ge({start:i.token,body:x(),end:u()});case"[":case"(":return T();case";":return i.in_directives=!1,s(),new Se;default:f()}case"keyword":switch(i.token.value){case"break":return s(),y(qe);case"continue":return s(),y($e);case"debugger":return s(),D(),new _e;case"do":s();var O=Dt(v);p("keyword","while");var M=g();return D(!0),new ye({body:O,condition:M});case"while":return s(),new Ce({condition:g(),body:Dt((function(){return v(!1,!0)}))});case"for":return s(),function(){var e="`for await` invalid in this context",t=i.token;"name"==t.type&&"await"==t.value?(h()||l(t,e),s()):t=!1;_("(");var n=null;if(r("punc",";"))t&&l(t,e);else{n=r("keyword","var")?(s(),L(!0)):r("keyword","let")?(s(),V(!0)):r("keyword","const")?(s(),P(!0)):Je(!0,!0);var o=r("operator","in"),a=r("name","of");if(t&&!a&&l(t,e),o||a)return n instanceof rt?n.definitions.length>1&&l(n.start,"Only one variable declaration allowed in for..in loop"):He(n)||(n=Xe(n))instanceof Be||l(n.start,"Invalid left-hand side in for..in loop"),s(),o?function(e){var t=Je(!0);return _(")"),new Fe({init:e,object:t,body:Dt((function(){return v(!1,!0)}))})}(n):function(e,t){var n=e instanceof rt?e.definitions[0].name:null,i=Je(!0);return _(")"),new Me({await:t,init:e,name:n,object:i,body:Dt((function(){return v(!1,!0)}))})}(n,!!t)}return function(e){_(";");var t=r("punc",";")?null:Je(!0);_(";");var n=r("punc",")")?null:Je(!0);return _(")"),new Oe({init:e,condition:t,step:n,body:Dt((function(){return v(!1,!0)}))})}(n)}();case"class":return s(),n&&c("classes are not allowed as the body of a loop"),o&&c("classes are not allowed as the body of an if"),q(xt);case"function":return s(),n&&c("functions are not allowed as the body of a loop"),F(Pe,!1,!1,e);case"if":return s(),function(){var e=g(),t=v(!1,!1,!0),n=null;r("keyword","else")&&(s(),n=v(!1,!1,!0));return new je({condition:e,body:t,alternative:n})}();case"return":0!=i.in_function||t.bare_returns||c("'return' outside of function"),s();var N=null;return r("punc",";")?s():m()||(N=Je(!0),D()),new ze({value:N});case"switch":return s(),new Ze({expression:g(),body:Dt(k)});case"throw":s(),d(i.token)&&c("Illegal newline after 'throw'");N=Je(!0);return D(),new We({value:N});case"try":return s(),function(){var e=x(),t=null,n=null;if(r("keyword","catch")){var o=i.token;if(s(),r("punc","{"))var a=null;else{_("(");a=R(void 0,qt);_(")")}t=new nt({start:o,argname:a,body:x(),end:u()})}if(r("keyword","finally")){o=i.token;s(),n=new it({start:o,body:x(),end:u()})}t||n||c("Missing catch/finally blocks");return new tt({body:e,bcatch:t,bfinally:n})}();case"var":s();C=L();return D(),C;case"let":s();C=V();return D(),C;case"const":s();C=P();return D(),C;case"with":return i.input.has_directive("use strict")&&c("Strict mode may not include a with statement"),s(),new Re({expression:g(),body:v()});case"export":if(!ee(a(),"punc","(")){s();C=function(){var e,t,n,o,c,l=i.token;if(r("keyword","default"))e=!0,s();else if(t=J(!1)){if(r("name","from")){s();var p=i.token;return"string"!==p.type&&f(),s(),new lt({start:l,is_default:e,exported_names:t,module_name:new an({start:p,value:p.value,quote:p.quote,end:p}),end:u()})}return new lt({start:l,is_default:e,exported_names:t,end:u()})}r("punc","{")||e&&(r("keyword","class")||r("keyword","function"))&&ee(a(),"punc")?(o=Je(!1),D()):(n=v(e))instanceof rt&&e?f(n.start):n instanceof rt||n instanceof ke||n instanceof xt?c=n:n instanceof me?o=n.body:f(n.start);return new lt({start:l,is_default:e,exported_value:o,exported_definition:c,end:u()})}();return r("punc",";")&&D(),C}}}f()}));function T(e){return new me({body:(e=Je(!0),D(),e)})}function y(e){var t,n=null;m()||(n=le(tn,!0)),null!=n?((t=i.labels.find(e=>e.name===n.name))||c("Undefined label "+n.name),n.thedef=t):0==i.in_loop&&c(e.TYPE+" not inside a loop or switch"),D();var r=new e({label:n});return t&&t.references.push(r),r}var C=function(e,t,n){d(i.token)&&c("Unexpected newline before arrow (=>)"),p("arrow","=>");var o=w(r("punc","{"),!1,n),a=o instanceof Array&&o.length?o[o.length-1].end:o instanceof Array?e:o.end;return new Ve({start:e,end:a,async:n,argnames:t,body:o})},F=function(e,t,n,i){var o=e===Pe,a=r("operator","*");a&&s();var c=r("name")?le(o?Ht:zt):null;o&&!c&&(i?e=Le:f()),!c||e===Ie||c instanceof Vt||f(u());var l=[],p=w(!0,a||t,n,c,l);return new e({start:l.start,end:p.end,is_generator:a,async:n,name:c,argnames:l,body:p})};function M(e,t){var n=new Set,i=!1,r=!1,o=!1,a=!!t,s={add_parameter:function(t){if(n.has(t.value))!1===i&&(i=t),s.check_strict();else if(n.add(t.value),e)switch(t.value){case"arguments":case"eval":case"yield":a&&l(t,"Unexpected "+t.value+" identifier as parameter inside strict mode");break;default:O.has(t.value)&&f()}},mark_default_assignment:function(e){!1===r&&(r=e)},mark_spread:function(e){!1===o&&(o=e)},mark_strict_mode:function(){a=!0},is_strict:function(){return!1!==r||!1!==o||a},check_strict:function(){s.is_strict()&&!1!==i&&l(i,"Parameter "+i.value+" was used already")}};return s}function R(e,t){var n,o=!1;return void 0===e&&(e=M(!0,i.input.has_directive("use strict"))),r("expand","...")&&(o=i.token,e.mark_spread(i.token),s()),n=N(e,t),r("operator","=")&&!1===o&&(e.mark_default_assignment(i.token),s(),n=new bt({start:n.start,left:n,operator:"=",right:Je(!1),end:i.token})),!1!==o&&(r("punc",")")||f(),n=new xe({start:o,expression:n,end:o})),e.check_strict(),n}function N(e,t){var n,o=[],l=!0,p=!1,d=i.token;if(void 0===e&&(e=M(!1,i.input.has_directive("use strict"))),t=void 0===t?Gt:t,r("punc","[")){for(s();!r("punc","]");){if(l?l=!1:_(","),r("expand","...")&&(p=!0,n=i.token,e.mark_spread(i.token),s()),r("punc"))switch(i.token.value){case",":o.push(new dn({start:i.token,end:i.token}));continue;case"]":break;case"[":case"{":o.push(N(e,t));break;default:f()}else r("name")?(e.add_parameter(i.token),o.push(le(t))):c("Invalid function parameter");r("operator","=")&&!1===p&&(e.mark_default_assignment(i.token),s(),o[o.length-1]=new bt({start:o[o.length-1].start,left:o[o.length-1],operator:"=",right:Je(!1),end:i.token})),p&&(r("punc","]")||c("Rest element must be last element"),o[o.length-1]=new xe({start:n,expression:o[o.length-1],end:n}))}return _("]"),e.check_strict(),new Be({start:d,names:o,is_array:!0,end:u()})}if(r("punc","{")){for(s();!r("punc","}");){if(l?l=!1:_(","),r("expand","...")&&(p=!0,n=i.token,e.mark_spread(i.token),s()),r("name")&&(ee(a(),"punc")||ee(a(),"operator"))&&[",","}","="].includes(a().value)){e.add_parameter(i.token);var m=u(),E=le(t);p?o.push(new xe({start:n,expression:E,end:E.end})):o.push(new Ft({start:m,key:E.name,value:E,end:E.end}))}else{if(r("punc","}"))continue;var h=i.token,D=te();null===D?f(u()):"name"!==u().type||r("punc",":")?(_(":"),o.push(new Ft({start:h,quote:h.quote,key:D,value:N(e,t),end:u()}))):o.push(new Ft({start:u(),key:D,value:new t({start:u(),name:D,end:u()}),end:u()}))}p?r("punc","}")||c("Rest element must be last element"):r("operator","=")&&(e.mark_default_assignment(i.token),s(),o[o.length-1].value=new bt({start:o[o.length-1].value.start,left:o[o.length-1].value,operator:"=",right:Je(!1),end:i.token}))}return _("}"),e.check_strict(),new Be({start:d,names:o,is_array:!1,end:u()})}if(r("name"))return e.add_parameter(i.token),le(t);c("Invalid function parameter")}function w(e,n,o,a,u){var c=i.in_loop,l=i.labels,p=i.in_generator,d=i.in_async;if(++i.in_function,n&&(i.in_generator=i.in_function),o&&(i.in_async=i.in_function),u&&function(e){var n=M(!0,i.input.has_directive("use strict"));for(_("(");!r("punc",")");){var o=R(n);if(e.push(o),r("punc",")")||(_(","),r("punc",")")&&t.ecma<8&&f()),o instanceof xe)break}s()}(u),e&&(i.in_directives=!0),i.in_loop=0,i.labels=[],e){i.input.push_directives_stack();var m=x();a&&ce(a),u&&u.forEach(ce),i.input.pop_directives_stack()}else m=Je(!1);return--i.in_function,i.in_loop=c,i.labels=l,i.in_generator=p,i.in_async=d,m}function x(){_("{");for(var e=[];!r("punc","}");)r("eof")&&f(),e.push(v());return s(),e}function k(){_("{");for(var e,t=[],n=null,o=null;!r("punc","}");)r("eof")&&f(),r("keyword","case")?(o&&(o.end=u()),n=[],o=new et({start:(e=i.token,s(),e),expression:Je(!0),body:n}),t.push(o),_(":")):r("keyword","default")?(o&&(o.end=u()),n=[],o=new Qe({start:(e=i.token,s(),_(":"),e),body:n}),t.push(o)):(n||f(),n.push(v()));return o&&(o.end=u()),s(),t}function I(e,t){for(var n,o=[];;){var a="var"===t?Pt:"const"===t?Kt:"let"===t?Ut:null;if(r("punc","{")||r("punc","[")?n=new ft({start:i.token,name:N(void 0,a),value:r("operator","=")?(p("operator","="),Je(!1,e)):null,end:u()}):"import"==(n=new ft({start:i.token,name:le(a),value:r("operator","=")?(s(),Je(!1,e)):e||"const"!==t?null:c("Missing initializer in const declaration"),end:u()})).name.name&&c("Unexpected token: import"),o.push(n),!r("punc",","))break;s()}return o}var L=function(e){return new ot({start:u(),definitions:I(e,"var"),end:u()})},V=function(e){return new at({start:u(),definitions:I(e,"let"),end:u()})},P=function(e){return new st({start:u(),definitions:I(e,"const"),end:u()})};function B(){var e,t=i.token;switch(t.type){case"name":e=ue(Jt);break;case"num":e=new sn({start:t,end:t,value:t.value});break;case"big_int":e=new un({start:t,end:t,value:t.value});break;case"string":e=new an({start:t,end:t,value:t.value,quote:t.quote});break;case"regexp":e=new cn({start:t,end:t,value:t.value});break;case"atom":switch(t.value){case"false":e=new hn({start:t,end:t});break;case"true":e=new Dn({start:t,end:t});break;case"null":e=new fn({start:t,end:t})}}return s(),e}function U(e,t,n,i){var r=function(e,t){return t?new bt({start:e.start,left:e,operator:"=",right:t,end:t.end}):e};return e instanceof Ct?r(new Be({start:e.start,end:e.end,is_array:!1,names:e.properties.map(U)}),i):e instanceof Ft?(e.value=U(e.value,0,[e.key]),r(e,i)):e instanceof dn?e:e instanceof Be?(e.names=e.names.map(U),r(e,i)):e instanceof Jt?r(new Gt({name:e.name,start:e.start,end:e.end}),i):e instanceof xe?(e.expression=U(e.expression),r(e,i)):e instanceof yt?r(new Be({start:e.start,end:e.end,is_array:!0,names:e.elements.map(U)}),i):e instanceof Tt?r(U(e.left,void 0,void 0,e.right),i):e instanceof bt?(e.left=U(e.left,0,[e.left]),e):void c("Invalid function parameter",e.start.line,e.start.col)}var G=function(e,o){if(r("operator","new"))return function(e){var n=i.token;if(p("operator","new"),r("punc","."))return s(),p("name","target"),Ee(new Lt({start:n,end:u()}),e);var o,a=G(!1);r("punc","(")?(s(),o=X(")",t.ecma>=8)):o=[];var c=new _t({start:n,expression:a,args:o,end:u()});return pe(c),Ee(c,e)}(e);var c,l=i.token,d=r("name","async")&&"["!=(c=a()).value&&"arrow"!=c.type&&B();if(r("punc")){switch(i.token.value){case"(":if(d&&!e)break;var m=function(e,n){var o,a,c,l=[];for(_("(");!r("punc",")");)o&&f(o),r("expand","...")?(o=i.token,n&&(a=i.token),s(),l.push(new xe({start:u(),expression:Je(),end:i.token}))):l.push(Je()),r("punc",")")||(_(","),r("punc",")")&&(t.ecma<8&&f(),c=u(),n&&(a=c)));return _(")"),e&&r("arrow","=>")?o&&c&&f(c):a&&f(a),l}(o,!d);if(o&&r("arrow","=>"))return C(l,m.map(U),!!d);var E=d?new pt({expression:d,args:m}):1==m.length?m[0]:new dt({expressions:m});if(E.start){const e=l.comments_before.length;if(n.set(l,e),E.start.comments_before.unshift(...l.comments_before),l.comments_before=E.start.comments_before,0==e&&l.comments_before.length>0){var h=l.comments_before[0];h.nlb||(h.nlb=l.nlb,l.nlb=!1)}l.comments_after=E.start.comments_after}E.start=l;var D=u();return E.end&&(D.comments_before=E.end.comments_before,E.end.comments_after.push(...D.comments_after),D.comments_after=E.end.comments_after),E.end=D,E instanceof pt&&pe(E),Ee(E,e);case"[":return Ee(z(),e);case"{":return Ee(Y(),e)}d||f()}if(o&&r("name")&&ee(a(),"arrow")){var g=new Gt({name:i.token.value,start:l,end:l});return s(),C(l,[g],!!d)}if(r("keyword","function")){s();var S=F(Le,!1,!!d);return S.start=l,S.end=u(),Ee(S,e)}if(d)return Ee(d,e);if(r("keyword","class")){s();var A=q(kt);return A.start=l,A.end=u(),Ee(A,e)}return r("template_head")?Ee(H(),e):se.has(i.token.type)?Ee(B(),e):void f()};function H(e){var t=[],n=i.token;for(t.push(new Ge({start:i.token,raw:i.token.raw,value:i.token.value,end:i.token}));!i.token.end;)s(),A(),t.push(Je(!0)),ee("template_substitution")||f(),t.push(new Ge({start:i.token,raw:i.token.raw,value:i.token.value,end:i.token}));return s(),new Ue({start:n,segments:t,end:i.token})}function X(e,t,n){for(var o=!0,a=[];!r("punc",e)&&(o?o=!1:_(","),!t||!r("punc",e));)r("punc",",")&&n?a.push(new dn({start:i.token,end:i.token})):r("expand","...")?(s(),a.push(new xe({start:u(),expression:Je(),end:i.token}))):a.push(Je(!1));return s(),a}var z=S((function(){return _("["),new yt({elements:X("]",!t.strict,!0)})})),W=S((e,t)=>F(Ie,e,t)),Y=S((function(){var e=i.token,n=!0,o=[];for(_("{");!r("punc","}")&&(n?n=!1:_(","),t.strict||!r("punc","}"));)if("expand"!=(e=i.token).type){var a,c=te();if(r("punc",":"))null===c?f(u()):(s(),a=Je(!1));else{var l=$(c,e);if(l){o.push(l);continue}a=new Jt({start:u(),name:c,end:u()})}r("operator","=")&&(s(),a=new Tt({start:e,left:a,operator:"=",right:Je(!1),end:u()})),o.push(new Ft({start:e,quote:e.quote,key:c instanceof fe?c:""+c,value:a,end:u()}))}else s(),o.push(new xe({start:e,expression:Je(!1),end:u()}));return s(),new Ct({properties:o})}));function q(e){var t,n,o,a,c=[];for(i.input.push_directives_stack(),i.input.add_directive("use strict"),"name"==i.token.type&&"extends"!=i.token.value&&(o=le(e===xt?Wt:Yt)),e!==xt||o||f(),"extends"==i.token.value&&(s(),a=Je(!0)),_("{");r("punc",";");)s();for(;!r("punc","}");)for(t=i.token,(n=$(te(),t,!0))||f(),c.push(n);r("punc",";");)s();return i.input.pop_directives_stack(),s(),new e({start:t,name:o,extends:a,properties:c,end:u()})}function $(e,t,n){var o=function(e,t){return"string"==typeof e||"number"==typeof e?new Xt({start:t,name:""+e,end:u()}):(null===e&&f(),e)},a=!1,s=!1,c=!1,l=t;if(n&&"static"===e&&!r("punc","(")&&(s=!0,l=i.token,e=te()),"async"!==e||r("punc","(")||r("punc",",")||r("punc","}")||r("operator","=")||(a=!0,l=i.token,e=te()),null===e&&(c=!0,l=i.token,null===(e=te())&&f()),r("punc","("))return e=o(e,t),new Nt({start:t,static:s,is_generator:c,async:a,key:e,quote:e instanceof Xt?l.quote:void 0,value:W(c,a),end:u()});if(l=i.token,"get"==e){if(!r("punc")||r("punc","["))return e=o(te(),t),new Rt({start:t,static:s,key:e,quote:e instanceof Xt?l.quote:void 0,value:W(),end:u()})}else if("set"==e&&(!r("punc")||r("punc","[")))return e=o(te(),t),new Mt({start:t,static:s,key:e,quote:e instanceof Xt?l.quote:void 0,value:W(),end:u()})}function j(e){function t(e){return new e({name:te(),start:u(),end:u()})}var n,o,a=e?jt:en,c=e?$t:Qt,l=i.token;return e?n=t(a):o=t(c),r("name","as")?(s(),e?o=t(c):n=t(a)):e?o=new c(n):n=new a(o),new ut({start:l,foreign_name:n,name:o,end:u()})}function Z(e,t){var n,r=e?jt:en,o=e?$t:Qt,a=i.token,s=u();return t=t||new o({name:"*",start:a,end:s}),n=new r({name:"*",start:a,end:s}),new ut({start:a,foreign_name:n,name:t,end:s})}function J(e){var t;if(r("punc","{")){for(s(),t=[];!r("punc","}");)t.push(j(e)),r("punc",",")&&s();s()}else if(r("operator","*")){var n;s(),e&&r("name","as")&&(s(),n=le(e?$t:en)),t=[Z(e,n)]}return t}function te(){var e=i.token;switch(e.type){case"punc":if("["===e.value){s();var t=Je(!1);return _("]"),t}f(e);case"operator":if("*"===e.value)return s(),null;["delete","in","instanceof","new","typeof","void"].includes(e.value)||f(e);case"name":"yield"==e.value&&(E()?l(e,"Yield cannot be used as identifier inside generators"):ee(a(),"punc",":")||ee(a(),"punc","(")||!i.input.has_directive("use strict")||l(e,"Unexpected yield identifier inside strict mode"));case"string":case"num":case"big_int":case"keyword":case"atom":return s(),e.value;default:f(e)}}function ue(e){var t=i.token.value;return new("this"==t?nn:"super"==t?rn:e)({name:String(t),start:i.token,end:i.token})}function ce(e){var t=e.name;E()&&"yield"==t&&l(e.start,"Yield cannot be used as identifier inside generators"),i.input.has_directive("use strict")&&("yield"==t&&l(e.start,"Unexpected yield identifier inside strict mode"),e instanceof Vt&&("arguments"==t||"eval"==t)&&l(e.start,"Unexpected "+t+" in strict mode"))}function le(e,t){if(!r("name"))return t||c("Name expected"),null;var n=ue(e);return ce(n),s(),n}function pe(e){var t=e.start,i=t.comments_before;const r=n.get(t);for(var o=null!=r?r:i.length;--o>=0;){var a=i[o];if(/[@#]__/.test(a.value)){if(/[@#]__PURE__/.test(a.value)){b(e,Tn);break}if(/[@#]__INLINE__/.test(a.value)){b(e,bn);break}if(/[@#]__NOINLINE__/.test(a.value)){b(e,yn);break}}}}var Ee=function(e,t){var n,o=e.start;if(r("punc","."))return s(),Ee(new Et({start:o,expression:e,property:(n=i.token,"name"!=n.type&&f(),s(),n.value),end:u()}),t);if(r("punc","[")){s();var a=Je(!0);return _("]"),Ee(new ht({start:o,expression:e,property:a,end:u()}),t)}if(t&&r("punc","(")){s();var c=new pt({start:o,expression:e,args:he(),end:u()});return pe(c),Ee(c,!0)}return r("template_head")?Ee(new Ke({start:o,prefix:e,template_string:H(),end:u()}),t):e};function he(){for(var e=[];!r("punc",")");)r("expand","...")?(s(),e.push(new xe({start:u(),expression:Je(!1),end:u()}))):e.push(Je(!1)),r("punc",")")||(_(","),r("punc",")")&&t.ecma<8&&f());return s(),e}var De=function(e,t){var n=i.token;if("name"==n.type&&"await"==n.value){if(h())return s(),h()||c("Unexpected await expression outside async function",i.prev.line,i.prev.col,i.prev.pos),new gn({start:u(),end:i.token,expression:De(!0)});i.input.has_directive("use strict")&&l(i.token,"Unexpected await identifier inside strict mode")}if(r("operator")&&ie.has(n.value)){s(),A();var o=Ae(gt,n,De(e));return o.start=n,o.end=u(),o}for(var a=G(e,t);r("operator")&&re.has(i.token.value)&&!d(i.token);)a instanceof Ve&&f(),(a=Ae(St,i.token,a)).start=n,a.end=i.token,s();return a};function Ae(e,t,n){var r=t.value;switch(r){case"++":case"--":He(n)||c("Invalid use of "+r+" operator",t.line,t.col,t.pos);break;case"delete":n instanceof Jt&&i.input.has_directive("use strict")&&c("Calling delete on expression not allowed in strict mode",n.start.line,n.start.col,n.start.pos)}return new e({operator:r,expression:n})}var be=function(e,t,n){var o=r("operator")?i.token.value:null;"in"==o&&n&&(o=null),"**"==o&&e instanceof gt&&!ee(e.start,"punc","(")&&"--"!==e.operator&&"++"!==e.operator&&f(e.start);var a=null!=o?ae[o]:null;if(null!=a&&(a>t||"**"===o&&t===a)){s();var u=be(De(!0),a,n);return be(new At({start:e.start,left:e,operator:o,right:u,end:u.end}),t,n)}return e};var Ne=function(e){var t=i.token,n=function(e){return be(De(!0,!0),0,e)}(e);if(r("operator","?")){s();var o=Je(!1);return _(":"),new vt({start:t,condition:n,consequent:o,alternative:Je(!1,e),end:u()})}return n};function He(e){return e instanceof mt||e instanceof Jt}function Xe(e){if(e instanceof Ct)e=new Be({start:e.start,names:e.properties.map(Xe),is_array:!1,end:e.end});else if(e instanceof yt){for(var t=[],n=0;n<e.elements.length;n++)e.elements[n]instanceof xe&&(n+1!==e.elements.length&&l(e.elements[n].start,"Spread must the be last element in destructuring array"),e.elements[n].expression=Xe(e.elements[n].expression)),t.push(Xe(e.elements[n]));e=new Be({start:e.start,names:t,is_array:!0,end:e.end})}else e instanceof Ot?e.value=Xe(e.value):e instanceof Tt&&(e=new bt({start:e.start,left:e.left,operator:"=",right:e.right,end:e.end}));return e}var Ye=function(e){A();var t=i.token;if("name"==t.type&&"yield"==t.value){if(E())return s(),function(){E()||c("Unexpected yield expression outside generator function",i.prev.line,i.prev.col,i.prev.pos);var e=i.token,t=!1,n=!0;return m()||r("punc")&&K.has(i.token.value)?n=!1:r("operator","*")&&(t=!0,s()),new Sn({start:e,is_star:t,expression:n?Je():null,end:u()})}();i.input.has_directive("use strict")&&l(i.token,"Unexpected yield identifier inside strict mode")}var n=Ne(e),o=i.token.value;if(r("operator")&&oe.has(o)){if(He(n)||(n=Xe(n))instanceof Be)return s(),new Tt({start:t,left:n,operator:o,right:Ye(e),end:u()});c("Invalid assignment")}return n},Je=function(e,t){for(var n=i.token,o=[];o.push(Ye(t)),e&&r("punc",",");)s(),e=!0;return 1==o.length?o[0]:new dt({start:n,expressions:o,end:a()})};function Dt(e){++i.in_loop;var t=e();return--i.in_loop,t}return t.expression?Je(!0):function(){var e=i.token,n=[];for(i.input.push_directives_stack(),t.module&&i.input.add_directive("use strict");!r("eof");)n.push(v());i.input.pop_directives_stack();var o=u(),a=t.toplevel;return a?(a.body=a.body.concat(n),a.end=o):a=new we({start:e,body:n,end:o}),a}()}function ce(e,t,n,i=fe){var r=t=t?t.split(/\s+/):[];i&&i.PROPS&&(t=t.concat(i.PROPS));for(var o="return function AST_"+e+"(props){ if (props) { ",a=t.length;--a>=0;)o+="this."+t[a]+" = props."+t[a]+";";const s=i&&Object.create(i.prototype);(s&&s.initialize||n&&n.initialize)&&(o+="this.initialize();"),o+="}",o+="this.flags = 0;",o+="}";var u=new Function(o)();if(s&&(u.prototype=s,u.BASE=i),i&&i.SUBCLASSES.push(u),u.prototype.CTOR=u,u.PROPS=t||null,u.SELF_PROPS=r,u.SUBCLASSES=[],e&&(u.prototype.TYPE=u.TYPE=e),n)for(a in n)D(n,a)&&("$"===a[0]?u[a.substr(1)]=n[a]:u.prototype[a]=n[a]);return u.DEFMETHOD=function(e,t){this.prototype[e]=t},u}var le=ce("Token","type value line col pos endline endcol endpos nlb comments_before comments_after file raw quote end",{},null),fe=ce("Node","start end",{_clone:function(e){if(e){var t=this.clone();return t.transform(new vn((function(e){if(e!==t)return e.clone(!0)})))}return new this.CTOR(this)},clone:function(e){return this._clone(e)},$documentation:"Base class of all AST nodes",$propdoc:{start:"[AST_Token] The first token of this node",end:"[AST_Token] The last token of this node"},_walk:function(e){return e._visit(this)},walk:function(e){return this._walk(e)}},null);fe.warn_function=null,fe.warn=function(e,t){fe.warn_function&&fe.warn_function(_(e,t))};var pe=ce("Statement",null,{$documentation:"Base class of all statements"}),_e=ce("Debugger",null,{$documentation:"Represents a debugger statement"},pe),de=ce("Directive","value quote",{$documentation:'Represents a directive, like "use strict";',$propdoc:{value:"[string] The value of this directive as a plain string (it's not an AST_String!)",quote:"[string] the original quote character"}},pe),me=ce("SimpleStatement","body",{$documentation:"A statement consisting of an expression, i.e. a = 1 + 2",$propdoc:{body:"[AST_Node] an expression node (should not be instanceof AST_Statement)"},_walk:function(e){return e._visit(this,(function(){this.body._walk(e)}))}},pe);function Ee(e,t){var n=e.body;if(n instanceof fe)n._walk(t);else for(var i=0,r=n.length;i<r;i++)n[i]._walk(t)}function he(e){var t=this._clone(e);return this.block_scope&&(t.block_scope=this.block_scope.clone()),t}var De=ce("Block","body block_scope",{$documentation:"A body of statements (usually braced)",$propdoc:{body:"[AST_Statement*] an array of statements",block_scope:"[AST_Scope] the block scope"},_walk:function(e){return e._visit(this,(function(){Ee(this,e)}))},clone:he},pe),ge=ce("BlockStatement",null,{$documentation:"A block statement"},De),Se=ce("EmptyStatement",null,{$documentation:"The empty statement (empty block or simply a semicolon)"},pe),Ae=ce("StatementWithBody","body",{$documentation:"Base class for all statements that contain one nested body: `For`, `ForIn`, `Do`, `While`, `With`",$propdoc:{body:"[AST_Statement] the body; this should always be present, even if it's an AST_EmptyStatement"}},pe),ve=ce("LabeledStatement","label",{$documentation:"Statement with a label",$propdoc:{label:"[AST_Label] a label definition"},_walk:function(e){return e._visit(this,(function(){this.label._walk(e),this.body._walk(e)}))},clone:function(e){var t=this._clone(e);if(e){var n=t.label,i=this.label;t.walk(new An((function(e){e instanceof Ye&&e.label&&e.label.thedef===i&&(e.label.thedef=n,n.references.push(e))})))}return t}},Ae),Te=ce("IterationStatement","block_scope",{$documentation:"Internal class.  All loops inherit from it.",$propdoc:{block_scope:"[AST_Scope] the block scope for this iteration statement."},clone:he},Ae),be=ce("DWLoop","condition",{$documentation:"Base class for do/while statements",$propdoc:{condition:"[AST_Node] the loop condition.  Should not be instanceof AST_Statement"}},Te),ye=ce("Do",null,{$documentation:"A `do` statement",_walk:function(e){return e._visit(this,(function(){this.body._walk(e),this.condition._walk(e)}))}},be),Ce=ce("While",null,{$documentation:"A `while` statement",_walk:function(e){return e._visit(this,(function(){this.condition._walk(e),this.body._walk(e)}))}},be),Oe=ce("For","init condition step",{$documentation:"A `for` statement",$propdoc:{init:"[AST_Node?] the `for` initialization code, or null if empty",condition:"[AST_Node?] the `for` termination clause, or null if empty",step:"[AST_Node?] the `for` update clause, or null if empty"},_walk:function(e){return e._visit(this,(function(){this.init&&this.init._walk(e),this.condition&&this.condition._walk(e),this.step&&this.step._walk(e),this.body._walk(e)}))}},Te),Fe=ce("ForIn","init object",{$documentation:"A `for ... in` statement",$propdoc:{init:"[AST_Node] the `for/in` initialization code",object:"[AST_Node] the object that we're looping through"},_walk:function(e){return e._visit(this,(function(){this.init._walk(e),this.object._walk(e),this.body._walk(e)}))}},Te),Me=ce("ForOf","await",{$documentation:"A `for ... of` statement"},Fe),Re=ce("With","expression",{$documentation:"A `with` statement",$propdoc:{expression:"[AST_Node] the `with` expression"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e),this.body._walk(e)}))}},Ae),Ne=ce("Scope","variables functions uses_with uses_eval parent_scope enclosed cname _var_name_cache",{$documentation:"Base class for all statements introducing a lexical scope",$propdoc:{variables:"[Map/S] a map of name -> SymbolDef for all variables/functions defined in this scope",functions:"[Map/S] like `variables`, but only lists function declarations",uses_with:"[boolean/S] tells whether this scope uses the `with` statement",uses_eval:"[boolean/S] tells whether this scope contains a direct call to the global `eval`",parent_scope:"[AST_Scope?/S] link to the parent scope",enclosed:"[SymbolDef*/S] a list of all symbol definitions that are accessed from this scope or any subscopes",cname:"[integer/S] current index for mangling variables (used internally by the mangler)"},get_defun_scope:function(){for(var e=this;e.is_block_scope();)e=e.parent_scope;return e},clone:function(e){var t=this._clone(e);return this.variables&&(t.variables=new Map(this.variables)),this.functions&&(t.functions=new Map(this.functions)),this.enclosed&&(t.enclosed=this.enclosed.slice()),t},pinned:function(){return this.uses_eval||this.uses_with}},De),we=ce("Toplevel","globals",{$documentation:"The toplevel scope",$propdoc:{globals:"[Map/S] a map of name -> SymbolDef for all undeclared names"},wrap_commonjs:function(e){var t=this.body,n="(function(exports){'$ORIG';})(typeof "+e+"=='undefined'?("+e+"={}):"+e+");";return n=(n=ue(n)).transform(new vn((function(e){if(e instanceof de&&"$ORIG"==e.value)return f.splice(t)})))},wrap_enclose:function(e){"string"!=typeof e&&(e="");var t=e.indexOf(":");t<0&&(t=e.length);var n=this.body;return ue(["(function(",e.slice(0,t),'){"$ORIG"})(',e.slice(t+1),")"].join("")).transform(new vn((function(e){if(e instanceof de&&"$ORIG"==e.value)return f.splice(n)})))}},Ne),xe=ce("Expansion","expression",{$documentation:"An expandible argument, such as ...rest, a splat, such as [1,2,...all], or an expansion in a variable declaration, such as var [first, ...rest] = list",$propdoc:{expression:"[AST_Node] the thing to be expanded"},_walk:function(e){var t=this;return e._visit(this,(function(){t.expression.walk(e)}))}}),ke=ce("Lambda","name argnames uses_arguments is_generator async",{$documentation:"Base class for functions",$propdoc:{name:"[AST_SymbolDeclaration?] the name of this function",argnames:"[AST_SymbolFunarg|AST_Destructuring|AST_Expansion|AST_DefaultAssign*] array of function arguments, destructurings, or expanding arguments",uses_arguments:"[boolean/S] tells whether this function accesses the arguments array",is_generator:"[boolean] is this a generator method",async:"[boolean] is this method async"},args_as_names:function(){for(var e=[],t=0;t<this.argnames.length;t++)this.argnames[t]instanceof Be?e.push(...this.argnames[t].all_symbols()):e.push(this.argnames[t]);return e},_walk:function(e){return e._visit(this,(function(){this.name&&this.name._walk(e);for(var t=this.argnames,n=0,i=t.length;n<i;n++)t[n]._walk(e);Ee(this,e)}))}},Ne),Ie=ce("Accessor",null,{$documentation:"A setter/getter function.  The `name` property is always null."},ke),Le=ce("Function",null,{$documentation:"A function expression"},ke),Ve=ce("Arrow",null,{$documentation:"An ES6 Arrow function ((a) => b)"},ke),Pe=ce("Defun",null,{$documentation:"A function definition"},ke),Be=ce("Destructuring","names is_array",{$documentation:"A destructuring of several names. Used in destructuring assignment and with destructuring function argument names",$propdoc:{names:"[AST_Node*] Array of properties or elements",is_array:"[Boolean] Whether the destructuring represents an object or array"},_walk:function(e){return e._visit(this,(function(){this.names.forEach((function(t){t._walk(e)}))}))},all_symbols:function(){var e=[];return this.walk(new An((function(t){t instanceof It&&e.push(t)}))),e}}),Ke=ce("PrefixedTemplateString","template_string prefix",{$documentation:"A templatestring with a prefix, such as String.raw`foobarbaz`",$propdoc:{template_string:"[AST_TemplateString] The template string",prefix:"[AST_SymbolRef|AST_PropAccess] The prefix, which can be a symbol such as `foo` or a dotted expression such as `String.raw`."},_walk:function(e){this.prefix._walk(e),this.template_string._walk(e)}}),Ue=ce("TemplateString","segments",{$documentation:"A template string literal",$propdoc:{segments:"[AST_Node*] One or more segments, starting with AST_TemplateSegment. AST_Node may follow AST_TemplateSegment, but each AST_Node must be followed by AST_TemplateSegment."},_walk:function(e){return e._visit(this,(function(){this.segments.forEach((function(t){t._walk(e)}))}))}}),Ge=ce("TemplateSegment","value raw",{$documentation:"A segment of a template string literal",$propdoc:{value:"Content of the segment",raw:"Raw content of the segment"}}),He=ce("Jump",null,{$documentation:"Base class for “jumps” (for now that's `return`, `throw`, `break` and `continue`)"},pe),Xe=ce("Exit","value",{$documentation:"Base class for “exits” (`return` and `throw`)",$propdoc:{value:"[AST_Node?] the value returned or thrown by this statement; could be null for AST_Return"},_walk:function(e){return e._visit(this,this.value&&function(){this.value._walk(e)})}},He),ze=ce("Return",null,{$documentation:"A `return` statement"},Xe),We=ce("Throw",null,{$documentation:"A `throw` statement"},Xe),Ye=ce("LoopControl","label",{$documentation:"Base class for loop control statements (`break` and `continue`)",$propdoc:{label:"[AST_LabelRef?] the label, or null if none"},_walk:function(e){return e._visit(this,this.label&&function(){this.label._walk(e)})}},He),qe=ce("Break",null,{$documentation:"A `break` statement"},Ye),$e=ce("Continue",null,{$documentation:"A `continue` statement"},Ye),je=ce("If","condition alternative",{$documentation:"A `if` statement",$propdoc:{condition:"[AST_Node] the `if` condition",alternative:"[AST_Statement?] the `else` part, or null if not present"},_walk:function(e){return e._visit(this,(function(){this.condition._walk(e),this.body._walk(e),this.alternative&&this.alternative._walk(e)}))}},Ae),Ze=ce("Switch","expression",{$documentation:"A `switch` statement",$propdoc:{expression:"[AST_Node] the `switch` “discriminant”"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e),Ee(this,e)}))}},De),Je=ce("SwitchBranch",null,{$documentation:"Base class for `switch` branches"},De),Qe=ce("Default",null,{$documentation:"A `default` switch branch"},Je),et=ce("Case","expression",{$documentation:"A `case` switch branch",$propdoc:{expression:"[AST_Node] the `case` expression"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e),Ee(this,e)}))}},Je),tt=ce("Try","bcatch bfinally",{$documentation:"A `try` statement",$propdoc:{bcatch:"[AST_Catch?] the catch block, or null if not present",bfinally:"[AST_Finally?] the finally block, or null if not present"},_walk:function(e){return e._visit(this,(function(){Ee(this,e),this.bcatch&&this.bcatch._walk(e),this.bfinally&&this.bfinally._walk(e)}))}},De),nt=ce("Catch","argname",{$documentation:"A `catch` node; only makes sense as part of a `try` statement",$propdoc:{argname:"[AST_SymbolCatch|AST_Destructuring|AST_Expansion|AST_DefaultAssign] symbol for the exception"},_walk:function(e){return e._visit(this,(function(){this.argname&&this.argname._walk(e),Ee(this,e)}))}},De),it=ce("Finally",null,{$documentation:"A `finally` node; only makes sense as part of a `try` statement"},De),rt=ce("Definitions","definitions",{$documentation:"Base class for `var` or `const` nodes (variable declarations/initializations)",$propdoc:{definitions:"[AST_VarDef*] array of variable definitions"},_walk:function(e){return e._visit(this,(function(){for(var t=this.definitions,n=0,i=t.length;n<i;n++)t[n]._walk(e)}))}},pe),ot=ce("Var",null,{$documentation:"A `var` statement"},rt),at=ce("Let",null,{$documentation:"A `let` statement"},rt),st=ce("Const",null,{$documentation:"A `const` statement"},rt),ut=ce("NameMapping","foreign_name name",{$documentation:"The part of the export/import statement that declare names from a module.",$propdoc:{foreign_name:"[AST_SymbolExportForeign|AST_SymbolImportForeign] The name being exported/imported (as specified in the module)",name:"[AST_SymbolExport|AST_SymbolImport] The name as it is visible to this module."},_walk:function(e){return e._visit(this,(function(){this.foreign_name._walk(e),this.name._walk(e)}))}}),ct=ce("Import","imported_name imported_names module_name",{$documentation:"An `import` statement",$propdoc:{imported_name:"[AST_SymbolImport] The name of the variable holding the module's default export.",imported_names:"[AST_NameMapping*] The names of non-default imported variables",module_name:"[AST_String] String literal describing where this module came from"},_walk:function(e){return e._visit(this,(function(){this.imported_name&&this.imported_name._walk(e),this.imported_names&&this.imported_names.forEach((function(t){t._walk(e)})),this.module_name._walk(e)}))}}),lt=ce("Export","exported_definition exported_value is_default exported_names module_name",{$documentation:"An `export` statement",$propdoc:{exported_definition:"[AST_Defun|AST_Definitions|AST_DefClass?] An exported definition",exported_value:"[AST_Node?] An exported value",exported_names:"[AST_NameMapping*?] List of exported names",module_name:"[AST_String?] Name of the file to load exports from",is_default:"[Boolean] Whether this is the default exported value of this module"},_walk:function(e){e._visit(this,(function(){this.exported_definition&&this.exported_definition._walk(e),this.exported_value&&this.exported_value._walk(e),this.exported_names&&this.exported_names.forEach((function(t){t._walk(e)})),this.module_name&&this.module_name._walk(e)}))}},pe),ft=ce("VarDef","name value",{$documentation:"A variable declaration; only appears in a AST_Definitions node",$propdoc:{name:"[AST_Destructuring|AST_SymbolConst|AST_SymbolLet|AST_SymbolVar] name of the variable",value:"[AST_Node?] initializer, or null of there's no initializer"},_walk:function(e){return e._visit(this,(function(){this.name._walk(e),this.value&&this.value._walk(e)}))}}),pt=ce("Call","expression args _annotations",{$documentation:"A function call expression",$propdoc:{expression:"[AST_Node] expression to invoke as function",args:"[AST_Node*] array of arguments",_annotations:"[number] bitfield containing information about the call"},initialize(){null==this._annotations&&(this._annotations=0)},_walk(e){return e._visit(this,(function(){for(var t=this.args,n=0,i=t.length;n<i;n++)t[n]._walk(e);this.expression._walk(e)}))}}),_t=ce("New",null,{$documentation:"An object instantiation.  Derives from a function call since it has exactly the same properties"},pt),dt=ce("Sequence","expressions",{$documentation:"A sequence expression (comma-separated expressions)",$propdoc:{expressions:"[AST_Node*] array of expressions (at least two)"},_walk:function(e){return e._visit(this,(function(){this.expressions.forEach((function(t){t._walk(e)}))}))}}),mt=ce("PropAccess","expression property",{$documentation:'Base class for property access expressions, i.e. `a.foo` or `a["foo"]`',$propdoc:{expression:"[AST_Node] the “container” expression",property:"[AST_Node|string] the property to access.  For AST_Dot this is always a plain string, while for AST_Sub it's an arbitrary AST_Node"}}),Et=ce("Dot","quote",{$documentation:"A dotted property access expression",$propdoc:{quote:"[string] the original quote character when transformed from AST_Sub"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))}},mt),ht=ce("Sub",null,{$documentation:'Index-style property access, i.e. `a["foo"]`',_walk:function(e){return e._visit(this,(function(){this.expression._walk(e),this.property._walk(e)}))}},mt),Dt=ce("Unary","operator expression",{$documentation:"Base class for unary expressions",$propdoc:{operator:"[string] the operator",expression:"[AST_Node] expression that this unary operator applies to"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))}}),gt=ce("UnaryPrefix",null,{$documentation:"Unary prefix expression, i.e. `typeof i` or `++i`"},Dt),St=ce("UnaryPostfix",null,{$documentation:"Unary postfix expression, i.e. `i++`"},Dt),At=ce("Binary","operator left right",{$documentation:"Binary expression, i.e. `a + b`",$propdoc:{left:"[AST_Node] left-hand side expression",operator:"[string] the operator",right:"[AST_Node] right-hand side expression"},_walk:function(e){return e._visit(this,(function(){this.left._walk(e),this.right._walk(e)}))}}),vt=ce("Conditional","condition consequent alternative",{$documentation:"Conditional expression using the ternary operator, i.e. `a ? b : c`",$propdoc:{condition:"[AST_Node]",consequent:"[AST_Node]",alternative:"[AST_Node]"},_walk:function(e){return e._visit(this,(function(){this.condition._walk(e),this.consequent._walk(e),this.alternative._walk(e)}))}}),Tt=ce("Assign",null,{$documentation:"An assignment expression — `a = b + 5`"},At),bt=ce("DefaultAssign",null,{$documentation:"A default assignment expression like in `(a = 3) => a`"},At),yt=ce("Array","elements",{$documentation:"An array literal",$propdoc:{elements:"[AST_Node*] array of elements"},_walk:function(e){return e._visit(this,(function(){for(var t=this.elements,n=0,i=t.length;n<i;n++)t[n]._walk(e)}))}}),Ct=ce("Object","properties",{$documentation:"An object literal",$propdoc:{properties:"[AST_ObjectProperty*] array of properties"},_walk:function(e){return e._visit(this,(function(){for(var t=this.properties,n=0,i=t.length;n<i;n++)t[n]._walk(e)}))}}),Ot=ce("ObjectProperty","key value",{$documentation:"Base class for literal object properties",$propdoc:{key:"[string|AST_Node] property name. For ObjectKeyVal this is a string. For getters, setters and computed property this is an AST_Node.",value:"[AST_Node] property value.  For getters and setters this is an AST_Accessor."},_walk:function(e){return e._visit(this,(function(){this.key instanceof fe&&this.key._walk(e),this.value._walk(e)}))}}),Ft=ce("ObjectKeyVal","quote",{$documentation:"A key: value object property",$propdoc:{quote:"[string] the original quote character"}},Ot),Mt=ce("ObjectSetter","quote static",{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] whether this is a static setter (classes only)"},$documentation:"An object setter property"},Ot),Rt=ce("ObjectGetter","quote static",{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] whether this is a static getter (classes only)"},$documentation:"An object getter property"},Ot),Nt=ce("ConciseMethod","quote static is_generator async",{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] is this method static (classes only)",is_generator:"[boolean] is this a generator method",async:"[boolean] is this method async"},$documentation:"An ES6 concise method inside an object or class"},Ot),wt=ce("Class","name extends properties",{$propdoc:{name:"[AST_SymbolClass|AST_SymbolDefClass?] optional class name.",extends:"[AST_Node]? optional parent class",properties:"[AST_ObjectProperty*] array of properties"},$documentation:"An ES6 class",_walk:function(e){return e._visit(this,(function(){this.name&&this.name._walk(e),this.extends&&this.extends._walk(e),this.properties.forEach(t=>t._walk(e))}))}},Ne),xt=ce("DefClass",null,{$documentation:"A class definition"},wt),kt=ce("ClassExpression",null,{$documentation:"A class expression."},wt),It=ce("Symbol","scope name thedef",{$propdoc:{name:"[string] name of this symbol",scope:"[AST_Scope/S] the current scope (not necessarily the definition scope)",thedef:"[SymbolDef/S] the definition of this symbol"},$documentation:"Base class for all symbols"}),Lt=ce("NewTarget",null,{$documentation:"A reference to new.target"}),Vt=ce("SymbolDeclaration","init",{$documentation:"A declaration symbol (symbol in var/const, function name or argument, symbol in catch)"},It),Pt=ce("SymbolVar",null,{$documentation:"Symbol defining a variable"},Vt),Bt=ce("SymbolBlockDeclaration",null,{$documentation:"Base class for block-scoped declaration symbols"},Vt),Kt=ce("SymbolConst",null,{$documentation:"A constant declaration"},Bt),Ut=ce("SymbolLet",null,{$documentation:"A block-scoped `let` declaration"},Bt),Gt=ce("SymbolFunarg",null,{$documentation:"Symbol naming a function argument"},Pt),Ht=ce("SymbolDefun",null,{$documentation:"Symbol defining a function"},Vt),Xt=ce("SymbolMethod",null,{$documentation:"Symbol in an object defining a method"},It),zt=ce("SymbolLambda",null,{$documentation:"Symbol naming a function expression"},Vt),Wt=ce("SymbolDefClass",null,{$documentation:"Symbol naming a class's name in a class declaration. Lexically scoped to its containing scope, and accessible within the class."},Bt),Yt=ce("SymbolClass",null,{$documentation:"Symbol naming a class's name. Lexically scoped to the class."},Vt),qt=ce("SymbolCatch",null,{$documentation:"Symbol naming the exception in catch"},Bt),$t=ce("SymbolImport",null,{$documentation:"Symbol referring to an imported name"},Bt),jt=ce("SymbolImportForeign",null,{$documentation:"A symbol imported from a module, but it is defined in the other module, and its real name is irrelevant for this module's purposes"},It),Zt=ce("Label","references",{$documentation:"Symbol naming a label (declaration)",$propdoc:{references:"[AST_LoopControl*] a list of nodes referring to this label"},initialize:function(){this.references=[],this.thedef=this}},It),Jt=ce("SymbolRef",null,{$documentation:"Reference to some symbol (not definition/declaration)"},It),Qt=ce("SymbolExport",null,{$documentation:"Symbol referring to a name to export"},Jt),en=ce("SymbolExportForeign",null,{$documentation:"A symbol exported from this module, but it is used in the other module, and its real name is irrelevant for this module's purposes"},It),tn=ce("LabelRef",null,{$documentation:"Reference to a label symbol"},It),nn=ce("This",null,{$documentation:"The `this` symbol"},It),rn=ce("Super",null,{$documentation:"The `super` symbol"},nn),on=ce("Constant",null,{$documentation:"Base class for all constants",getValue:function(){return this.value}}),an=ce("String","value quote",{$documentation:"A string literal",$propdoc:{value:"[string] the contents of this string",quote:"[string] the original quote character"}},on),sn=ce("Number","value literal",{$documentation:"A number literal",$propdoc:{value:"[number] the numeric value",literal:"[string] numeric value as string (optional)"}},on),un=ce("BigInt","value",{$documentation:"A big int literal",$propdoc:{value:"[string] big int value"}},on),cn=ce("RegExp","value",{$documentation:"A regexp literal",$propdoc:{value:"[RegExp] the actual regexp"}},on),ln=ce("Atom",null,{$documentation:"Base class for atoms"},on),fn=ce("Null",null,{$documentation:"The `null` atom",value:null},ln),pn=ce("NaN",null,{$documentation:"The impossible value",value:NaN},ln),_n=ce("Undefined",null,{$documentation:"The `undefined` value",value:void 0},ln),dn=ce("Hole",null,{$documentation:"A hole in an array",value:void 0},ln),mn=ce("Infinity",null,{$documentation:"The `Infinity` value",value:1/0},ln),En=ce("Boolean",null,{$documentation:"Base class for booleans"},ln),hn=ce("False",null,{$documentation:"The `false` atom",value:!1},En),Dn=ce("True",null,{$documentation:"The `true` atom",value:!0},En),gn=ce("Await","expression",{$documentation:"An `await` statement",$propdoc:{expression:"[AST_Node] the mandatory expression being awaited"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))}}),Sn=ce("Yield","expression is_star",{$documentation:"A `yield` statement",$propdoc:{expression:"[AST_Node?] the value returned or thrown by this statement; could be null (representing undefined) but only when is_star is set to false",is_star:"[Boolean] Whether this is a yield or yield* statement"},_walk:function(e){return e._visit(this,this.expression&&function(){this.expression._walk(e)})}});class An{constructor(e){this.visit=e,this.stack=[],this.directives=Object.create(null)}_visit(e,t){this.push(e);var n=this.visit(e,t?function(){t.call(e)}:a);return!n&&t&&t.call(e),this.pop(),n}parent(e){return this.stack[this.stack.length-2-(e||0)]}push(e){e instanceof ke?this.directives=Object.create(this.directives):e instanceof de&&!this.directives[e.value]?this.directives[e.value]=e:e instanceof wt&&(this.directives=Object.create(this.directives),this.directives["use strict"]||(this.directives["use strict"]=e)),this.stack.push(e)}pop(){var e=this.stack.pop();(e instanceof ke||e instanceof wt)&&(this.directives=Object.getPrototypeOf(this.directives))}self(){return this.stack[this.stack.length-1]}find_parent(e){for(var t=this.stack,n=t.length;--n>=0;){var i=t[n];if(i instanceof e)return i}}has_directive(e){var t=this.directives[e];if(t)return t;var n=this.stack[this.stack.length-1];if(n instanceof Ne&&n.body)for(var i=0;i<n.body.length;++i){var r=n.body[i];if(!(r instanceof de))break;if(r.value==e)return r}}loopcontrol_target(e){var t=this.stack;if(e.label)for(var n=t.length;--n>=0;){if((i=t[n])instanceof ve&&i.label.name==e.label.name)return i.body}else for(n=t.length;--n>=0;){var i;if((i=t[n])instanceof Te||e instanceof qe&&i instanceof Ze)return i}}}class vn extends An{constructor(e,t){super(),this.before=e,this.after=t}}const Tn=1,bn=2,yn=4;var Cn=Object.freeze({__proto__:null,AST_Accessor:Ie,AST_Array:yt,AST_Arrow:Ve,AST_Assign:Tt,AST_Atom:ln,AST_Await:gn,AST_BigInt:un,AST_Binary:At,AST_Block:De,AST_BlockStatement:ge,AST_Boolean:En,AST_Break:qe,AST_Call:pt,AST_Case:et,AST_Catch:nt,AST_Class:wt,AST_ClassExpression:kt,AST_ConciseMethod:Nt,AST_Conditional:vt,AST_Const:st,AST_Constant:on,AST_Continue:$e,AST_Debugger:_e,AST_Default:Qe,AST_DefaultAssign:bt,AST_DefClass:xt,AST_Definitions:rt,AST_Defun:Pe,AST_Destructuring:Be,AST_Directive:de,AST_Do:ye,AST_Dot:Et,AST_DWLoop:be,AST_EmptyStatement:Se,AST_Exit:Xe,AST_Expansion:xe,AST_Export:lt,AST_False:hn,AST_Finally:it,AST_For:Oe,AST_ForIn:Fe,AST_ForOf:Me,AST_Function:Le,AST_Hole:dn,AST_If:je,AST_Import:ct,AST_Infinity:mn,AST_IterationStatement:Te,AST_Jump:He,AST_Label:Zt,AST_LabeledStatement:ve,AST_LabelRef:tn,AST_Lambda:ke,AST_Let:at,AST_LoopControl:Ye,AST_NameMapping:ut,AST_NaN:pn,AST_New:_t,AST_NewTarget:Lt,AST_Node:fe,AST_Null:fn,AST_Number:sn,AST_Object:Ct,AST_ObjectGetter:Rt,AST_ObjectKeyVal:Ft,AST_ObjectProperty:Ot,AST_ObjectSetter:Mt,AST_PrefixedTemplateString:Ke,AST_PropAccess:mt,AST_RegExp:cn,AST_Return:ze,AST_Scope:Ne,AST_Sequence:dt,AST_SimpleStatement:me,AST_Statement:pe,AST_StatementWithBody:Ae,AST_String:an,AST_Sub:ht,AST_Super:rn,AST_Switch:Ze,AST_SwitchBranch:Je,AST_Symbol:It,AST_SymbolBlockDeclaration:Bt,AST_SymbolCatch:qt,AST_SymbolClass:Yt,AST_SymbolConst:Kt,AST_SymbolDeclaration:Vt,AST_SymbolDefClass:Wt,AST_SymbolDefun:Ht,AST_SymbolExport:Qt,AST_SymbolExportForeign:en,AST_SymbolFunarg:Gt,AST_SymbolImport:$t,AST_SymbolImportForeign:jt,AST_SymbolLambda:zt,AST_SymbolLet:Ut,AST_SymbolMethod:Xt,AST_SymbolRef:Jt,AST_SymbolVar:Pt,AST_TemplateSegment:Ge,AST_TemplateString:Ue,AST_This:nn,AST_Throw:We,AST_Token:le,AST_Toplevel:we,AST_True:Dn,AST_Try:tt,AST_Unary:Dt,AST_UnaryPostfix:St,AST_UnaryPrefix:gt,AST_Undefined:_n,AST_Var:ot,AST_VarDef:ft,AST_While:Ce,AST_With:Re,AST_Yield:Sn,TreeTransformer:vn,TreeWalker:An,walk_body:Ee,_INLINE:bn,_NOINLINE:yn,_PURE:Tn});function On(e,t){e.DEFMETHOD("transform",(function(e,n){let i=void 0;if(e.push(this),e.before&&(i=e.before(this,t,n)),void 0===i&&(t(i=this,e),e.after)){const t=e.after(i,n);void 0!==t&&(i=t)}return e.pop(),i}))}function Fn(e,t){return f(e,(function(e){return e.transform(t,!0)}))}function Mn(e){let t=e.parent(-1);for(let n,i=0;n=e.parent(i);i++){if(n instanceof pe&&n.body===t)return!0;if(!(n instanceof dt&&n.expressions[0]===t||"Call"===n.TYPE&&n.expression===t||n instanceof Ke&&n.prefix===t||n instanceof Et&&n.expression===t||n instanceof ht&&n.expression===t||n instanceof vt&&n.condition===t||n instanceof At&&n.left===t||n instanceof St&&n.expression===t))return!1;t=n}}On(fe,a),On(ve,(function(e,t){e.label=e.label.transform(t),e.body=e.body.transform(t)})),On(me,(function(e,t){e.body=e.body.transform(t)})),On(De,(function(e,t){e.body=Fn(e.body,t)})),On(ye,(function(e,t){e.body=e.body.transform(t),e.condition=e.condition.transform(t)})),On(Ce,(function(e,t){e.condition=e.condition.transform(t),e.body=e.body.transform(t)})),On(Oe,(function(e,t){e.init&&(e.init=e.init.transform(t)),e.condition&&(e.condition=e.condition.transform(t)),e.step&&(e.step=e.step.transform(t)),e.body=e.body.transform(t)})),On(Fe,(function(e,t){e.init=e.init.transform(t),e.object=e.object.transform(t),e.body=e.body.transform(t)})),On(Re,(function(e,t){e.expression=e.expression.transform(t),e.body=e.body.transform(t)})),On(Xe,(function(e,t){e.value&&(e.value=e.value.transform(t))})),On(Ye,(function(e,t){e.label&&(e.label=e.label.transform(t))})),On(je,(function(e,t){e.condition=e.condition.transform(t),e.body=e.body.transform(t),e.alternative&&(e.alternative=e.alternative.transform(t))})),On(Ze,(function(e,t){e.expression=e.expression.transform(t),e.body=Fn(e.body,t)})),On(et,(function(e,t){e.expression=e.expression.transform(t),e.body=Fn(e.body,t)})),On(tt,(function(e,t){e.body=Fn(e.body,t),e.bcatch&&(e.bcatch=e.bcatch.transform(t)),e.bfinally&&(e.bfinally=e.bfinally.transform(t))})),On(nt,(function(e,t){e.argname&&(e.argname=e.argname.transform(t)),e.body=Fn(e.body,t)})),On(rt,(function(e,t){e.definitions=Fn(e.definitions,t)})),On(ft,(function(e,t){e.name=e.name.transform(t),e.value&&(e.value=e.value.transform(t))})),On(Be,(function(e,t){e.names=Fn(e.names,t)})),On(ke,(function(e,t){e.name&&(e.name=e.name.transform(t)),e.argnames=Fn(e.argnames,t),e.body instanceof fe?e.body=e.body.transform(t):e.body=Fn(e.body,t)})),On(pt,(function(e,t){e.expression=e.expression.transform(t),e.args=Fn(e.args,t)})),On(dt,(function(e,t){const n=Fn(e.expressions,t);e.expressions=n.length?n:[new sn({value:0})]})),On(Et,(function(e,t){e.expression=e.expression.transform(t)})),On(ht,(function(e,t){e.expression=e.expression.transform(t),e.property=e.property.transform(t)})),On(Sn,(function(e,t){e.expression&&(e.expression=e.expression.transform(t))})),On(gn,(function(e,t){e.expression=e.expression.transform(t)})),On(Dt,(function(e,t){e.expression=e.expression.transform(t)})),On(At,(function(e,t){e.left=e.left.transform(t),e.right=e.right.transform(t)})),On(vt,(function(e,t){e.condition=e.condition.transform(t),e.consequent=e.consequent.transform(t),e.alternative=e.alternative.transform(t)})),On(yt,(function(e,t){e.elements=Fn(e.elements,t)})),On(Ct,(function(e,t){e.properties=Fn(e.properties,t)})),On(Ot,(function(e,t){e.key instanceof fe&&(e.key=e.key.transform(t)),e.value=e.value.transform(t)})),On(wt,(function(e,t){e.name&&(e.name=e.name.transform(t)),e.extends&&(e.extends=e.extends.transform(t)),e.properties=Fn(e.properties,t)})),On(xe,(function(e,t){e.expression=e.expression.transform(t)})),On(ut,(function(e,t){e.foreign_name=e.foreign_name.transform(t),e.name=e.name.transform(t)})),On(ct,(function(e,t){e.imported_name&&(e.imported_name=e.imported_name.transform(t)),e.imported_names&&Fn(e.imported_names,t),e.module_name=e.module_name.transform(t)})),On(lt,(function(e,t){e.exported_definition&&(e.exported_definition=e.exported_definition.transform(t)),e.exported_value&&(e.exported_value=e.exported_value.transform(t)),e.exported_names&&Fn(e.exported_names,t),e.module_name&&(e.module_name=e.module_name.transform(t))})),On(Ue,(function(e,t){e.segments=Fn(e.segments,t)})),On(Ke,(function(e,t){e.prefix=e.prefix.transform(t),e.template_string=e.template_string.transform(t)}));const Rn=/^$|[;{][\s\n]*$/,Nn=10,wn=32,xn=/[@#]__(PURE|INLINE|NOINLINE)__/g;function kn(e){return"comment2"==e.type&&/@preserve|@lic|@cc_on|^\**!/i.test(e.value)}function In(e){var t=!e;void 0===(e=o(e,{ascii_only:!1,beautify:!1,braces:!1,comments:"some",ecma:5,ie8:!1,indent_level:4,indent_start:0,inline_script:!0,keep_quoted_props:!1,max_line_len:!1,preamble:null,quote_keys:!1,quote_style:0,safari10:!1,semicolons:!0,shebang:!0,shorthand:void 0,source_map:null,webkit:!1,width:80,wrap_iife:!1,wrap_func_args:!0},!0)).shorthand&&(e.shorthand=e.ecma>5);var n=s;if(e.comments){let t=e.comments;if("string"==typeof e.comments&&/^\/.*\/[a-zA-Z]*$/.test(e.comments)){var i=e.comments.lastIndexOf("/");t=new RegExp(e.comments.substr(1,i-1),e.comments.substr(i+1))}n=t instanceof RegExp?function(e){return"comment5"!=e.type&&t.test(e.value)}:"function"==typeof t?function(e){return"comment5"!=e.type&&t(this,e)}:"some"===t?kn:u}var r=0,c=0,l=1,f=0,p="";let _=new Set;var d=e.ascii_only?function(t,n){return e.ecma>=6&&(t=t.replace(/[\ud800-\udbff][\udc00-\udfff]/g,(function(e){return"\\u{"+function(e,t){return z(e.charAt(t))?65536+(e.charCodeAt(t)-55296<<10)+e.charCodeAt(t+1)-56320:e.charCodeAt(t)}(e,0).toString(16)+"}"}))),t.replace(/[\u0000-\u001f\u007f-\uffff]/g,(function(e){var t=e.charCodeAt(0).toString(16);if(t.length<=2&&!n){for(;t.length<2;)t="0"+t;return"\\x"+t}for(;t.length<4;)t="0"+t;return"\\u"+t}))}:function(e){for(var t="",n=0,i=e.length;n<i;n++)z(e[n])&&!W(e[n+1])||W(e[n])&&!z(e[n-1])?t+="\\u"+e.charCodeAt(n).toString(16):t+=e[n];return t};function m(t,n){var i=function(t,n){var i=0,r=0;function o(){return"'"+t.replace(/\x27/g,"\\'")+"'"}function a(){return'"'+t.replace(/\x22/g,'\\"')+'"'}if(t=t.replace(/[\\\b\f\n\r\v\t\x22\x27\u2028\u2029\0\ufeff]/g,(function(n,o){switch(n){case'"':return++i,'"';case"'":return++r,"'";case"\\":return"\\\\";case"\n":return"\\n";case"\r":return"\\r";case"\t":return"\\t";case"\b":return"\\b";case"\f":return"\\f";case"\v":return e.ie8?"\\x0B":"\\v";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";case"\ufeff":return"\\ufeff";case"\0":return/[0-9]/.test(X(t,o+1))?"\\x00":"\\0"}return n})),t=d(t),"`"===n)return"`"+t.replace(/`/g,"\\`")+"`";switch(e.quote_style){case 1:return o();case 2:return a();case 3:return"'"==n?o():a();default:return i>r?o():a()}}(t,n);return e.inline_script&&(i=(i=(i=i.replace(/<\x2f(script)([>\/\t\n\f\r ])/gi,"<\\/$1$2")).replace(/\x3c!--/g,"\\x3c!--")).replace(/--\x3e/g,"--\\x3e")),i}var h,D,g=!1,S=!1,A=!1,v=0,T=!1,b=!1,y=-1,C="",O=e.source_map&&[],F=O?function(){O.forEach((function(t){try{e.source_map.add(t.token.file,t.line,t.col,t.token.line,t.token.col,t.name||"name"!=t.token.type?t.name:t.token.value)}catch(e){null!=t.token.file&&fe.warn("Couldn't figure out mapping for {file}:{line},{col} → {cline},{ccol} [{name}]",{file:t.token.file,line:t.token.line,col:t.token.col,cline:t.line,ccol:t.col,name:t.name||""})}})),O=[]}:a,M=e.max_line_len?function(){if(c>e.max_line_len){if(v){var t=p.slice(0,v),n=p.slice(v);if(O){var i=n.length-c;O.forEach((function(e){e.line++,e.col+=i}))}p=t+"\n"+n,l++,f++,c=n.length}c>e.max_line_len&&fe.warn("Output exceeds {max_line_len} characters",e)}v&&(v=0,F())}:a,R=E("( [ + * / - , . `");function N(t){var n=X(t=String(t),0);T&&n&&(T=!1,"\n"!==n&&(N("\n"),x())),b&&n&&(b=!1,/[\s;})]/.test(n)||w()),y=-1;var i=C.charAt(C.length-1);A&&(A=!1,(":"!==i||"}"!==n)&&(n&&";}".includes(n)||";"===i)||(e.semicolons||R.has(n)?(p+=";",c++,f++):(M(),c>0&&(p+="\n",f++,l++,c=0),/^\s+$/.test(t)&&(A=!0)),e.beautify||(S=!1))),S&&(($(i)&&($(n)||"\\"==n)||"/"==n&&n==i||("+"==n||"-"==n)&&n==C)&&(p+=" ",c++,f++),S=!1),h&&(O.push({token:h,name:D,line:l,col:c}),h=!1,v||F()),p+=t,g="("==t[t.length-1],f+=t.length;var r=t.split(/\r?\n/),o=r.length-1;l+=o,c+=r[0].length,o>0&&(M(),c=r[o].length),C=t}var w=e.beautify?function(){N(" ")}:function(){S=!0},x=e.beautify?function(t){var n;e.beautify&&N((n=t?.5:0," ".repeat(e.indent_start+r-n*e.indent_level)))}:a,k=e.beautify?function(e,t){!0===e&&(e=P());var n=r;r=e;var i=t();return r=n,i}:function(e,t){return t()},I=e.beautify?function(){if(y<0)return N("\n");"\n"!=p[y]&&(p=p.slice(0,y)+"\n"+p.slice(y),f++,l++),y++}:e.max_line_len?function(){M(),v=p.length}:a,L=e.beautify?function(){N(";")}:function(){A=!0};function V(){A=!1,N(";")}function P(){return r+e.indent_level}function B(){return v&&M(),p}function K(){let e=p.length-1;for(;e>=0;){const t=p.charCodeAt(e);if(t===Nn)return!0;if(t!==wn)return!1;e--}return!0}var U=[];return{get:B,toString:B,indent:x,indentation:function(){return r},current_width:function(){return c-r},should_break:function(){return e.width&&this.current_width()>=e.width},has_parens:function(){return g},newline:I,print:N,star:function(){N("*")},space:w,comma:function(){N(","),w()},colon:function(){N(":"),w()},last:function(){return C},semicolon:L,force_semicolon:V,to_utf8:d,print_name:function(e){N(function(e){return e=e.toString(),e=d(e,!0)}(e))},print_string:function(e,t,n){var i=m(e,t);!0!==n||i.includes("\\")||(Rn.test(p)||V(),V()),N(i)},print_template_string_chars:function(e){var t=m(e,"`").replace(/\${/g,"\\${");return N(t.substr(1,t.length-2))},encode_string:m,next_indent:P,with_indent:k,with_block:function(e){var t;return N("{"),I(),k(P(),(function(){t=e()})),x(),N("}"),t},with_parens:function(e){N("(");var t=e();return N(")"),t},with_square:function(e){N("[");var t=e();return N("]"),t},add_mapping:O?function(e,t){h=e,D=t}:a,option:function(t){return e[t]},printed_comments:_,prepend_comments:t?a:function(t){var i=t.start;if(i){var r=this.printed_comments;if(!i.comments_before||!r.has(i.comments_before)){var o=i.comments_before;if(o||(o=i.comments_before=[]),r.add(o),t instanceof Xe&&t.value){var a=new An((function(e){var t=a.parent();if(!(t instanceof Xe||t instanceof At&&t.left===e||"Call"==t.TYPE&&t.expression===e||t instanceof vt&&t.condition===e||t instanceof Et&&t.expression===e||t instanceof dt&&t.expressions[0]===e||t instanceof ht&&t.expression===e||t instanceof St))return!0;if(e.start){var n=e.start.comments_before;n&&!r.has(n)&&(r.add(n),o=o.concat(n))}}));a.push(t),t.value.walk(a)}if(0==f){o.length>0&&e.shebang&&"comment5"===o[0].type&&!r.has(o[0])&&(N("#!"+o.shift().value+"\n"),x());var s=e.preamble;s&&N(s.replace(/\r\n?|[\n\u2028\u2029]|\s*$/g,"\n"))}if(0!=(o=o.filter(n,t).filter(e=>!r.has(e))).length){var u=K();o.forEach((function(e,t){r.add(e),u||(e.nlb?(N("\n"),x(),u=!0):t>0&&w()),/comment[134]/.test(e.type)?(N("//"+e.value.replace(xn," ")+"\n"),x(),u=!0):"comment2"==e.type&&(N("/*"+e.value.replace(xn," ")+"*/"),u=!1)})),u||(i.nlb?(N("\n"),x()):w())}}}},append_comments:t||n===s?a:function(e,t){var i=e.end;if(i){var r=this.printed_comments,o=i[t?"comments_before":"comments_after"];if(o&&!r.has(o)&&(e instanceof pe||o.every(e=>!/comment[134]/.test(e.type)))){r.add(o);var a=p.length;o.filter(n,e).forEach((function(e,n){r.has(e)||(r.add(e),b=!1,T?(N("\n"),x(),T=!1):e.nlb&&(n>0||!K())?(N("\n"),x()):(n>0||!t)&&w(),/comment[134]/.test(e.type)?(N("//"+e.value.replace(xn," ")),T=!0):"comment2"==e.type&&(N("/*"+e.value.replace(xn," ")+"*/"),b=!0))})),p.length>a&&(y=a)}}},line:function(){return l},col:function(){return c},pos:function(){return f},push_node:function(e){U.push(e)},pop_node:function(){return U.pop()},parent:function(e){return U[U.length-2-(e||0)]}}}!function(){function e(e,t){e.DEFMETHOD("_codegen",t)}var t=!1,n=null,i=null;function r(e,t){Array.isArray(e)?e.forEach((function(e){r(e,t)})):e.DEFMETHOD("needs_parens",t)}function o(e,n,i,r){var o=e.length-1;t=r,e.forEach((function(e,r){!0!==t||e instanceof de||e instanceof Se||e instanceof me&&e.body instanceof an||(t=!1),e instanceof Se||(i.indent(),e.print(i),r==o&&n||(i.newline(),n&&i.newline())),!0===t&&e instanceof me&&e.body instanceof an&&(t=!1)})),t=!1}function u(e,t){t.print("{"),t.with_indent(t.next_indent(),(function(){t.append_comments(e,!0)})),t.print("}")}function c(e,t,n){e.body.length>0?t.with_block((function(){o(e.body,!1,t,n)})):u(e,t)}function l(e,t,n){var i=!1;n&&e.walk(new An((function(e){return!!(i||e instanceof Ne)||(e instanceof At&&"in"==e.operator?(i=!0,!0):void 0)}))),e.print(t,i)}function f(e,t,n){n.option("quote_keys")?n.print_string(e):""+ +e==e&&e>=0?n.print(_(e)):(O.has(e)?!n.option("ie8"):j(e))?t&&n.option("keep_quoted_props")?n.print_string(e,t):n.print_name(e):n.print_string(e,t)}function p(e,t){t.option("braces")?d(e,t):!e||e instanceof Se?t.force_semicolon():e.print(t)}function _(e){var t,n,i,r=e.toString(10).replace(/^0\./,".").replace("e+","e"),o=[r];return Math.floor(e)===e&&(e<0?o.push("-0x"+(-e).toString(16).toLowerCase()):o.push("0x"+e.toString(16).toLowerCase())),(t=/^\.0+/.exec(r))?(n=t[0].length,i=r.slice(n),o.push(i+"e-"+(i.length+n-1))):(t=/0+$/.exec(r))?(n=t[0].length,o.push(r.slice(0,-n)+"e"+n)):(t=/^(\d)\.(\d+)e(-?\d+)$/.exec(r))&&o.push(t[1]+t[2]+"e"+(t[3]-t[2].length)),function(e){for(var t=e[0],n=t.length,i=1;i<e.length;++i)e[i].length<n&&(n=(t=e[i]).length);return t}(o)}function d(e,t){!e||e instanceof Se?t.print("{}"):e instanceof ge?e.print(t):t.with_block((function(){t.indent(),e.print(t),t.newline()}))}function m(e,t){e.forEach((function(e){e.DEFMETHOD("add_source_map",t)}))}fe.DEFMETHOD("print",(function(e,t){var r=this,o=r._codegen;function a(){e.prepend_comments(r),r.add_source_map(e),o(r,e),e.append_comments(r)}r instanceof Ne?n=r:!i&&r instanceof de&&"use asm"==r.value&&(i=n),e.push_node(r),t||r.needs_parens(e)?e.with_parens(a):a(),e.pop_node(),r===i&&(i=null)})),fe.DEFMETHOD("_print",fe.prototype.print),fe.DEFMETHOD("print_to_string",(function(e){var t=In(e);return this.print(t),t.get()})),r(fe,s),r(Le,(function(e){if(!e.has_parens()&&Mn(e))return!0;var t;if(e.option("webkit")&&((t=e.parent())instanceof mt&&t.expression===this))return!0;if(e.option("wrap_iife")&&((t=e.parent())instanceof pt&&t.expression===this))return!0;if(e.option("wrap_func_args")&&((t=e.parent())instanceof pt&&t.args.includes(this)))return!0;return!1})),r(Ve,(function(e){var t=e.parent();return t instanceof mt&&t.expression===this})),r(Ct,(function(e){return!e.has_parens()&&Mn(e)})),r(kt,Mn),r(Dt,(function(e){var t=e.parent();return t instanceof mt&&t.expression===this||t instanceof pt&&t.expression===this||t instanceof At&&"**"===t.operator&&this instanceof gt&&t.left===this&&"++"!==this.operator&&"--"!==this.operator})),r(gn,(function(e){var t=e.parent();return t instanceof mt&&t.expression===this||t instanceof pt&&t.expression===this||e.option("safari10")&&t instanceof gt})),r(dt,(function(e){var t=e.parent();return t instanceof pt||t instanceof Dt||t instanceof At||t instanceof ft||t instanceof mt||t instanceof yt||t instanceof Ot||t instanceof vt||t instanceof Ve||t instanceof bt||t instanceof xe||t instanceof Me&&this===t.object||t instanceof Sn||t instanceof lt})),r(At,(function(e){var t=e.parent();if(t instanceof pt&&t.expression===this)return!0;if(t instanceof Dt)return!0;if(t instanceof mt&&t.expression===this)return!0;if(t instanceof At){var n=t.operator,i=ae[n],r=this.operator,o=ae[r];if(i>o||i==o&&(this===t.right||"**"==n))return!0}})),r(Sn,(function(e){var t=e.parent();return t instanceof At&&"="!==t.operator||(t instanceof pt&&t.expression===this||(t instanceof vt&&t.condition===this||(t instanceof Dt||(t instanceof mt&&t.expression===this||void 0))))})),r(mt,(function(e){var t=e.parent();if(t instanceof _t&&t.expression===this){var n=!1;return this.walk(new An((function(e){return!!(n||e instanceof Ne)||(e instanceof pt?(n=!0,!0):void 0)}))),n}})),r(pt,(function(e){var t,n=e.parent();return!!(n instanceof _t&&n.expression===this||n instanceof lt&&n.is_default&&this.expression instanceof Le)||this.expression instanceof Le&&n instanceof mt&&n.expression===this&&(t=e.parent(1))instanceof Tt&&t.left===n})),r(_t,(function(e){var t=e.parent();if(0===this.args.length&&(t instanceof mt||t instanceof pt&&t.expression===this))return!0})),r(sn,(function(e){var t=e.parent();if(t instanceof mt&&t.expression===this){var n=this.getValue();if(n<0||/^0/.test(_(n)))return!0}})),r(un,(function(e){var t=e.parent();if(t instanceof mt&&t.expression===this&&this.getValue().startsWith("-"))return!0})),r([Tt,vt],(function(e){var t=e.parent();return t instanceof Dt||(t instanceof At&&!(t instanceof Tt)||(t instanceof pt&&t.expression===this||(t instanceof vt&&t.condition===this||(t instanceof mt&&t.expression===this||(this instanceof Tt&&this.left instanceof Be&&!1===this.left.is_array||void 0)))))})),e(de,(function(e,t){t.print_string(e.value,e.quote),t.semicolon()})),e(xe,(function(e,t){t.print("..."),e.expression.print(t)})),e(Be,(function(e,t){t.print(e.is_array?"[":"{");var n=e.names.length;e.names.forEach((function(e,i){i>0&&t.comma(),e.print(t),i==n-1&&e instanceof dn&&t.comma()})),t.print(e.is_array?"]":"}")})),e(_e,(function(e,t){t.print("debugger"),t.semicolon()})),Ae.DEFMETHOD("_do_print_body",(function(e){p(this.body,e)})),e(pe,(function(e,t){e.body.print(t),t.semicolon()})),e(we,(function(e,t){o(e.body,!0,t,!0),t.print("")})),e(ve,(function(e,t){e.label.print(t),t.colon(),e.body.print(t)})),e(me,(function(e,t){e.body.print(t),t.semicolon()})),e(ge,(function(e,t){c(e,t)})),e(Se,(function(e,t){t.semicolon()})),e(ye,(function(e,t){t.print("do"),t.space(),d(e.body,t),t.space(),t.print("while"),t.space(),t.with_parens((function(){e.condition.print(t)})),t.semicolon()})),e(Ce,(function(e,t){t.print("while"),t.space(),t.with_parens((function(){e.condition.print(t)})),t.space(),e._do_print_body(t)})),e(Oe,(function(e,t){t.print("for"),t.space(),t.with_parens((function(){e.init?(e.init instanceof rt?e.init.print(t):l(e.init,t,!0),t.print(";"),t.space()):t.print(";"),e.condition?(e.condition.print(t),t.print(";"),t.space()):t.print(";"),e.step&&e.step.print(t)})),t.space(),e._do_print_body(t)})),e(Fe,(function(e,t){t.print("for"),e.await&&(t.space(),t.print("await")),t.space(),t.with_parens((function(){e.init.print(t),t.space(),t.print(e instanceof Me?"of":"in"),t.space(),e.object.print(t)})),t.space(),e._do_print_body(t)})),e(Re,(function(e,t){t.print("with"),t.space(),t.with_parens((function(){e.expression.print(t)})),t.space(),e._do_print_body(t)})),ke.DEFMETHOD("_do_print",(function(e,t){var n=this;t||(n.async&&(e.print("async"),e.space()),e.print("function"),n.is_generator&&e.star(),n.name&&e.space()),n.name instanceof It?n.name.print(e):t&&n.name instanceof fe&&e.with_square((function(){n.name.print(e)})),e.with_parens((function(){n.argnames.forEach((function(t,n){n&&e.comma(),t.print(e)}))})),e.space(),c(n,e,!0)})),e(ke,(function(e,t){e._do_print(t)})),e(Ke,(function(e,t){var n=e.prefix,i=n instanceof ke||n instanceof At||n instanceof vt||n instanceof dt||n instanceof Dt||n instanceof Et&&n.expression instanceof Ct;i&&t.print("("),e.prefix.print(t),i&&t.print(")"),e.template_string.print(t)})),e(Ue,(function(e,t){var n=t.parent()instanceof Ke;t.print("`");for(var i=0;i<e.segments.length;i++)e.segments[i]instanceof Ge?n?t.print(e.segments[i].raw):t.print_template_string_chars(e.segments[i].value):(t.print("${"),e.segments[i].print(t),t.print("}"));t.print("`")})),Ve.DEFMETHOD("_do_print",(function(e){var t=this,n=e.parent(),i=n instanceof At&&!(n instanceof Tt)||n instanceof Dt||n instanceof pt&&t===n.expression;i&&e.print("("),t.async&&(e.print("async"),e.space()),1===t.argnames.length&&t.argnames[0]instanceof It?t.argnames[0].print(e):e.with_parens((function(){t.argnames.forEach((function(t,n){n&&e.comma(),t.print(e)}))})),e.space(),e.print("=>"),e.space(),t.body instanceof fe?t.body.print(e):c(t,e),i&&e.print(")")})),Xe.DEFMETHOD("_do_print",(function(e,t){if(e.print(t),this.value){e.space();const t=this.value.start.comments_before;t&&t.length&&!e.printed_comments.has(t)?(e.print("("),this.value.print(e),e.print(")")):this.value.print(e)}e.semicolon()})),e(ze,(function(e,t){e._do_print(t,"return")})),e(We,(function(e,t){e._do_print(t,"throw")})),e(Sn,(function(e,t){var n=e.is_star?"*":"";t.print("yield"+n),e.expression&&(t.space(),e.expression.print(t))})),e(gn,(function(e,t){t.print("await"),t.space();var n=e.expression,i=!(n instanceof pt||n instanceof Jt||n instanceof mt||n instanceof Dt||n instanceof on);i&&t.print("("),e.expression.print(t),i&&t.print(")")})),Ye.DEFMETHOD("_do_print",(function(e,t){e.print(t),this.label&&(e.space(),this.label.print(e)),e.semicolon()})),e(qe,(function(e,t){e._do_print(t,"break")})),e($e,(function(e,t){e._do_print(t,"continue")})),e(je,(function(e,t){t.print("if"),t.space(),t.with_parens((function(){e.condition.print(t)})),t.space(),e.alternative?(!function(e,t){var n=e.body;if(t.option("braces")||t.option("ie8")&&n instanceof ye)return d(n,t);if(!n)return t.force_semicolon();for(;;)if(n instanceof je){if(!n.alternative)return void d(e.body,t);n=n.alternative}else{if(!(n instanceof Ae))break;n=n.body}p(e.body,t)}(e,t),t.space(),t.print("else"),t.space(),e.alternative instanceof je?e.alternative.print(t):p(e.alternative,t)):e._do_print_body(t)})),e(Ze,(function(e,t){t.print("switch"),t.space(),t.with_parens((function(){e.expression.print(t)})),t.space();var n=e.body.length-1;n<0?u(e,t):t.with_block((function(){e.body.forEach((function(e,i){t.indent(!0),e.print(t),i<n&&e.body.length>0&&t.newline()}))}))})),Je.DEFMETHOD("_do_print_body",(function(e){e.newline(),this.body.forEach((function(t){e.indent(),t.print(e),e.newline()}))})),e(Qe,(function(e,t){t.print("default:"),e._do_print_body(t)})),e(et,(function(e,t){t.print("case"),t.space(),e.expression.print(t),t.print(":"),e._do_print_body(t)})),e(tt,(function(e,t){t.print("try"),t.space(),c(e,t),e.bcatch&&(t.space(),e.bcatch.print(t)),e.bfinally&&(t.space(),e.bfinally.print(t))})),e(nt,(function(e,t){t.print("catch"),e.argname&&(t.space(),t.with_parens((function(){e.argname.print(t)}))),t.space(),c(e,t)})),e(it,(function(e,t){t.print("finally"),t.space(),c(e,t)})),rt.DEFMETHOD("_do_print",(function(e,t){e.print(t),e.space(),this.definitions.forEach((function(t,n){n&&e.comma(),t.print(e)}));var n=e.parent();(!(n instanceof Oe||n instanceof Fe)||n&&n.init!==this)&&e.semicolon()})),e(at,(function(e,t){e._do_print(t,"let")})),e(ot,(function(e,t){e._do_print(t,"var")})),e(st,(function(e,t){e._do_print(t,"const")})),e(ct,(function(e,t){t.print("import"),t.space(),e.imported_name&&e.imported_name.print(t),e.imported_name&&e.imported_names&&(t.print(","),t.space()),e.imported_names&&(1===e.imported_names.length&&"*"===e.imported_names[0].foreign_name.name?e.imported_names[0].print(t):(t.print("{"),e.imported_names.forEach((function(n,i){t.space(),n.print(t),i<e.imported_names.length-1&&t.print(",")})),t.space(),t.print("}"))),(e.imported_name||e.imported_names)&&(t.space(),t.print("from"),t.space()),e.module_name.print(t),t.semicolon()})),e(ut,(function(e,t){var n=t.parent()instanceof ct,i=e.name.definition();(i&&i.mangled_name||e.name.name)!==e.foreign_name.name?(n?t.print(e.foreign_name.name):e.name.print(t),t.space(),t.print("as"),t.space(),n?e.name.print(t):t.print(e.foreign_name.name)):e.name.print(t)})),e(lt,(function(e,t){if(t.print("export"),t.space(),e.is_default&&(t.print("default"),t.space()),e.exported_names)1===e.exported_names.length&&"*"===e.exported_names[0].name.name?e.exported_names[0].print(t):(t.print("{"),e.exported_names.forEach((function(n,i){t.space(),n.print(t),i<e.exported_names.length-1&&t.print(",")})),t.space(),t.print("}"));else if(e.exported_value)e.exported_value.print(t);else if(e.exported_definition&&(e.exported_definition.print(t),e.exported_definition instanceof rt))return;e.module_name&&(t.space(),t.print("from"),t.space(),e.module_name.print(t)),(e.exported_value&&!(e.exported_value instanceof Pe||e.exported_value instanceof Le||e.exported_value instanceof wt)||e.module_name||e.exported_names)&&t.semicolon()})),e(ft,(function(e,t){if(e.name.print(t),e.value){t.space(),t.print("="),t.space();var n=t.parent(1),i=n instanceof Oe||n instanceof Fe;l(e.value,t,i)}})),e(pt,(function(e,t){e.expression.print(t),e instanceof _t&&0===e.args.length||((e.expression instanceof pt||e.expression instanceof ke)&&t.add_mapping(e.start),t.with_parens((function(){e.args.forEach((function(e,n){n&&t.comma(),e.print(t)}))})))})),e(_t,(function(e,t){t.print("new"),t.space(),pt.prototype._codegen(e,t)})),dt.DEFMETHOD("_do_print",(function(e){this.expressions.forEach((function(t,n){n>0&&(e.comma(),e.should_break()&&(e.newline(),e.indent())),t.print(e)}))})),e(dt,(function(e,t){e._do_print(t)})),e(Et,(function(e,t){var n=e.expression;n.print(t);var i=e.property;t.option("ie8")&&O.has(i)?(t.print("["),t.add_mapping(e.end),t.print_string(i),t.print("]")):(n instanceof sn&&n.getValue()>=0&&(/[xa-f.)]/i.test(t.last())||t.print(".")),t.print("."),t.add_mapping(e.end),t.print_name(i))})),e(ht,(function(e,t){e.expression.print(t),t.print("["),e.property.print(t),t.print("]")})),e(gt,(function(e,t){var n=e.operator;t.print(n),(/^[a-z]/i.test(n)||/[+-]$/.test(n)&&e.expression instanceof gt&&/^[+-]/.test(e.expression.operator))&&t.space(),e.expression.print(t)})),e(St,(function(e,t){e.expression.print(t),t.print(e.operator)})),e(At,(function(e,t){var n=e.operator;e.left.print(t),">"==n[0]&&e.left instanceof St&&"--"==e.left.operator?t.print(" "):t.space(),t.print(n),("<"==n||"<<"==n)&&e.right instanceof gt&&"!"==e.right.operator&&e.right.expression instanceof gt&&"--"==e.right.expression.operator?t.print(" "):t.space(),e.right.print(t)})),e(vt,(function(e,t){e.condition.print(t),t.space(),t.print("?"),t.space(),e.consequent.print(t),t.space(),t.colon(),e.alternative.print(t)})),e(yt,(function(e,t){t.with_square((function(){var n=e.elements,i=n.length;i>0&&t.space(),n.forEach((function(e,n){n&&t.comma(),e.print(t),n===i-1&&e instanceof dn&&t.comma()})),i>0&&t.space()}))})),e(Ct,(function(e,t){e.properties.length>0?t.with_block((function(){e.properties.forEach((function(e,n){n&&(t.print(","),t.newline()),t.indent(),e.print(t)})),t.newline()})):u(e,t)})),e(wt,(function(e,t){if(t.print("class"),t.space(),e.name&&(e.name.print(t),t.space()),e.extends){var n=!(e.extends instanceof Jt||e.extends instanceof mt||e.extends instanceof kt||e.extends instanceof Le);t.print("extends"),n?t.print("("):t.space(),e.extends.print(t),n?t.print(")"):t.space()}e.properties.length>0?t.with_block((function(){e.properties.forEach((function(e,n){n&&t.newline(),t.indent(),e.print(t)})),t.newline()})):t.print("{}")})),e(Lt,(function(e,t){t.print("new.target")})),e(Ft,(function(e,t){function n(e){var t=e.definition();return t?t.mangled_name||t.name:e.name}var i=t.option("shorthand");i&&e.value instanceof It&&j(e.key)&&n(e.value)===e.key&&!O.has(e.key)?f(e.key,e.quote,t):i&&e.value instanceof bt&&e.value.left instanceof It&&j(e.key)&&n(e.value.left)===e.key?(f(e.key,e.quote,t),t.space(),t.print("="),t.space(),e.value.right.print(t)):(e.key instanceof fe?t.with_square((function(){e.key.print(t)})):f(e.key,e.quote,t),t.colon(),e.value.print(t))})),Ot.DEFMETHOD("_print_getter_setter",(function(e,t){var n=this;n.static&&(t.print("static"),t.space()),e&&(t.print(e),t.space()),n.key instanceof Xt?f(n.key.name,n.quote,t):t.with_square((function(){n.key.print(t)})),n.value._do_print(t,!0)})),e(Mt,(function(e,t){e._print_getter_setter("set",t)})),e(Rt,(function(e,t){e._print_getter_setter("get",t)})),e(Nt,(function(e,t){var n;e.is_generator&&e.async?n="async*":e.is_generator?n="*":e.async&&(n="async"),e._print_getter_setter(n,t)})),It.DEFMETHOD("_do_print",(function(e){var t=this.definition();e.print_name(t?t.mangled_name||t.name:this.name)})),e(It,(function(e,t){e._do_print(t)})),e(dn,a),e(nn,(function(e,t){t.print("this")})),e(rn,(function(e,t){t.print("super")})),e(on,(function(e,t){t.print(e.getValue())})),e(an,(function(e,n){n.print_string(e.getValue(),e.quote,t)})),e(sn,(function(e,t){i&&e.start&&null!=e.start.raw?t.print(e.start.raw):t.print(_(e.getValue()))})),e(un,(function(e,t){t.print(e.getValue()+"n")})),e(cn,(function(e,t){let{source:n,flags:i}=e.getValue();n=A(n),i=i?function(e){const t=new Set(e.split(""));let n="";for(const e of v)t.has(e)&&(n+=e,t.delete(e));return t.size&&t.forEach(e=>{n+=e}),n}(i):"",t.print(t.to_utf8(`/${n}/${i}`));const r=t.parent();r instanceof At&&/^\w/.test(r.operator)&&r.left===e&&t.print(" ")})),m([fe,ve,we],a),m([yt,ge,nt,wt,on,_e,rt,de,it,He,ke,_t,Ct,Ae,It,Ze,Je,Ue,Ge,tt],(function(e){e.add_mapping(this.start)})),m([Rt,Mt],(function(e){e.add_mapping(this.start,this.key.name)})),m([Ot],(function(e){e.add_mapping(this.start,this.key)}))}();const Ln=1,Vn=2;let Pn=null;class Bn{constructor(e,t,n){this.name=t.name,this.orig=[t],this.init=n,this.eliminated=0,this.assignments=0,this.scope=e,this.references=[],this.replaced=0,this.global=!1,this.export=0,this.mangled_name=null,this.undeclared=!1,this.id=Bn.next_id++,this.chained=!1,this.direct_access=!1,this.escaped=0,this.recursive_refs=0,this.references=[],this.should_replace=void 0,this.single_use=!1,this.fixed=!1,Object.seal(this)}unmangleable(e){return e||(e={}),!!(Pn&&Pn.has(this.id)&&g(e.keep_fnames,this.orig[0].name))||(this.global&&!e.toplevel||this.export&Ln||this.undeclared||!e.eval&&this.scope.pinned()||(this.orig[0]instanceof zt||this.orig[0]instanceof Ht)&&g(e.keep_fnames,this.orig[0].name)||this.orig[0]instanceof Xt||(this.orig[0]instanceof Yt||this.orig[0]instanceof Wt)&&g(e.keep_classnames,this.orig[0].name))}mangle(e){const t=e.cache&&e.cache.props;if(this.global&&t&&t.has(this.name))this.mangled_name=t.get(this.name);else if(!this.mangled_name&&!this.unmangleable(e)){var n=this.scope,i=this.orig[0];e.ie8&&i instanceof zt&&(n=n.parent_scope);const r=Kn(this);this.mangled_name=r?r.mangled_name||r.name:n.next_mangled(e,this),this.global&&t&&t.set(this.name,this.mangled_name)}}}function Kn(e){if(e.orig[0]instanceof qt&&e.scope.is_block_scope())return e.scope.get_defun_scope().variables.get(e.name)}function Un(e,t){var n=e.enclosed;e:for(;;){var i=Gn(++e.cname);if(!O.has(i)&&!t.reserved.has(i)){for(var r=n.length;--r>=0;){var o=n[r];if(i==(o.mangled_name||o.unmangleable(t)&&o.name))continue e}return i}}}Bn.next_id=1,we.DEFMETHOD("figure_out_scope",(function(e){e=o(e,{cache:null,ie8:!1,safari10:!1});var t=this,n=t.parent_scope=null,i=new Map,r=null,a=null,s=[],u=new An((function(t,o){if(t.is_block_scope()){const i=n;t.block_scope=n=new Ne(t);const r=t instanceof nt?i.parent_scope:i;if(n.init_scope_vars(r),n.uses_with=i.uses_with,n.uses_eval=i.uses_eval,e.safari10&&(t instanceof Oe||t instanceof Fe)&&s.push(n),t instanceof Ze){const e=n;n=i,t.expression.walk(u),n=e;for(let e=0;e<t.body.length;e++)t.body[e].walk(u)}else o();return n=i,!0}if(t instanceof Be){const e=a;return a=t,o(),a=e,!0}if(t instanceof Ne){t.init_scope_vars(n);var c=n,l=r,f=i;return r=n=t,i=new Map,o(),n=c,r=l,i=f,!0}if(t instanceof ve){var p=t.label;if(i.has(p.name))throw new Error(_("Label {name} defined twice",p));return i.set(p.name,p),o(),i.delete(p.name),!0}if(t instanceof Re)for(var d=n;d;d=d.parent_scope)d.uses_with=!0;else{if(t instanceof It&&(t.scope=n),t instanceof Zt&&(t.thedef=t,t.references=[]),t instanceof zt)r.def_function(t,"arguments"==t.name?void 0:r);else if(t instanceof Ht)h((t.scope=r.parent_scope.get_defun_scope()).def_function(t,r),1);else if(t instanceof Yt)h(r.def_variable(t,r),1);else if(t instanceof $t)n.def_variable(t);else if(t instanceof Wt)h((t.scope=r.parent_scope).def_function(t,r),1);else if(t instanceof Pt||t instanceof Ut||t instanceof Kt||t instanceof qt){if((m=t instanceof Bt?n.def_variable(t,null):r.def_variable(t,"SymbolVar"==t.TYPE?null:void 0)).orig.every(e=>e===t||(t instanceof Bt?e instanceof zt:!(e instanceof Ut||e instanceof Kt)))||Q(`"${t.name}" is redeclared`,t.start.file,t.start.line,t.start.col,t.start.pos),t instanceof Gt||h(m,2),r!==n){t.mark_enclosed(e);var m=n.find_variable(t);t.thedef!==m&&(t.thedef=m,t.reference(e))}}else if(t instanceof tn){var E=i.get(t.name);if(!E)throw new Error(_("Undefined label {name} [{line},{col}]",{name:t.name,line:t.start.line,col:t.start.col}));t.thedef=E}n instanceof we||!(t instanceof lt||t instanceof ct)||Q(`"${t.TYPE}" statement may only appear at the top level`,t.start.file,t.start.line,t.start.col,t.start.pos)}function h(e,t){if(a){var n=0;do{t++}while(u.parent(n++)!==a)}var i=u.parent(t);if(e.export=i instanceof lt?Ln:0){var r=i.exported_definition;(r instanceof Pe||r instanceof xt)&&i.is_default&&(e.export=Vn)}}}));t.walk(u),t.globals=new Map;u=new An((function(n,i){if(n instanceof Ye&&n.label)return n.label.thedef.references.push(n),!0;if(n instanceof Jt){var r,o=n.name;if("eval"==o&&u.parent()instanceof pt)for(var a=n.scope;a&&!a.uses_eval;a=a.parent_scope)a.uses_eval=!0;return u.parent()instanceof ut&&u.parent(1).module_name||!(r=n.scope.find_variable(o))?(r=t.def_global(n),n instanceof Qt&&(r.export=Ln)):r.scope instanceof ke&&"arguments"==o&&(r.scope.uses_arguments=!0),n.thedef=r,n.reference(e),!n.scope.is_block_scope()||r.orig[0]instanceof Bt||(n.scope=n.scope.get_defun_scope()),!0}var s;if(n instanceof qt&&(s=Kn(n.definition())))for(a=n.scope;a&&(p(a.enclosed,s),a!==s.scope);)a=a.parent_scope}));if(t.walk(u),(e.ie8||e.safari10)&&t.walk(new An((function(n,i){if(n instanceof qt){var r=n.name,o=n.thedef.references,a=n.scope.get_defun_scope(),s=a.find_variable(r)||t.globals.get(r)||a.def_variable(n);return o.forEach((function(t){t.thedef=s,t.reference(e)})),n.thedef=s,n.reference(e),!0}}))),e.safari10)for(const e of s)e.parent_scope.variables.forEach((function(t){p(e.enclosed,t)}))})),we.DEFMETHOD("def_global",(function(e){var t=this.globals,n=e.name;if(t.has(n))return t.get(n);var i=new Bn(this,e);return i.undeclared=!0,i.global=!0,t.set(n,i),i})),Ne.DEFMETHOD("init_scope_vars",(function(e){this.variables=new Map,this.functions=new Map,this.uses_with=!1,this.uses_eval=!1,this.parent_scope=e,this.enclosed=[],this.cname=-1,this._var_name_cache=null})),Ne.DEFMETHOD("var_names",(function e(){var t=this._var_name_cache;return t||(this._var_name_cache=t=new Set(this.parent_scope?e.call(this.parent_scope):null),this._added_var_names&&this._added_var_names.forEach(e=>{t.add(e)}),this.enclosed.forEach((function(e){t.add(e.name)})),this.variables.forEach((function(e,n){t.add(n)}))),t})),Ne.DEFMETHOD("add_var_name",(function(e){this._added_var_names||(this._added_var_names=new Set),this._added_var_names.add(e),this._var_name_cache||this.var_names(),this._var_name_cache.add(e)})),Ne.DEFMETHOD("add_child_scope",(function(e){if(e.parent_scope===this)return;e.parent_scope=this,e._var_name_cache=null,e._added_var_names&&e._added_var_names.forEach(t=>e.add_var_name(t));const t=new Set(e.enclosed),n=(()=>{const e=[];let t=this;do{e.push(t)}while(t=t.parent_scope);return e.reverse(),e})(),i=[];for(const e of n){i.forEach(t=>p(e.enclosed,t));for(const n of e.variables.values())t.has(n)&&(p(i,n),p(e.enclosed,n))}})),fe.DEFMETHOD("is_block_scope",s),wt.DEFMETHOD("is_block_scope",s),ke.DEFMETHOD("is_block_scope",s),we.DEFMETHOD("is_block_scope",s),Je.DEFMETHOD("is_block_scope",s),De.DEFMETHOD("is_block_scope",u),Te.DEFMETHOD("is_block_scope",u),ke.DEFMETHOD("init_scope_vars",(function(){Ne.prototype.init_scope_vars.apply(this,arguments),this.uses_arguments=!1,this.def_variable(new Gt({name:"arguments",start:this.start,end:this.end}))})),Ve.DEFMETHOD("init_scope_vars",(function(){Ne.prototype.init_scope_vars.apply(this,arguments),this.uses_arguments=!1})),It.DEFMETHOD("mark_enclosed",(function(e){for(var t=this.definition(),n=this.scope;n&&(p(n.enclosed,t),e.keep_fnames&&n.functions.forEach((function(n){g(e.keep_fnames,n.name)&&p(t.scope.enclosed,n)})),n!==t.scope);)n=n.parent_scope})),It.DEFMETHOD("reference",(function(e){this.definition().references.push(this),this.mark_enclosed(e)})),Ne.DEFMETHOD("find_variable",(function(e){return e instanceof It&&(e=e.name),this.variables.get(e)||this.parent_scope&&this.parent_scope.find_variable(e)})),Ne.DEFMETHOD("def_function",(function(e,t){var n=this.def_variable(e,t);return(!n.init||n.init instanceof Pe)&&(n.init=t),this.functions.set(e.name,n),n})),Ne.DEFMETHOD("def_variable",(function(e,t){var n=this.variables.get(e.name);return n?(n.orig.push(e),n.init&&(n.scope!==e.scope||n.init instanceof Le)&&(n.init=t)):(n=new Bn(this,e,t),this.variables.set(e.name,n),n.global=!this.parent_scope),e.thedef=n})),Ne.DEFMETHOD("next_mangled",(function(e){return Un(this,e)})),we.DEFMETHOD("next_mangled",(function(e){let t;const n=this.mangled_names;do{t=Un(this,e)}while(n.has(t));return t})),Le.DEFMETHOD("next_mangled",(function(e,t){for(var n=t.orig[0]instanceof Gt&&this.name&&this.name.definition(),i=n?n.mangled_name||n.name:null;;){var r=Un(this,e);if(!i||i!=r)return r}})),It.DEFMETHOD("unmangleable",(function(e){var t=this.definition();return!t||t.unmangleable(e)})),Zt.DEFMETHOD("unmangleable",s),It.DEFMETHOD("unreferenced",(function(){return!this.definition().references.length&&!this.scope.pinned()})),It.DEFMETHOD("definition",(function(){return this.thedef})),It.DEFMETHOD("global",(function(){return this.definition().global})),we.DEFMETHOD("_default_mangler_options",(function(e){return(e=o(e,{eval:!1,ie8:!1,keep_classnames:!1,keep_fnames:!1,module:!1,reserved:[],toplevel:!1})).module&&(e.toplevel=!0),Array.isArray(e.reserved)||e.reserved instanceof Set||(e.reserved=[]),e.reserved=new Set(e.reserved),e.reserved.add("arguments"),e})),we.DEFMETHOD("mangle_names",(function(e){e=this._default_mangler_options(e);var t=-1,n=[];e.keep_fnames&&(Pn=new Set);const i=this.mangled_names=new Set;e.cache&&(this.globals.forEach(o),e.cache.props&&e.cache.props.forEach((function(e){i.add(e)})));var r=new An((function(i,r){if(i instanceof ve){var a=t;return r(),t=a,!0}if(i instanceof Ne)i.variables.forEach(o);else if(i.is_block_scope())i.block_scope.variables.forEach(o);else if(Pn&&i instanceof ft&&i.value instanceof ke&&!i.value.name&&g(e.keep_fnames,i.name.name))Pn.add(i.name.definition().id);else{if(i instanceof Zt){let e;do{e=Gn(++t)}while(O.has(e));return i.mangled_name=e,!0}!e.ie8&&!e.safari10&&i instanceof qt&&n.push(i.definition())}}));function o(t){!(e.reserved.has(t.name)||t.export&Ln)&&n.push(t)}this.walk(r),n.forEach(t=>{t.mangle(e)}),Pn=null})),we.DEFMETHOD("find_colliding_names",(function(e){const t=e.cache&&e.cache.props,n=new Set;return e.reserved.forEach(i),this.globals.forEach(r),this.walk(new An((function(e){e instanceof Ne&&e.variables.forEach(r),e instanceof qt&&r(e.definition())}))),n;function i(e){n.add(e)}function r(n){var r=n.name;if(n.global&&t&&t.has(r))r=t.get(r);else if(!n.unmangleable(e))return;i(r)}})),we.DEFMETHOD("expand_names",(function(e){Gn.reset(),Gn.sort(),e=this._default_mangler_options(e);var t=this.find_colliding_names(e),n=0;function i(i){if(i.global&&e.cache)return;if(i.unmangleable(e))return;if(e.reserved.has(i.name))return;const r=Kn(i),o=i.name=r?r.name:function(){var e;do{e=Gn(n++)}while(t.has(e)||O.has(e));return e}();i.orig.forEach((function(e){e.name=o})),i.references.forEach((function(e){e.name=o}))}this.globals.forEach(i),this.walk(new An((function(e){e instanceof Ne&&e.variables.forEach(i),e instanceof qt&&i(e.definition())})))})),fe.DEFMETHOD("tail_node",c),dt.DEFMETHOD("tail_node",(function(){return this.expressions[this.expressions.length-1]})),we.DEFMETHOD("compute_char_frequency",(function(e){e=this._default_mangler_options(e);try{fe.prototype.print=function(t,n){this._print(t,n),this instanceof It&&!this.unmangleable(e)?Gn.consider(this.name,-1):e.properties&&(this instanceof Et?Gn.consider(this.property,-1):this instanceof ht&&function e(t){t instanceof an?Gn.consider(t.value,-1):t instanceof vt?(e(t.consequent),e(t.alternative)):t instanceof dt&&e(t.tail_node())}(this.property))},Gn.consider(this.print_to_string(),1)}finally{fe.prototype.print=fe.prototype._print}Gn.sort()}));const Gn=(()=>{const e="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_".split(""),t="0123456789".split("");let n,i;function r(){i=new Map,e.forEach((function(e){i.set(e,0)})),t.forEach((function(e){i.set(e,0)}))}function o(e,t){return i.get(t)-i.get(e)}function a(e){var t="",i=54;e++;do{t+=n[--e%i],e=Math.floor(e/i),i=64}while(e>0);return t}return a.consider=function(e,t){for(var n=e.length;--n>=0;)i.set(e[n],i.get(e[n])+t)},a.sort=function(){n=m(e,o).concat(m(t,o))},a.reset=r,r(),a})(),Hn=1,Xn=8,zn=16,Wn=32,Yn=256,qn=512,$n=1024,jn=Yn|qn|$n,Zn=(e,t)=>e.flags&t,Jn=(e,t)=>{e.flags|=t},Qn=(e,t)=>{e.flags&=~t};class ei extends An{constructor(e,t){super(),void 0===e.defaults||e.defaults||(t=!0),this.options=o(e,{arguments:!1,arrows:!t,booleans:!t,booleans_as_integers:!1,collapse_vars:!t,comparisons:!t,computed_props:!t,conditionals:!t,dead_code:!t,defaults:!0,directives:!t,drop_console:!1,drop_debugger:!t,ecma:5,evaluate:!t,expression:!1,global_defs:!1,hoist_funs:!1,hoist_props:!t,hoist_vars:!1,ie8:!1,if_return:!t,inline:!t,join_vars:!t,keep_classnames:!1,keep_fargs:!0,keep_fnames:!1,keep_infinity:!1,loops:!t,module:!1,negate_iife:!t,passes:1,properties:!t,pure_getters:!t&&"strict",pure_funcs:null,reduce_funcs:null,reduce_vars:!t,sequences:!t,side_effects:!t,switches:!t,top_retain:null,toplevel:!(!e||!e.top_retain),typeofs:!t,unsafe:!1,unsafe_arrows:!1,unsafe_comps:!1,unsafe_Function:!1,unsafe_math:!1,unsafe_methods:!1,unsafe_proto:!1,unsafe_regexp:!1,unsafe_undefined:!1,unused:!t,warnings:!1},!0);var n=this.options.global_defs;if("object"==typeof n)for(var i in n)"@"===i[0]&&D(n,i)&&(n[i.slice(1)]=ue(n[i],{expression:!0}));!0===this.options.inline&&(this.options.inline=3);var r=this.options.pure_funcs;this.pure_funcs="function"==typeof r?r:r?function(e){return!r.includes(e.expression.print_to_string())}:u;var a=this.options.top_retain;a instanceof RegExp?this.top_retain=function(e){return a.test(e.name)}:"function"==typeof a?this.top_retain=a:a&&("string"==typeof a&&(a=a.split(/,/)),this.top_retain=function(e){return a.includes(e.name)}),this.options.module&&(this.directives["use strict"]=!0,this.options.toplevel=!0);var s=this.options.toplevel;this.toplevel="string"==typeof s?{funcs:/funcs/.test(s),vars:/vars/.test(s)}:{funcs:s,vars:s};var c=this.options.sequences;this.sequences_limit=1==c?800:0|c,this.warnings_produced={},this.evaluated_regexps=new Map}option(e){return this.options[e]}exposed(e){if(e.export)return!0;if(e.global)for(var t=0,n=e.orig.length;t<n;t++)if(!this.toplevel[e.orig[t]instanceof Ht?"funcs":"vars"])return!0;return!1}in_boolean_context(){if(!this.option("booleans"))return!1;for(var e,t=this.self(),n=0;e=this.parent(n);n++){if(e instanceof me||e instanceof vt&&e.condition===t||e instanceof be&&e.condition===t||e instanceof Oe&&e.condition===t||e instanceof je&&e.condition===t||e instanceof gt&&"!"==e.operator&&e.expression===t)return!0;if(!(e instanceof At&&("&&"==e.operator||"||"==e.operator)||e instanceof vt||e.tail_node()===t))return!1;t=e}}compress(e){e=e.resolve_defines(this),this.option("expression")&&e.process_expression(!0);for(var t=+this.options.passes||1,n=1/0,i=!1,r={ie8:this.option("ie8")},o=0;o<t;o++)if(e.figure_out_scope(r),0===o&&this.option("drop_console")&&(e=e.drop_console()),(o>0||this.option("reduce_vars"))&&e.reset_opt_flags(this),e=e.transform(this),t>1){var a=0;if(e.walk(new An((function(){a++}))),this.info("pass "+o+": last_count: "+n+", count: "+a),a<n)n=a,i=!1;else{if(i)break;i=!0}}return this.option("expression")&&e.process_expression(!1),e}info(...e){"verbose"==this.options.warnings&&fe.warn(...e)}warn(e,t){if(this.options.warnings){var n=_(e,t);n in this.warnings_produced||(this.warnings_produced[n]=!0,fe.warn.apply(fe,arguments))}}clear_warnings(){this.warnings_produced={}}before(e,t,n){if(Zn(e,Yn))return e;var i=!1;e instanceof Ne&&(e=(e=e.hoist_properties(this)).hoist_declarations(this),i=!0),t(e,this),t(e,this);var r=e.optimize(this);return i&&r instanceof Ne&&(r.drop_unused(this),t(r,this)),r===e&&Jn(r,Yn),r}}function ti(e,t){e.DEFMETHOD("optimize",(function(e){if(Zn(this,qn))return this;if(e.has_directive("use asm"))return this;var n=t(this,e);return Jn(n,qn),n}))}function ni(e,t){if(!((t=Ci(t))instanceof fe)){var n;if(e instanceof yt){var i=e.elements;if("length"==t)return fi(i.length,e);"number"==typeof t&&t in i&&(n=i[t])}else if(e instanceof Ct){t=""+t;for(var r=e.properties,o=r.length;--o>=0;){if(!(r[o]instanceof Ft))return;n||r[o].key!==t||(n=r[o].value)}}return n instanceof Jt&&n.fixed_value()||n}}function ii(e,t,n,i,r,o){var a=t.parent(r),s=Ri(n,a);if(s)return s;if(!o&&a instanceof pt&&a.expression===n&&!(i instanceof Ve)&&!(i instanceof wt)&&!a.is_expr_pure(e)&&(!(i instanceof Le)||!(a instanceof _t)&&i.contains_this()))return!0;if(a instanceof yt)return ii(e,t,a,a,r+1);if(a instanceof Ft&&n===a.value){var u=t.parent(r+1);return ii(e,t,u,u,r+2)}if(a instanceof mt&&a.expression===n){var c=ni(i,a.property);return!o&&ii(e,t,a,c,r+1)}}function ri(e){return e instanceof Ve||e instanceof Le}function oi(e){if(e instanceof nn)return!0;if(e instanceof Jt)return e.definition().orig[0]instanceof zt;if(e instanceof mt){if((e=e.expression)instanceof Jt){if(e.is_immutable())return!1;e=e.fixed_value()}return!e||!(e instanceof cn)&&(e instanceof on||oi(e))}return!1}function ai(e,t){if(!(e instanceof Jt))return!1;for(var n=e.definition().orig,i=n.length;--i>=0;)if(n[i]instanceof t)return!0}function si(e,t){for(let n=0;;n++){const i=e.parent(n);if(i instanceof we)return t?i:void 0;if(i instanceof ke)return i;if(i.block_scope)return i.block_scope}}function ui(e,t){for(var n,i=0;(n=e.parent(i++))&&!(n instanceof Ne);)if(n instanceof nt&&n.argname){n=n.argname.definition().scope;break}return n.find_variable(t)}function ci(e,t,n){return n||(n={}),t&&(n.start||(n.start=t.start),n.end||(n.end=t.end)),new e(n)}function li(e,t){if(1==t.length)return t[0];if(0==t.length)throw new Error("trying to create a sequence with length zero!");return ci(dt,e,{expressions:t.reduce(_i,[])})}function fi(e,t){switch(typeof e){case"string":return ci(an,t,{value:e});case"number":return isNaN(e)?ci(pn,t):isFinite(e)?1/e<0?ci(gt,t,{operator:"-",expression:ci(sn,t,{value:-e})}):ci(sn,t,{value:e}):e<0?ci(gt,t,{operator:"-",expression:ci(mn,t)}):ci(mn,t);case"boolean":return ci(e?Dn:hn,t);case"undefined":return ci(_n,t);default:if(null===e)return ci(fn,t,{value:null});if(e instanceof RegExp)return ci(cn,t,{value:{source:A(e.source),flags:e.flags}});throw new Error(_("Can't handle constant of type: {type}",{type:typeof e}))}}function pi(e,t,n){return e instanceof gt&&"delete"==e.operator||e instanceof pt&&e.expression===t&&(n instanceof mt||n instanceof Jt&&"eval"==n.name)?li(t,[ci(sn,t,{value:0}),n]):n}function _i(e,t){return t instanceof dt?e.push(...t.expressions):e.push(t),e}function di(e){if(null===e)return[];if(e instanceof ge)return e.body;if(e instanceof Se)return[];if(e instanceof pe)return[e];throw new Error("Can't convert thing to statement array")}function mi(e){return null===e||(e instanceof Se||e instanceof ge&&0==e.body.length)}function Ei(e){return!(e instanceof xt||e instanceof Pe||e instanceof at||e instanceof st||e instanceof lt||e instanceof ct)}function hi(e){return e instanceof Te&&e.body instanceof ge?e.body:e}function Di(e){return"Call"==e.TYPE&&(e.expression instanceof Le||Di(e.expression))}function gi(e){return e instanceof Jt&&e.definition().undeclared}ti(fe,(function(e,t){return e})),we.DEFMETHOD("drop_console",(function(){return this.transform(new vn((function(e){if("Call"==e.TYPE){var t=e.expression;if(t instanceof mt){for(var n=t.expression;n.expression;)n=n.expression;if(gi(n)&&"console"==n.name)return ci(_n,e)}}})))})),fe.DEFMETHOD("equivalent_to",(function(e){return this.TYPE==e.TYPE&&this.print_to_string()==e.print_to_string()})),Ne.DEFMETHOD("process_expression",(function(e,t){var n=this,i=new vn((function(r){if(e&&r instanceof me)return ci(ze,r,{value:r.body});if(!e&&r instanceof ze){if(t){var o=r.value&&r.value.drop_side_effect_free(t,!0);return o?ci(me,r,{body:o}):ci(Se,r)}return ci(me,r,{body:r.value||ci(gt,r,{operator:"void",expression:ci(sn,r,{value:0})})})}if(r instanceof wt||r instanceof ke&&r!==n)return r;if(r instanceof De){var a=r.body.length-1;a>=0&&(r.body[a]=r.body[a].transform(i))}else r instanceof je?(r.body=r.body.transform(i),r.alternative&&(r.alternative=r.alternative.transform(i))):r instanceof Re&&(r.body=r.body.transform(i));return r}));n.transform(i)})),function(e){function t(e,t){t.assignments=0,t.chained=!1,t.direct_access=!1,t.escaped=0,t.recursive_refs=0,t.references=[],t.should_replace=void 0,t.single_use=void 0,t.scope.pinned()?t.fixed=!1:t.orig[0]instanceof Kt||!e.exposed(t)?t.fixed=t.init:t.fixed=!1}function n(e,n,i){i.variables.forEach((function(i){t(n,i),null===i.fixed?(e.defs_to_safe_ids.set(i,e.safe_ids),s(e,i,!0)):i.fixed&&(e.loop_ids.set(i.id,e.in_loop),s(e,i,!0))}))}function i(e,n){n.block_scope&&n.block_scope.variables.forEach((function(n){t(e,n)}))}function r(e){e.safe_ids=Object.create(e.safe_ids)}function o(e){e.safe_ids=Object.getPrototypeOf(e.safe_ids)}function s(e,t,n){e.safe_ids[t.id]=n}function u(e,t){if("m"==t.single_use)return!1;if(e.safe_ids[t.id]){if(null==t.fixed){var n=t.orig[0];if(n instanceof Gt||"arguments"==n.name)return!1;t.fixed=ci(_n,n)}return!0}return t.fixed instanceof Pe}function c(e,t,n,i){if(void 0===t.fixed)return!0;let r;return null===t.fixed&&(r=e.defs_to_safe_ids.get(t))?(r[t.id]=!1,e.defs_to_safe_ids.delete(t),!0):!!D(e.safe_ids,t.id)&&(!!u(e,t)&&(!1!==t.fixed&&(!(null!=t.fixed&&(!i||t.references.length>t.assignments))&&(t.fixed instanceof Pe?i instanceof fe&&t.fixed.parent_scope===n:t.orig.every(e=>!(e instanceof Kt||e instanceof Ht||e instanceof zt))))))}function l(e,t,n,i,r,o,a){var s=e.parent(o);if(r){if(r.is_constant())return;if(r instanceof kt)return}if(s instanceof Tt&&"="==s.operator&&i===s.right||s instanceof pt&&(i!==s.expression||s instanceof _t)||s instanceof Xe&&i===s.value&&i.scope!==t.scope||s instanceof ft&&i===s.value||s instanceof Sn&&i===s.value&&i.scope!==t.scope)return!(a>1)||r&&r.is_constant_expression(n)||(a=1),void((!t.escaped||t.escaped>a)&&(t.escaped=a));if(s instanceof yt||s instanceof gn||s instanceof At&&Fi.has(s.operator)||s instanceof vt&&i!==s.condition||s instanceof xe||s instanceof dt&&i===s.tail_node())l(e,t,n,s,s,o+1,a);else if(s instanceof Ft&&i===s.value){var u=e.parent(o+1);l(e,t,n,u,u,o+2,a)}else if(s instanceof mt&&i===s.expression&&(l(e,t,n,s,r=ni(r,s.property),o+1,a+1),r))return;o>0||s instanceof dt&&i!==s.tail_node()||s instanceof me||(t.direct_access=!0)}e(fe,a);var f=new An((function(e){if(e instanceof It){var t=e.definition();t&&(e instanceof Jt&&t.references.push(e),t.fixed=!1)}}));function p(e,t,i){Qn(this,zn);const r=e.safe_ids;return e.safe_ids=Object.create(null),n(e,i,this),t(),e.safe_ids=r,!0}function _(e,t,i){var a,u=this;return Qn(this,zn),r(e),n(e,i,u),u.uses_arguments?(t(),void o(e)):(!u.name&&(a=e.parent())instanceof pt&&a.expression===u&&!a.args.some(e=>e instanceof xe)&&u.argnames.every(e=>e instanceof It)&&u.argnames.forEach((function(t,n){if(t.definition){var i=t.definition();i.orig.length>1||(void 0!==i.fixed||u.uses_arguments&&!e.has_directive("use strict")?i.fixed=!1:(i.fixed=function(){return a.args[n]||ci(_n,a)},e.loop_ids.set(i.id,e.in_loop),s(e,i,!0)))}})),t(),o(e),!0)}e(Ie,(function(e,t,i){return r(e),n(e,i,this),t(),o(e),!0})),e(Tt,(function(e,t,n){var i=this;if(i.left instanceof Be)i.left.walk(f);else{var r=i.left;if(r instanceof Jt){var o=r.definition(),a=c(e,o,r.scope,i.right);if(o.assignments++,a){var u=o.fixed;if(u||"="==i.operator){var p="="==i.operator,_=p?i.right:i;if(!ii(n,e,i,_,0))return o.references.push(r),p||(o.chained=!0),o.fixed=p?function(){return i.right}:function(){return ci(At,i,{operator:i.operator.slice(0,-1),left:u instanceof fe?u:u(),right:i.right})},s(e,o,!1),i.right.walk(e),s(e,o,!0),l(e,o,r.scope,i,_,0,1),!0}}}}})),e(At,(function(e){if(Fi.has(this.operator))return this.left.walk(e),r(e),this.right.walk(e),o(e),!0})),e(De,(function(e,t,n){i(n,this)})),e(et,(function(e){return r(e),this.expression.walk(e),o(e),r(e),Ee(this,e),o(e),!0})),e(kt,(function(e,t){return Qn(this,zn),r(e),t(),o(e),!0})),e(vt,(function(e){return this.condition.walk(e),r(e),this.consequent.walk(e),o(e),r(e),this.alternative.walk(e),o(e),!0})),e(Qe,(function(e,t){return r(e),t(),o(e),!0})),e(xt,p),e(Pe,p),e(ye,(function(e,t,n){i(n,this);const a=e.in_loop;return e.in_loop=this,r(e),this.body.walk(e),Xi(this)&&(o(e),r(e)),this.condition.walk(e),o(e),e.in_loop=a,!0})),e(Oe,(function(e,t,n){i(n,this),this.init&&this.init.walk(e);const a=e.in_loop;return e.in_loop=this,r(e),this.condition&&this.condition.walk(e),this.body.walk(e),this.step&&(Xi(this)&&(o(e),r(e)),this.step.walk(e)),o(e),e.in_loop=a,!0})),e(Fe,(function(e,t,n){i(n,this),this.init.walk(f),this.object.walk(e);const a=e.in_loop;return e.in_loop=this,r(e),this.body.walk(e),o(e),e.in_loop=a,!0})),e(Le,_),e(Ve,_),e(je,(function(e){return this.condition.walk(e),r(e),this.body.walk(e),o(e),this.alternative&&(r(e),this.alternative.walk(e),o(e)),!0})),e(ve,(function(e){return r(e),this.body.walk(e),o(e),!0})),e(qt,(function(){this.definition().fixed=!1})),e(Jt,(function(e,t,n){var i,r,o=this.definition();o.references.push(this),1==o.references.length&&!o.fixed&&o.orig[0]instanceof Ht&&e.loop_ids.set(o.id,e.in_loop),void 0!==o.fixed&&u(e,o)?o.fixed&&((i=this.fixed_value())instanceof ke&&Yi(e,o)?o.recursive_refs++:i&&!n.exposed(o)&&function(e,t,n){return t.option("unused")&&!n.scope.pinned()&&n.references.length-n.recursive_refs==1&&e.loop_ids.get(n.id)===e.in_loop}(e,n,o)?o.single_use=!(i instanceof ke&&function(e,t,n){let i=si(e);const r=t.enclosed.filter(e=>!t.variables.has(e.name)).map(e=>e.name);if(!r.length)return!1;for(;i&&!(i instanceof we)&&i!==n;){if(r.some(e=>i.variables.has(e)))return!0;i=i.parent_scope}return!1}(e,i,o.scope))&&(i instanceof ke&&!i.pinned()||i instanceof wt||o.scope===this.scope&&i.is_constant_expression()):o.single_use=!1,ii(n,e,this,i,0,!!(r=i)&&(r.is_constant()||r instanceof ke||r instanceof nn))&&(o.single_use?o.single_use="m":o.fixed=!1)):o.fixed=!1,l(e,o,this.scope,this,i,0,1)})),e(we,(function(e,i,r){this.globals.forEach((function(e){t(r,e)})),n(e,r,this)})),e(tt,(function(e,t,n){return i(n,this),r(e),Ee(this,e),o(e),this.bcatch&&(r(e),this.bcatch.walk(e),o(e)),this.bfinally&&this.bfinally.walk(e),!0})),e(Dt,(function(e,t){var n=this;if("++"===n.operator||"--"===n.operator){var i=n.expression;if(i instanceof Jt){var r=i.definition(),o=c(e,r,i.scope,!0);if(r.assignments++,o){var a=r.fixed;if(a)return r.references.push(i),r.chained=!0,r.fixed=function(){return ci(At,n,{operator:n.operator.slice(0,-1),left:ci(gt,n,{operator:"+",expression:a instanceof fe?a:a()}),right:ci(sn,n,{value:1})})},s(e,r,!0),!0}}}})),e(ft,(function(e,t){var n=this;if(n.name instanceof Be)n.name.walk(f);else{var i=n.name.definition();if(n.value){if(c(e,i,n.name.scope,n.value))return i.fixed=function(){return n.value},e.loop_ids.set(i.id,e.in_loop),s(e,i,!1),t(),s(e,i,!0),!0;i.fixed=!1}}})),e(Ce,(function(e,t,n){i(n,this);const a=e.in_loop;return e.in_loop=this,r(e),t(),o(e),e.in_loop=a,!0}))}((function(e,t){e.DEFMETHOD("reduce_vars",t)})),we.DEFMETHOD("reset_opt_flags",(function(e){const t=this,n=e.option("reduce_vars"),i=new An((function(r,o){if(Qn(r,jn),n)return e.top_retain&&r instanceof Pe&&i.parent()===t&&Jn(r,$n),r.reduce_vars(i,o,e)}));i.safe_ids=Object.create(null),i.in_loop=null,i.loop_ids=new Map,i.defs_to_safe_ids=new Map,t.walk(i)})),It.DEFMETHOD("fixed_value",(function(){var e=this.definition().fixed;return!e||e instanceof fe?e:e()})),Jt.DEFMETHOD("is_immutable",(function(){var e=this.definition().orig;return 1==e.length&&e[0]instanceof zt}));var Si=E("Array Boolean clearInterval clearTimeout console Date decodeURI decodeURIComponent encodeURI encodeURIComponent Error escape eval EvalError Function isFinite isNaN JSON Math Number parseFloat parseInt RangeError ReferenceError RegExp Object setInterval setTimeout String SyntaxError TypeError unescape URIError");Jt.DEFMETHOD("is_declared",(function(e){return!this.definition().undeclared||e.option("unsafe")&&Si.has(this.name)}));var Ai,vi=E("Infinity NaN undefined");function Ti(e){return e instanceof mn||e instanceof pn||e instanceof _n}function bi(e,t){var n,r,o=t.find_parent(Ne).get_defun_scope();!function(){var e=t.self(),i=0;do{if(e instanceof nt||e instanceof it)i++;else if(e instanceof Te)n=!0;else{if(e instanceof Ne){o=e;break}e instanceof tt&&(r=!0)}}while(e=t.parent(i++))}();var a,s=10;do{a=!1,c(e),t.option("dead_code")&&p(e,t),t.option("if_return")&&l(e,t),t.sequences_limit>0&&(m(e,t),h(e,t)),t.option("join_vars")&&g(e),t.option("collapse_vars")&&u(e,t)}while(a&&s-- >0);function u(e,t){if(o.pinned())return e;for(var s,u=[],c=e.length,l=new vn((function(e,n){if(O)return e;if(!C)return e!==_[d]?e:++d<_.length?x(e):(C=!0,(h=function e(t,n,i){var r=l.parent(n);if(r instanceof Tt)return i&&!(r.left instanceof mt||S.has(r.left.name))?e(r,n+1,i):t;if(r instanceof At)return!i||Fi.has(r.operator)&&r.left!==t?t:e(r,n+1,i);if(r instanceof pt)return t;if(r instanceof et)return t;if(r instanceof vt)return i&&r.condition===t?e(r,n+1,i):t;if(r instanceof rt)return e(r,n+1,!0);if(r instanceof Xe)return i?e(r,n+1,i):t;if(r instanceof je)return i&&r.condition===t?e(r,n+1,i):t;if(r instanceof Te)return t;if(r instanceof dt)return e(r,n+1,r.tail_node()!==t);if(r instanceof me)return e(r,n+1,!0);if(r instanceof Ze)return t;if(r instanceof ft)return t;return null}(e,0))===e&&(O=!0),e);var i,s=l.parent();if(e instanceof Tt&&"="!=e.operator&&g.equivalent_to(e.left)||e instanceof gn||e instanceof pt&&g instanceof mt&&g.equivalent_to(e.expression)||e instanceof _e||e instanceof Be||e instanceof xe&&e.expression instanceof It&&e.expression.definition().references.length>1||e instanceof Te&&!(e instanceof Oe)||e instanceof Ye||e instanceof tt||e instanceof Re||e instanceof Sn||e instanceof lt||s instanceof Oe&&e!==s.init||!T&&e instanceof Jt&&!e.is_declared(t)&&!Bi.has(e))return O=!0,e;if(D||A&&T||!(s instanceof At&&Fi.has(s.operator)&&s.left!==e||s instanceof vt&&s.condition!==e||s instanceof je&&s.condition!==e)||(D=s),M&&!(e instanceof Vt)&&g.equivalent_to(e)){if(D)return O=!0,e;if(Ri(e,s))return E&&F++,e;if(F++,E&&m instanceof ft)return e;if(a=O=!0,t.info("Collapsing {name} [{file}:{line},{col}]",{name:e.print_to_string(),file:e.start.file,line:e.start.line,col:e.start.col}),m instanceof St)return ci(gt,m,m);if(m instanceof ft){var u=m.name.definition(),c=m.value;return u.references.length-u.replaced!=1||t.exposed(u)?ci(Tt,m,{operator:"=",left:ci(Jt,m.name,m.name),right:c}):(u.replaced++,y&&Ti(c)?c.transform(t):pi(s,e,c))}return Qn(m,Wn),m}return(e instanceof pt||e instanceof Xe&&(v||g instanceof mt||X(g))||e instanceof mt&&(v||e.expression.may_throw_on_access(t))||e instanceof Jt&&(S.get(e.name)||v&&X(e))||e instanceof ft&&e.value&&(S.has(e.name.name)||v&&X(e.name))||(i=Ri(e.left,e))&&(i instanceof mt||S.has(i.name))||b&&(r?e.has_side_effects(t):function e(t,n){if(t instanceof Tt)return e(t.left,!0);if(t instanceof Dt)return e(t.expression,!0);if(t instanceof ft)return t.value&&e(t.value);if(n){if(t instanceof Et)return e(t.expression,!0);if(t instanceof ht)return e(t.expression,!0);if(t instanceof Jt)return t.definition().scope!==o}return!1}(e)))&&(h=e,e instanceof Ne&&(O=!0)),x(e)}),(function(e){O||(h===e&&(O=!0),D===e&&(D=null))})),p=new vn((function(e){if(O)return e;if(!C){if(e!==_[d])return e;if(++d<_.length)return;return C=!0,e}return e instanceof Jt&&e.name==w.name?(--F||(O=!0),Ri(e,p.parent())?e:(w.replaced++,E.replaced--,m.value)):e instanceof Qe||e instanceof Ne?e:void 0}));--c>=0;){0==c&&t.option("unused")&&I();var _=[];for(L(e[c]);u.length>0;){_=u.pop();var d=0,m=_[_.length-1],E=null,h=null,D=null,g=V(m);if(g&&!oi(g)&&!g.has_side_effects(t)){var S=B(m),A=U(g);g instanceof Jt&&S.set(g.name,!1);var v=G(m),T=H(),b=m.may_throw(t),y=m.name instanceof Gt,C=y,O=!1,F=0,M=!s||!C;if(!M){for(var R=t.self().argnames.lastIndexOf(m.name)+1;!O&&R<s.length;R++)s[R].transform(l);M=!0}for(var N=c;!O&&N<e.length;N++)e[N].transform(l);if(E){var w=m.name.definition();if(O&&w.references.length-w.replaced>F)F=!1;else{O=!1,d=0,C=y;for(N=c;!O&&N<e.length;N++)e[N].transform(p);E.single_use=!1}}F&&!K(m)&&e.splice(c,1)}}}function x(e){if(e instanceof Ne)return e;if(e instanceof Ze){e.expression=e.expression.transform(l);for(var t=0,n=e.body.length;!O&&t<n;t++){var i=e.body[t];if(i instanceof et){if(!C){if(i!==_[d])continue;d++}if(i.expression=i.expression.transform(l),!T)break}}return O=!0,e}}function k(e,t,n){var i=!1,r=!(e instanceof Ve);return t.walk(new An((function(t,a){if(i)return!0;if(t instanceof Jt&&(e.variables.has(t.name)||function(e,t){if(e.global)return!1;let n=e.scope;for(;n&&n!==t;){if(n.variables.has(e.name))return!0;n=n.parent_scope}return!1}(t.definition(),e))){var s=t.definition().scope;if(s!==o)for(;s=s.parent_scope;)if(s===o)return!0;return i=!0}if((n||r)&&t instanceof nn)return i=!0;if(t instanceof Ne&&!(t instanceof Ve)){var u=r;return r=!1,a(),r=u,!0}}))),i}function I(){var e,n=t.self();if(ri(n)&&!n.name&&!n.uses_arguments&&!n.pinned()&&(e=t.parent())instanceof pt&&e.expression===n&&e.args.every(e=>!(e instanceof xe))){var r=t.has_directive("use strict");r&&!i(r,n.body)&&(r=!1);var o=n.argnames.length;s=e.args.slice(o);for(var a=new Set,c=o;--c>=0;){var l=n.argnames[c],f=e.args[c];const i=l.definition&&l.definition();if(!(i&&i.orig.length>1)&&(s.unshift(ci(ft,l,{name:l,value:f})),!a.has(l.name)))if(a.add(l.name),l instanceof xe){var p=e.args.slice(c);p.every(e=>!k(n,e,r))&&u.unshift([ci(ft,l,{name:l.expression,value:ci(yt,e,{elements:p})})])}else f?(f instanceof ke&&f.pinned()||k(n,f,r))&&(f=null):f=ci(_n,l).transform(t),f&&u.unshift([ci(ft,l,{name:l,value:f})])}}}function L(e){if(_.push(e),e instanceof Tt)e.left.has_side_effects(t)||u.push(_.slice()),L(e.right);else if(e instanceof At)L(e.left),L(e.right);else if(e instanceof pt)L(e.expression),e.args.forEach(L);else if(e instanceof et)L(e.expression);else if(e instanceof vt)L(e.condition),L(e.consequent),L(e.alternative);else if(!(e instanceof rt)||!t.option("unused")&&e instanceof st)e instanceof be?(L(e.condition),e.body instanceof De||L(e.body)):e instanceof Xe?e.value&&L(e.value):e instanceof Oe?(e.init&&L(e.init),e.condition&&L(e.condition),e.step&&L(e.step),e.body instanceof De||L(e.body)):e instanceof Fe?(L(e.object),e.body instanceof De||L(e.body)):e instanceof je?(L(e.condition),e.body instanceof De||L(e.body),!e.alternative||e.alternative instanceof De||L(e.alternative)):e instanceof dt?e.expressions.forEach(L):e instanceof me?L(e.body):e instanceof Ze?(L(e.expression),e.body.forEach(L)):e instanceof Dt?"++"!=e.operator&&"--"!=e.operator||u.push(_.slice()):e instanceof ft&&e.value&&(u.push(_.slice()),L(e.value));else{var n=e.definitions.length,i=n-200;for(i<0&&(i=0);i<n;i++)L(e.definitions[i])}_.pop()}function V(e){if(!(e instanceof ft&&e.name instanceof Vt)){const t=e[e instanceof Tt?"left":"expression"];return!ai(t,Kt)&&!ai(t,Ut)&&t}var n=e.name.definition();if(i(e.name,n.orig)){var r=n.references.length-n.replaced;if(r)return n.orig.length-n.eliminated>1&&!(e.name instanceof Gt)||(r>1?function(e){var t=e.value;if(t instanceof Jt&&"arguments"!=t.name){var n=t.definition();if(!n.undeclared)return E=n}}(e):!t.exposed(n))?ci(Jt,e.name,e.name):void 0}}function P(e){return e[e instanceof Tt?"right":"value"]}function B(e){var n=new Map;if(e instanceof Dt)return n;var i=new An((function(e,r){for(var o=e;o instanceof mt;)o=o.expression;(o instanceof Jt||o instanceof nn)&&n.set(o.name,n.get(o.name)||ii(t,i,e,e,0))}));return P(e).walk(i),n}function K(n){if(n.name instanceof Gt){var i=t.parent(),r=t.self().argnames,o=r.indexOf(n.name);if(o<0)i.args.length=Math.min(i.args.length,r.length-1);else{var a=i.args;a[o]&&(a[o]=ci(sn,a[o],{value:0}))}return!0}var s=!1;return e[c].transform(new vn((function(e,t,i){return s?e:e===n||e.body===n?(s=!0,e instanceof ft?(e.value=null,e):i?f.skip:null):void 0}),(function(e){if(e instanceof dt)switch(e.expressions.length){case 0:return null;case 1:return e.expressions[0]}})))}function U(e){for(;e instanceof mt;)e=e.expression;return e instanceof Jt&&e.definition().scope===o&&!(n&&(S.has(e.name)||m instanceof Dt||m instanceof Tt&&"="!=m.operator))}function G(e){return e instanceof Dt?Mi.has(e.operator):P(e).has_side_effects(t)}function H(){if(v)return!1;if(E)return!0;if(g instanceof Jt){var e=g.definition();if(e.references.length-e.replaced==(m instanceof ft?1:2))return!0}return!1}function X(e){if(!e.definition)return!0;var t=e.definition();return!(1==t.orig.length&&t.orig[0]instanceof Ht)&&(t.scope.get_defun_scope()!==o||!t.references.every(e=>{var t=e.scope.get_defun_scope();return"Scope"==t.TYPE&&(t=t.parent_scope),t===o}))}}function c(e){for(var t=[],n=0;n<e.length;){var i=e[n];i instanceof ge&&i.body.every(Ei)?(a=!0,c(i.body),e.splice(n,1,...i.body),n+=i.body.length):i instanceof Se?(a=!0,e.splice(n,1)):i instanceof de?t.indexOf(i.value)<0?(n++,t.push(i.value)):(a=!0,e.splice(n,1)):n++}}function l(e,t){for(var n=t.self(),i=function(e){for(var t=0,n=e.length;--n>=0;){var i=e[n];if(i instanceof je&&i.body instanceof ze&&++t>1)return!0}return!1}(e),r=n instanceof ke,o=e.length;--o>=0;){var s=e[o],u=g(o),c=e[u];if(r&&!c&&s instanceof ze){if(!s.value){a=!0,e.splice(o,1);continue}if(s.value instanceof gt&&"void"==s.value.operator){a=!0,e[o]=ci(me,s,{body:s.value.expression});continue}}if(s instanceof je){var l;if(E(l=Ki(s.body))){l.label&&d(l.label.thedef.references,l),a=!0,(s=s.clone()).condition=s.condition.negate(t);var f=D(s.body,l);s.body=ci(ge,s,{body:di(s.alternative).concat(h())}),s.alternative=ci(ge,s,{body:f}),e[o]=s.transform(t);continue}if(E(l=Ki(s.alternative))){l.label&&d(l.label.thedef.references,l),a=!0,(s=s.clone()).body=ci(ge,s.body,{body:di(s.body).concat(h())});f=D(s.alternative,l);s.alternative=ci(ge,s.alternative,{body:f}),e[o]=s.transform(t);continue}}if(s instanceof je&&s.body instanceof ze){var p=s.body.value;if(!p&&!s.alternative&&(r&&!c||c instanceof ze&&!c.value)){a=!0,e[o]=ci(me,s.condition,{body:s.condition});continue}if(p&&!s.alternative&&c instanceof ze&&c.value){a=!0,(s=s.clone()).alternative=c,e[o]=s.transform(t),e.splice(u,1);continue}if(p&&!s.alternative&&(!c&&r&&i||c instanceof ze)){a=!0,(s=s.clone()).alternative=c||ci(ze,s,{value:null}),e[o]=s.transform(t),c&&e.splice(u,1);continue}var m=e[S(o)];if(t.option("sequences")&&r&&!s.alternative&&m instanceof je&&m.body instanceof ze&&g(u)==e.length&&c instanceof me){a=!0,(s=s.clone()).alternative=ci(ge,c,{body:[c,ci(ze,c,{value:null})]}),e[o]=s.transform(t),e.splice(u,1);continue}}}function E(i){if(!i)return!1;for(var a=o+1,s=e.length;a<s;a++){var u=e[a];if(u instanceof st||u instanceof at)return!1}var c=i instanceof Ye?t.loopcontrol_target(i):null;return i instanceof ze&&r&&function(e){return!e||e instanceof gt&&"void"==e.operator}(i.value)||i instanceof $e&&n===hi(c)||i instanceof qe&&c instanceof ge&&n===c}function h(){var t=e.slice(o+1);return e.length=o+1,t.filter((function(t){return!(t instanceof Pe)||(e.push(t),!1)}))}function D(e,t){var n=di(e).slice(0,-1);return t.value&&n.push(ci(me,t.value,{body:t.value.expression})),n}function g(t){for(var n=t+1,i=e.length;n<i;n++){var r=e[n];if(!(r instanceof ot&&_(r)))break}return n}function S(t){for(var n=t;--n>=0;){var i=e[n];if(!(i instanceof ot&&_(i)))break}return n}}function p(e,t){for(var n,i=t.self(),r=0,o=0,s=e.length;r<s;r++){var u=e[r];if(u instanceof Ye){var c=t.loopcontrol_target(u);u instanceof qe&&!(c instanceof Te)&&hi(c)===i||u instanceof $e&&hi(c)===i?u.label&&d(u.label.thedef.references,u):e[o++]=u}else e[o++]=u;if(Ki(u)){n=e.slice(r+1);break}}e.length=o,a=o!=s,n&&n.forEach((function(n){yi(t,n,e)}))}function _(e){return e.definitions.every(e=>!e.value)}function m(e,t){if(!(e.length<2)){for(var n=[],i=0,r=0,o=e.length;r<o;r++){var s=e[r];if(s instanceof me){n.length>=t.sequences_limit&&c();var u=s.body;n.length>0&&(u=u.drop_side_effect_free(t)),u&&_i(n,u)}else s instanceof rt&&_(s)||s instanceof Pe?e[i++]=s:(c(),e[i++]=s)}c(),e.length=i,i!=o&&(a=!0)}function c(){if(n.length){var t=li(n[0],n);e[i++]=ci(me,t,{body:t}),n=[]}}}function E(e,t){if(!(e instanceof ge))return e;for(var n=null,i=0,r=e.body.length;i<r;i++){var o=e.body[i];if(o instanceof ot&&_(o))t.push(o);else{if(n)return!1;n=o}}return n}function h(e,t){function n(e){r--,a=!0;var n=i.body;return li(n,[n,e]).transform(t)}for(var i,r=0,o=0;o<e.length;o++){var s=e[o];if(i)if(s instanceof Xe)s.value=n(s.value||ci(_n,s).transform(t));else if(s instanceof Oe){if(!(s.init instanceof rt)){var u=!1;i.body.walk(new An((function(e){return!!(u||e instanceof Ne)||(e instanceof At&&"in"==e.operator?(u=!0,!0):void 0)}))),u||(s.init?s.init=n(s.init):(s.init=i.body,r--,a=!0))}}else s instanceof Fe?s.init instanceof st||s.init instanceof at||(s.object=n(s.object)):s instanceof je?s.condition=n(s.condition):s instanceof Ze?s.expression=n(s.expression):s instanceof Re&&(s.expression=n(s.expression));if(t.option("conditionals")&&s instanceof je){var c=[],l=E(s.body,c),f=E(s.alternative,c);if(!1!==l&&!1!==f&&c.length>0){var p=c.length;c.push(ci(je,s,{condition:s.condition,body:l||ci(Se,s.body),alternative:f})),c.unshift(r,1),[].splice.apply(e,c),o+=p,r+=p+1,i=null,a=!0;continue}}e[r++]=s,i=s instanceof me?s:null}e.length=r}function D(e,n){if(e instanceof rt){var i,r=e.definitions[e.definitions.length-1];if(r.value instanceof Ct)if(n instanceof Tt?i=[n]:n instanceof dt&&(i=n.expressions.slice()),i){var a=!1;do{var s=i[0];if(!(s instanceof Tt))break;if("="!=s.operator)break;if(!(s.left instanceof mt))break;var u=s.left.expression;if(!(u instanceof Jt))break;if(r.name.name!=u.name)break;if(!s.right.is_constant_expression(o))break;var c=s.left.property;if(c instanceof fe&&(c=c.evaluate(t)),c instanceof fe)break;c=""+c;var l=t.option("ecma")<6&&t.has_directive("use strict")?function(e){return e.key!=c&&e.key&&e.key.name!=c}:function(e){return e.key&&e.key.name!=c};if(!r.value.properties.every(l))break;var f=r.value.properties.filter((function(e){return e.key===c}))[0];f?f.value=new dt({start:f.start,expressions:[f.value.clone(),s.right.clone()],end:f.end}):r.value.properties.push(ci(Ft,s,{key:c,value:s.right})),i.shift(),a=!0}while(i.length);return a&&i}}}function g(e){for(var t,n=0,i=-1,r=e.length;n<r;n++){var o=e[n],s=e[i];if(o instanceof rt)s&&s.TYPE==o.TYPE?(s.definitions=s.definitions.concat(o.definitions),a=!0):t&&t.TYPE==o.TYPE&&_(o)?(t.definitions=t.definitions.concat(o.definitions),a=!0):(e[++i]=o,t=o);else if(o instanceof Xe)o.value=c(o.value);else if(o instanceof Oe){(u=D(s,o.init))?(a=!0,o.init=u.length?li(o.init,u):null,e[++i]=o):s instanceof ot&&(!o.init||o.init.TYPE==s.TYPE)?(o.init&&(s.definitions=s.definitions.concat(o.init.definitions)),o.init=s,e[i]=o,a=!0):t&&o.init&&t.TYPE==o.init.TYPE&&_(o.init)?(t.definitions=t.definitions.concat(o.init.definitions),o.init=null,e[++i]=o,a=!0):e[++i]=o}else if(o instanceof Fe)o.object=c(o.object);else if(o instanceof je)o.condition=c(o.condition);else if(o instanceof me){var u;if(u=D(s,o.body)){if(a=!0,!u.length)continue;o.body=li(o.body,u)}e[++i]=o}else o instanceof Ze?o.expression=c(o.expression):o instanceof Re?o.expression=c(o.expression):e[++i]=o}function c(t){e[++i]=o;var n=D(s,t);return n?(a=!0,n.length?li(t,n):t instanceof dt?t.tail_node().left:t.left):t}e.length=i+1}}function yi(e,t,n){t instanceof Pe||e.warn("Dropping unreachable code [{file}:{line},{col}]",t.start),t.walk(new An((function(i){return i instanceof ot?(e.warn("Declarations in unreachable code! [{file}:{line},{col}]",i.start),i.remove_initializers(),n.push(i),!0):i instanceof Pe&&(i===t||!e.has_directive("use strict"))?(n.push(i===t?i:ci(ot,i,{definitions:[ci(ft,i,{name:ci(Pt,i.name,i.name),value:null})]})),!0):i instanceof Ne||void 0})))}function Ci(e){return e instanceof on?e.getValue():e instanceof gt&&"void"==e.operator&&e.expression instanceof on?void 0:e}function Oi(e,t){return Zn(e,Xn)||e instanceof _n||e instanceof gt&&"void"==e.operator&&!e.expression.has_side_effects(t)}!function(e){function t(e){return/strict/.test(e.option("pure_getters"))}fe.DEFMETHOD("may_throw_on_access",(function(e){return!e.option("pure_getters")||this._dot_throw(e)})),e(fe,t),e(fn,u),e(_n,u),e(on,s),e(yt,s),e(Ct,(function(e){if(!t(e))return!1;for(var n=this.properties.length;--n>=0;)if(this.properties[n]._dot_throw(e))return!0;return!1})),e(Ot,s),e(Rt,u),e(xe,(function(e){return this.expression._dot_throw(e)})),e(Le,s),e(Ve,s),e(St,s),e(gt,(function(){return"void"==this.operator})),e(At,(function(e){return("&&"==this.operator||"||"==this.operator)&&(this.left._dot_throw(e)||this.right._dot_throw(e))})),e(Tt,(function(e){return"="==this.operator&&this.right._dot_throw(e)})),e(vt,(function(e){return this.consequent._dot_throw(e)||this.alternative._dot_throw(e)})),e(Et,(function(e){return!!t(e)&&!(this.expression instanceof Le&&"prototype"==this.property)})),e(dt,(function(e){return this.tail_node()._dot_throw(e)})),e(Jt,(function(e){if(Zn(this,Xn))return!0;if(!t(e))return!1;if(gi(this)&&this.is_declared(e))return!1;if(this.is_immutable())return!1;var n=this.fixed_value();return!n||n._dot_throw(e)}))}((function(e,t){e.DEFMETHOD("_dot_throw",t)})),function(e){const t=E("! delete"),n=E("in instanceof == != === !== < <= >= >");e(fe,s),e(gt,(function(){return t.has(this.operator)})),e(At,(function(){return n.has(this.operator)||Fi.has(this.operator)&&this.left.is_boolean()&&this.right.is_boolean()})),e(vt,(function(){return this.consequent.is_boolean()&&this.alternative.is_boolean()})),e(Tt,(function(){return"="==this.operator&&this.right.is_boolean()})),e(dt,(function(){return this.tail_node().is_boolean()})),e(Dn,u),e(hn,u)}((function(e,t){e.DEFMETHOD("is_boolean",t)})),function(e){e(fe,s),e(sn,u);var t=E("+ - ~ ++ --");e(Dt,(function(){return t.has(this.operator)}));var n=E("- * / % & | ^ << >> >>>");e(At,(function(e){return n.has(this.operator)||"+"==this.operator&&this.left.is_number(e)&&this.right.is_number(e)})),e(Tt,(function(e){return n.has(this.operator.slice(0,-1))||"="==this.operator&&this.right.is_number(e)})),e(dt,(function(e){return this.tail_node().is_number(e)})),e(vt,(function(e){return this.consequent.is_number(e)&&this.alternative.is_number(e)}))}((function(e,t){e.DEFMETHOD("is_number",t)})),(Ai=function(e,t){e.DEFMETHOD("is_string",t)})(fe,s),Ai(an,u),Ai(Ue,(function(){return 1===this.segments.length})),Ai(gt,(function(){return"typeof"==this.operator})),Ai(At,(function(e){return"+"==this.operator&&(this.left.is_string(e)||this.right.is_string(e))})),Ai(Tt,(function(e){return("="==this.operator||"+="==this.operator)&&this.right.is_string(e)})),Ai(dt,(function(e){return this.tail_node().is_string(e)})),Ai(vt,(function(e){return this.consequent.is_string(e)&&this.alternative.is_string(e)}));var Fi=E("&& ||"),Mi=E("delete ++ --");function Ri(e,t){return t instanceof Dt&&Mi.has(t.operator)?t.expression:t instanceof Tt&&t.left===e?e:void 0}function Ni(e,t){return e.print_to_string().length>t.print_to_string().length?t:e}function wi(e,t){return Ni(ci(me,e,{body:e}),ci(me,t,{body:t})).body}function xi(e,t,n){return(Mn(e)?wi:Ni)(t,n)}function ki(e){const t=new Map;for(var n of Object.keys(e))t.set(n,E(e[n]));return t}!function(e){function t(e,t){e.warn("global_defs "+t.print_to_string()+" redefined [{file}:{line},{col}]",t.start)}we.DEFMETHOD("resolve_defines",(function(e){return e.option("global_defs")?(this.figure_out_scope({ie8:e.option("ie8")}),this.transform(new vn((function(n){var i=n._find_defs(e,"");if(i){for(var r,o=0,a=n;(r=this.parent(o++))&&r instanceof mt&&r.expression===a;)a=r;if(!Ri(a,r))return i;t(e,n)}})))):this})),e(fe,a),e(Et,(function(e,t){return this.expression._find_defs(e,"."+this.property+t)})),e(Vt,(function(e){this.global()&&D(e.option("global_defs"),this.name)&&t(e,this)})),e(Jt,(function(e,t){if(this.global()){var n=e.option("global_defs"),i=this.name+t;return D(n,i)?function e(t,n){if(t instanceof fe)return ci(t.CTOR,n,t);if(Array.isArray(t))return ci(yt,n,{elements:t.map((function(t){return e(t,n)}))});if(t&&"object"==typeof t){var i=[];for(var r in t)D(t,r)&&i.push(ci(Ft,n,{key:r,value:e(t[r],n)}));return ci(Ct,n,{properties:i})}return fi(t,n)}(n[i],this):void 0}}))}((function(e,t){e.DEFMETHOD("_find_defs",t)}));var Ii=["constructor","toString","valueOf"],Li=ki({Array:["indexOf","join","lastIndexOf","slice"].concat(Ii),Boolean:Ii,Function:Ii,Number:["toExponential","toFixed","toPrecision"].concat(Ii),Object:Ii,RegExp:["test"].concat(Ii),String:["charAt","charCodeAt","concat","indexOf","italics","lastIndexOf","match","replace","search","slice","split","substr","substring","toLowerCase","toUpperCase","trim"].concat(Ii)}),Vi=ki({Array:["isArray"],Math:["abs","acos","asin","atan","ceil","cos","exp","floor","log","round","sin","sqrt","tan","atan2","pow","max","min"],Number:["isFinite","isNaN"],Object:["create","getOwnPropertyDescriptor","getOwnPropertyNames","getPrototypeOf","isExtensible","isFrozen","isSealed","keys"],String:["fromCharCode"]});!function(e){fe.DEFMETHOD("evaluate",(function(e){if(!e.option("evaluate"))return this;var t=this._eval(e,1);return!t||t instanceof RegExp?t:"function"==typeof t||"object"==typeof t?this:t}));var t=E("! ~ - + void");fe.DEFMETHOD("is_constant",(function(){return this instanceof on?!(this instanceof cn):this instanceof gt&&this.expression instanceof on&&t.has(this.operator)})),e(pe,(function(){throw new Error(_("Cannot evaluate a statement [{file}:{line},{col}]",this.start))})),e(ke,c),e(wt,c),e(fe,c),e(on,(function(){return this.getValue()})),e(cn,(function(e){let t=e.evaluated_regexps.get(this);if(void 0===t){try{t=(0,eval)(this.print_to_string())}catch(e){t=null}e.evaluated_regexps.set(this,t)}return t||this})),e(Ue,(function(){return 1!==this.segments.length?this:this.segments[0].value})),e(Le,(function(e){if(e.option("unsafe")){var t=function(){};return t.node=this,t.toString=function(){return this.node.print_to_string()},t}return this})),e(yt,(function(e,t){if(e.option("unsafe")){for(var n=[],i=0,r=this.elements.length;i<r;i++){var o=this.elements[i],a=o._eval(e,t);if(o===a)return this;n.push(a)}return n}return this})),e(Ct,(function(e,t){if(e.option("unsafe")){for(var n={},i=0,r=this.properties.length;i<r;i++){var o=this.properties[i];if(o instanceof xe)return this;var a=o.key;if(a instanceof It)a=a.name;else if(a instanceof fe&&(a=a._eval(e,t))===o.key)return this;if("function"==typeof Object.prototype[a])return this;if(!(o.value instanceof Le)&&(n[a]=o.value._eval(e,t),n[a]===o.value))return this}return n}return this}));var n=E("! typeof void");e(gt,(function(e,t){var i=this.expression;if(e.option("typeofs")&&"typeof"==this.operator&&(i instanceof ke||i instanceof Jt&&i.fixed_value()instanceof ke))return"function";if(n.has(this.operator)||t++,(i=i._eval(e,t))===this.expression)return this;switch(this.operator){case"!":return!i;case"typeof":return i instanceof RegExp?this:typeof i;case"void":return;case"~":return~i;case"-":return-i;case"+":return+i}return this}));var i=E("&& || === !==");e(At,(function(e,t){i.has(this.operator)||t++;var n=this.left._eval(e,t);if(n===this.left)return this;var r,o=this.right._eval(e,t);if(o===this.right)return this;switch(this.operator){case"&&":r=n&&o;break;case"||":r=n||o;break;case"|":r=n|o;break;case"&":r=n&o;break;case"^":r=n^o;break;case"+":r=n+o;break;case"*":r=n*o;break;case"**":r=Math.pow(n,o);break;case"/":r=n/o;break;case"%":r=n%o;break;case"-":r=n-o;break;case"<<":r=n<<o;break;case">>":r=n>>o;break;case">>>":r=n>>>o;break;case"==":r=n==o;break;case"===":r=n===o;break;case"!=":r=n!=o;break;case"!==":r=n!==o;break;case"<":r=n<o;break;case"<=":r=n<=o;break;case">":r=n>o;break;case">=":r=n>=o;break;default:return this}return isNaN(r)&&e.find_parent(Re)?this:r})),e(vt,(function(e,t){var n=this.condition._eval(e,t);if(n===this.condition)return this;var i=n?this.consequent:this.alternative,r=i._eval(e,t);return r===i?this:r})),e(Jt,(function(e,t){var n,i=this.fixed_value();if(!i)return this;if(D(i,"_eval"))n=i._eval();else{if(this._eval=c,n=i._eval(e,t),delete this._eval,n===i)return this;i._eval=function(){return n}}if(n&&"object"==typeof n){var r=this.definition().escaped;if(r&&t>r)return this}return n}));var r={Array:Array,Math:Math,Number:Number,Object:Object,String:String},o=ki({Math:["E","LN10","LN2","LOG2E","LOG10E","PI","SQRT1_2","SQRT2"],Number:["MAX_VALUE","MIN_VALUE","NaN","NEGATIVE_INFINITY","POSITIVE_INFINITY"]});e(mt,(function(e,t){if(e.option("unsafe")){var n=this.property;if(n instanceof fe&&(n=n._eval(e,t))===this.property)return this;var i,a=this.expression;if(gi(a)){var s,u="hasOwnProperty"===a.name&&"call"===n&&(s=e.parent()&&e.parent().args)&&s&&s[0]&&s[0].evaluate(e);if(null==(u=u instanceof Et?u.expression:u)||u.thedef&&u.thedef.undeclared)return this.clone();var c=o.get(a.name);if(!c||!c.has(n))return this;i=r[a.name]}else{if(!(i=a._eval(e,t+1))||i===a||!D(i,n))return this;if("function"==typeof i)switch(n){case"name":return i.node.name?i.node.name.name:"";case"length":return i.node.argnames.length;default:return this}}return i[n]}return this})),e(pt,(function(e,t){var n=this.expression;if(e.option("unsafe")&&n instanceof mt){var i,o=n.property;if(o instanceof fe&&(o=o._eval(e,t))===n.property)return this;var a=n.expression;if(gi(a)){var s="hasOwnProperty"===a.name&&"call"===o&&this.args[0]&&this.args[0].evaluate(e);if(null==(s=s instanceof Et?s.expression:s)||s.thedef&&s.thedef.undeclared)return this.clone();var u=Vi.get(a.name);if(!u||!u.has(o))return this;i=r[a.name]}else{if((i=a._eval(e,t+1))===a||!i)return this;var c=Li.get(i.constructor.name);if(!c||!c.has(o))return this}for(var l=[],f=0,p=this.args.length;f<p;f++){var _=this.args[f],d=_._eval(e,t);if(_===d)return this;l.push(d)}try{return i[o].apply(i,l)}catch(t){e.warn("Error evaluating {code} [{file}:{line},{col}]",{code:this.print_to_string(),file:this.start.file,line:this.start.line,col:this.start.col})}}return this})),e(_t,c)}((function(e,t){e.DEFMETHOD("_eval",t)})),function(e){function t(e){return ci(gt,e,{operator:"!",expression:e})}function n(e,n,i){var r=t(e);if(i){var o=ci(me,n,{body:n});return Ni(r,o)===o?n:r}return Ni(r,n)}e(fe,(function(){return t(this)})),e(pe,(function(){throw new Error("Cannot negate a statement")})),e(Le,(function(){return t(this)})),e(Ve,(function(){return t(this)})),e(gt,(function(){return"!"==this.operator?this.expression:t(this)})),e(dt,(function(e){var t=this.expressions.slice();return t.push(t.pop().negate(e)),li(this,t)})),e(vt,(function(e,t){var i=this.clone();return i.consequent=i.consequent.negate(e),i.alternative=i.alternative.negate(e),n(this,i,t)})),e(At,(function(e,i){var r=this.clone(),o=this.operator;if(e.option("unsafe_comps"))switch(o){case"<=":return r.operator=">",r;case"<":return r.operator=">=",r;case">=":return r.operator="<",r;case">":return r.operator="<=",r}switch(o){case"==":return r.operator="!=",r;case"!=":return r.operator="==",r;case"===":return r.operator="!==",r;case"!==":return r.operator="===",r;case"&&":return r.operator="||",r.left=r.left.negate(e,i),r.right=r.right.negate(e),n(this,r,i);case"||":return r.operator="&&",r.left=r.left.negate(e,i),r.right=r.right.negate(e),n(this,r,i)}return t(this)}))}((function(e,t){e.DEFMETHOD("negate",(function(e,n){return t.call(this,e,n)}))}));var Pi=E("Boolean decodeURI decodeURIComponent Date encodeURI encodeURIComponent Error escape EvalError isFinite isNaN Number Object parseFloat parseInt RangeError ReferenceError String SyntaxError TypeError unescape URIError");pt.DEFMETHOD("is_expr_pure",(function(e){if(e.option("unsafe")){var t=this.expression,n=this.args&&this.args[0]&&this.args[0].evaluate(e);if(t.expression&&"hasOwnProperty"===t.expression.name&&(null==n||n.thedef&&n.thedef.undeclared))return!1;if(gi(t)&&Pi.has(t.name))return!0;let i;if(t instanceof Et&&gi(t.expression)&&(i=Vi.get(t.expression.name))&&i.has(t.property))return!0}return!!T(this,Tn)||!e.pure_funcs(this)})),fe.DEFMETHOD("is_call_pure",s),Et.DEFMETHOD("is_call_pure",(function(e){if(!e.option("unsafe"))return;const t=this.expression;let n;return t instanceof yt?n=Li.get("Array"):t.is_boolean()?n=Li.get("Boolean"):t.is_number(e)?n=Li.get("Number"):t instanceof cn?n=Li.get("RegExp"):t.is_string(e)?n=Li.get("String"):this.may_throw_on_access(e)||(n=Li.get("Object")),n&&n.has(this.property)}));const Bi=new Set(["Number","String","Array","Object","Function","Promise"]);function Ki(e){return e&&e.aborts()}!function(e){function t(e,t){for(var n=e.length;--n>=0;)if(e[n].has_side_effects(t))return!0;return!1}e(fe,u),e(Se,s),e(on,s),e(nn,s),e(De,(function(e){return t(this.body,e)})),e(pt,(function(e){return!(this.is_expr_pure(e)||this.expression.is_call_pure(e)&&!this.expression.has_side_effects(e))||t(this.args,e)})),e(Ze,(function(e){return this.expression.has_side_effects(e)||t(this.body,e)})),e(et,(function(e){return this.expression.has_side_effects(e)||t(this.body,e)})),e(tt,(function(e){return t(this.body,e)||this.bcatch&&this.bcatch.has_side_effects(e)||this.bfinally&&this.bfinally.has_side_effects(e)})),e(je,(function(e){return this.condition.has_side_effects(e)||this.body&&this.body.has_side_effects(e)||this.alternative&&this.alternative.has_side_effects(e)})),e(ve,(function(e){return this.body.has_side_effects(e)})),e(me,(function(e){return this.body.has_side_effects(e)})),e(ke,s),e(wt,(function(e){return!!this.extends&&this.extends.has_side_effects(e)})),e(xt,u),e(At,(function(e){return this.left.has_side_effects(e)||this.right.has_side_effects(e)})),e(Tt,u),e(vt,(function(e){return this.condition.has_side_effects(e)||this.consequent.has_side_effects(e)||this.alternative.has_side_effects(e)})),e(Dt,(function(e){return Mi.has(this.operator)||this.expression.has_side_effects(e)})),e(Jt,(function(e){return!this.is_declared(e)&&!Bi.has(this.name)})),e(Vt,s),e(Ct,(function(e){return t(this.properties,e)})),e(Ot,(function(e){return!!(this instanceof Ft&&this.key instanceof fe&&this.key.has_side_effects(e))||this.value.has_side_effects(e)})),e(yt,(function(e){return t(this.elements,e)})),e(Et,(function(e){return this.expression.may_throw_on_access(e)||this.expression.has_side_effects(e)})),e(ht,(function(e){return this.expression.may_throw_on_access(e)||this.expression.has_side_effects(e)||this.property.has_side_effects(e)})),e(dt,(function(e){return t(this.expressions,e)})),e(rt,(function(e){return t(this.definitions,e)})),e(ft,(function(e){return this.value})),e(Ge,s),e(Ue,(function(e){return t(this.segments,e)}))}((function(e,t){e.DEFMETHOD("has_side_effects",t)})),function(e){function t(e,t){for(var n=e.length;--n>=0;)if(e[n].may_throw(t))return!0;return!1}e(fe,u),e(wt,s),e(on,s),e(Se,s),e(ke,s),e(Vt,s),e(nn,s),e(yt,(function(e){return t(this.elements,e)})),e(Tt,(function(e){return!!this.right.may_throw(e)||!(!e.has_directive("use strict")&&"="==this.operator&&this.left instanceof Jt)&&this.left.may_throw(e)})),e(At,(function(e){return this.left.may_throw(e)||this.right.may_throw(e)})),e(De,(function(e){return t(this.body,e)})),e(pt,(function(e){return!!t(this.args,e)||!this.is_expr_pure(e)&&(!!this.expression.may_throw(e)||(!(this.expression instanceof ke)||t(this.expression.body,e)))})),e(et,(function(e){return this.expression.may_throw(e)||t(this.body,e)})),e(vt,(function(e){return this.condition.may_throw(e)||this.consequent.may_throw(e)||this.alternative.may_throw(e)})),e(rt,(function(e){return t(this.definitions,e)})),e(Et,(function(e){return this.expression.may_throw_on_access(e)||this.expression.may_throw(e)})),e(je,(function(e){return this.condition.may_throw(e)||this.body&&this.body.may_throw(e)||this.alternative&&this.alternative.may_throw(e)})),e(ve,(function(e){return this.body.may_throw(e)})),e(Ct,(function(e){return t(this.properties,e)})),e(Ot,(function(e){return this.value.may_throw(e)})),e(ze,(function(e){return this.value&&this.value.may_throw(e)})),e(dt,(function(e){return t(this.expressions,e)})),e(me,(function(e){return this.body.may_throw(e)})),e(ht,(function(e){return this.expression.may_throw_on_access(e)||this.expression.may_throw(e)||this.property.may_throw(e)})),e(Ze,(function(e){return this.expression.may_throw(e)||t(this.body,e)})),e(Jt,(function(e){return!this.is_declared(e)&&!Bi.has(this.name)})),e(tt,(function(e){return this.bcatch?this.bcatch.may_throw(e):t(this.body,e)||this.bfinally&&this.bfinally.may_throw(e)})),e(Dt,(function(e){return!("typeof"==this.operator&&this.expression instanceof Jt)&&this.expression.may_throw(e)})),e(ft,(function(e){return!!this.value&&this.value.may_throw(e)}))}((function(e,t){e.DEFMETHOD("may_throw",t)})),function(e){function t(e){var t=this,n=!0;return t.walk(new An((function(r){if(!n)return!0;if(r instanceof Jt){if(Zn(t,zn))return n=!1,!0;var o=r.definition();if(i(o,t.enclosed)&&!t.variables.has(o.name)){if(e){var a=e.find_variable(r);if(o.undeclared?!a:a===o)return n="f",!0}n=!1}return!0}return r instanceof nn&&t instanceof Ve?(n=!1,!0):void 0}))),n}e(fe,s),e(on,u),e(wt,(function(e){return!(this.extends&&!this.extends.is_constant_expression(e))&&t.call(this,e)})),e(ke,t),e(Dt,(function(){return this.expression.is_constant_expression()})),e(At,(function(){return this.left.is_constant_expression()&&this.right.is_constant_expression()})),e(yt,(function(){return this.elements.every(e=>e.is_constant_expression())})),e(Ct,(function(){return this.properties.every(e=>e.is_constant_expression())})),e(Ot,(function(){return!(this.key instanceof fe)&&this.value.is_constant_expression()}))}((function(e,t){e.DEFMETHOD("is_constant_expression",t)})),function(e){function t(){for(var e=0;e<this.body.length;e++)if(Ki(this.body[e]))return this.body[e];return null}e(pe,l),e(He,c),e(ct,(function(){return null})),e(ge,t),e(Je,t),e(je,(function(){return this.alternative&&Ki(this.body)&&Ki(this.alternative)&&this}))}((function(e,t){e.DEFMETHOD("aborts",t)}));var Ui=new Set(["use asm","use strict"]);function Gi(e,t){return bi(e.body,t),t.option("side_effects")&&1==e.body.length&&e.body[0]===t.has_directive("use strict")&&(e.body.length=0),e}ti(de,(function(e,t){return!t.option("directives")||Ui.has(e.value)&&t.has_directive(e.value)===e?e:ci(Se,e)})),ti(_e,(function(e,t){return t.option("drop_debugger")?ci(Se,e):e})),ti(ve,(function(e,t){return e.body instanceof qe&&t.loopcontrol_target(e.body)===e.body?ci(Se,e):0==e.label.references.length?e.body:e})),ti(De,(function(e,t){return bi(e.body,t),e})),ti(ge,(function(e,t){switch(bi(e.body,t),e.body.length){case 1:if(!t.has_directive("use strict")&&t.parent()instanceof je&&!((n=e.body[0])instanceof st||n instanceof at||n instanceof wt)||Ei(e.body[0]))return e.body[0];break;case 0:return ci(Se,e)}var n;return e})),ti(ke,Gi);const Hi=/keep_assign/;function Xi(e,t){var n=!1,i=new An((function(t){return!!(n||t instanceof Ne)||(t instanceof Ye&&i.loopcontrol_target(t)===e?n=!0:void 0)}));return t instanceof ve&&i.push(t),i.push(e),e.body.walk(i),n}function zi(e,t){return t.top_retain&&e instanceof Pe&&Zn(e,$n)&&e.name&&t.top_retain(e.name)}Ne.DEFMETHOD("drop_unused",(function(e){if(!e.option("unused"))return;if(e.has_directive("use asm"))return;var t=this;if(t.pinned())return;var n=!(t instanceof we)||e.toplevel.funcs,i=!(t instanceof we)||e.toplevel.vars;const r=Hi.test(e.option("unused"))?s:function(e){return e instanceof Tt&&(Zn(e,Wn)||"="==e.operator)?e.left:e instanceof Dt&&Zn(e,Wn)?e.expression:void 0};var o=new Map,a=new Map;t instanceof we&&e.top_retain&&t.variables.forEach((function(t){e.top_retain(t)&&!o.has(t.id)&&o.set(t.id,t)}));var u=new Map,c=new Map,l=this,p=new An((function(r,s){if(r instanceof ke&&r.uses_arguments&&!p.has_directive("use strict")&&r.argnames.forEach((function(e){if(e instanceof Vt){var t=e.definition();o.has(t.id)||o.set(t.id,t)}})),r!==t){if(r instanceof Pe||r instanceof xt){var f=r.name.definition();return(p.parent()instanceof lt||!n&&l===t)&&f.global&&!o.has(f.id)&&o.set(f.id,f),r instanceof xt&&r.extends&&(r.extends.has_side_effects(e)||r.extends.may_throw(e))&&r.extends.walk(p),h(c,f.id,r),!0}if(r instanceof Gt&&l===t&&h(u,r.definition().id,r),r instanceof rt&&l===t){const t=p.parent()instanceof lt;return r.definitions.forEach((function(n){if(n.name instanceof Pt&&h(u,n.name.definition().id,n),!t&&i||n.name.walk(new An((function(e){if(e instanceof Vt){var n=e.definition();!t&&!n.global||o.has(n.id)||o.set(n.id,n)}}))),n.value){if(n.name instanceof Be)n.walk(p);else{var r=n.name.definition();h(c,r.id,n.value),r.chained||n.name.fixed_value()!==n.value||a.set(r.id,n)}n.value.has_side_effects(e)&&n.value.walk(p)}})),!0}return m(r,s)}}));t.walk(p),p=new An(m),o.forEach((function(e){var t=c.get(e.id);t&&t.forEach((function(e){e.walk(p)}))}));var _=new vn((function(s,c,p){var m=_.parent();if(i){const e=r(s);if(e instanceof Jt){var E=e.definition(),h=o.has(E.id);if(s instanceof Tt){if(!h||a.has(E.id)&&a.get(E.id)!==s)return pi(m,s,s.right.transform(_))}else if(!h)return p?f.skip:ci(sn,s,{value:0})}}if(l===t){if(s.name&&(s instanceof kt&&!g(e.option("keep_classnames"),(E=s.name.definition()).name)||s instanceof Le&&!g(e.option("keep_fnames"),(E=s.name.definition()).name))&&(!o.has(E.id)||E.orig.length>1)&&(s.name=null),s instanceof ke&&!(s instanceof Ie))for(var D=!e.option("keep_fargs"),S=s.argnames,A=S.length;--A>=0;){var v=S[A];v instanceof xe&&(v=v.expression),v instanceof bt&&(v=v.left),v instanceof Be||o.has(v.definition().id)?D=!1:(Jn(v,Hn),D&&(S.pop(),e[v.unreferenced()?"warn":"info"]("Dropping unused function argument {name} [{file}:{line},{col}]",M(v))))}if((s instanceof Pe||s instanceof xt)&&s!==t){const t=s.name.definition();if(!(t.global&&!n||o.has(t.id))){if(e[s.name.unreferenced()?"warn":"info"]("Dropping unused function {name} [{file}:{line},{col}]",M(s.name)),t.eliminated++,s instanceof xt){const t=s.drop_side_effect_free(e);if(t)return ci(me,s,{body:t})}return p?f.skip:ci(Se,s)}}if(s instanceof rt&&!(m instanceof Fe&&m.init===s)){var T=!(m instanceof we||s instanceof ot),b=[],y=[],C=[],O=[];switch(s.definitions.forEach((function(t){t.value&&(t.value=t.value.transform(_));var n=t.name instanceof Be,r=n?new Bn(null,{name:"<destructure>"}):t.name.definition();if(T&&r.global)return C.push(t);if(!i&&!T||n&&(t.name.names.length||t.name.is_array||1!=e.option("pure_getters"))||o.has(r.id)){if(t.value&&a.has(r.id)&&a.get(r.id)!==t&&(t.value=t.value.drop_side_effect_free(e)),t.name instanceof Pt){var c=u.get(r.id);if(c.length>1&&(!t.value||r.orig.indexOf(t.name)>r.eliminated)){if(e.warn("Dropping duplicated definition of variable {name} [{file}:{line},{col}]",M(t.name)),t.value){var l=ci(Jt,t.name,t.name);r.references.push(l);var f=ci(Tt,t,{operator:"=",left:l,right:t.value});a.get(r.id)===t&&a.set(r.id,f),O.push(f.transform(_))}return d(c,t),void r.eliminated++}}t.value?(O.length>0&&(C.length>0?(O.push(t.value),t.value=li(t.value,O)):b.push(ci(me,s,{body:li(s,O)})),O=[]),C.push(t)):y.push(t)}else if(r.orig[0]instanceof qt){(p=t.value&&t.value.drop_side_effect_free(e))&&O.push(p),t.value=null,y.push(t)}else{var p;(p=t.value&&t.value.drop_side_effect_free(e))?(n||e.warn("Side effects in initialization of unused variable {name} [{file}:{line},{col}]",M(t.name)),O.push(p)):n||e[t.name.unreferenced()?"warn":"info"]("Dropping unused variable {name} [{file}:{line},{col}]",M(t.name)),r.eliminated++}})),(y.length>0||C.length>0)&&(s.definitions=y.concat(C),b.push(s)),O.length>0&&b.push(ci(me,s,{body:li(s,O)})),b.length){case 0:return p?f.skip:ci(Se,s);case 1:return b[0];default:return p?f.splice(b):ci(ge,s,{body:b})}}if(s instanceof Oe)return c(s,this),s.init instanceof ge&&(F=s.init,s.init=F.body.pop(),F.body.push(s)),s.init instanceof me?s.init=s.init.body:mi(s.init)&&(s.init=null),F?p?f.splice(F.body):F:s;if(s instanceof ve&&s.body instanceof Oe){if(c(s,this),s.body instanceof ge){var F=s.body;return s.body=F.body.pop(),F.body.push(s),p?f.splice(F.body):F}return s}if(s instanceof ge)return c(s,this),p&&s.body.every(Ei)?f.splice(s.body):s;if(s instanceof Ne){const e=l;return l=s,c(s,this),l=e,s}}function M(e){return{name:e.name,file:e.start.file,line:e.start.line,col:e.start.col}}}));function m(e,n){var i;const s=r(e);if(s instanceof Jt&&!ai(e.left,Bt)&&t.variables.get(s.name)===(i=s.definition()))return e instanceof Tt&&(e.right.walk(p),i.chained||e.left.fixed_value()!==e.right||a.set(i.id,e)),!0;if(e instanceof Jt){if(i=e.definition(),!o.has(i.id)&&(o.set(i.id,i),i.orig[0]instanceof qt)){const e=i.scope.is_block_scope()&&i.scope.get_defun_scope().variables.get(i.name);e&&o.set(e.id,e)}return!0}if(e instanceof Ne){var u=l;return l=e,n(),l=u,!0}}t.transform(_)})),Ne.DEFMETHOD("hoist_declarations",(function(e){var t=this;if(e.has_directive("use asm"))return t;if(!Array.isArray(t.body))return t;var n=e.option("hoist_funs"),i=e.option("hoist_vars");if(n||i){var r=[],o=[],a=new Map,s=0,u=0;t.walk(new An((function(e){return e instanceof Ne&&e!==t||(e instanceof ot?(++u,!0):void 0)}))),i=i&&u>1;var c=new vn((function(u){if(u!==t){if(u instanceof de)return r.push(u),ci(Se,u);if(n&&u instanceof Pe&&!(c.parent()instanceof lt)&&c.parent()===t)return o.push(u),ci(Se,u);if(i&&u instanceof ot){u.definitions.forEach((function(e){e.name instanceof Be||(a.set(e.name.name,e),++s)}));var l=u.to_assignments(e),f=c.parent();if(f instanceof Fe&&f.init===u){if(null==l){var p=u.definitions[0].name;return ci(Jt,p,p)}return l}return f instanceof Oe&&f.init===u?l:l?ci(me,u,{body:l}):ci(Se,u)}if(u instanceof Ne)return u}}));if(t=t.transform(c),s>0){var l=[];const e=t instanceof ke,n=e?t.args_as_names():null;if(a.forEach((t,i)=>{e&&n.some(e=>e.name===t.name.name)?a.delete(i):((t=t.clone()).value=null,l.push(t),a.set(i,t))}),l.length>0){for(var f=0;f<t.body.length;){if(t.body[f]instanceof me){var p,_,m=t.body[f].body;if(m instanceof Tt&&"="==m.operator&&(p=m.left)instanceof It&&a.has(p.name)){if((E=a.get(p.name)).value)break;E.value=m.right,d(l,E),l.push(E),t.body.splice(f,1);continue}if(m instanceof dt&&(_=m.expressions[0])instanceof Tt&&"="==_.operator&&(p=_.left)instanceof It&&a.has(p.name)){var E;if((E=a.get(p.name)).value)break;E.value=_.right,d(l,E),l.push(E),t.body[f].body=li(m,m.expressions.slice(1));continue}}if(t.body[f]instanceof Se)t.body.splice(f,1);else{if(!(t.body[f]instanceof ge))break;var h=[f,1].concat(t.body[f].body);t.body.splice.apply(t.body,h)}}l=ci(ot,t,{definitions:l}),o.push(l)}}t.body=r.concat(o,t.body)}return t})),Ne.DEFMETHOD("make_var_name",(function(e){for(var t=this.var_names(),n=e=e.replace(/(?:^[^a-z_$]|[^a-z0-9_$])/gi,"_"),i=0;t.has(n);i++)n=e+"$"+i;return this.add_var_name(n),n})),Ne.DEFMETHOD("hoist_properties",(function(e){var t=this;if(!e.option("hoist_props")||e.has_directive("use asm"))return t;var n=t instanceof we&&e.top_retain||s,i=new Map,r=new vn((function(o,a){if(o instanceof rt&&r.parent()instanceof lt)return o;if(o instanceof ft){const r=o.name;let u,c;if(r.scope===t&&1!=(u=r.definition()).escaped&&!u.assignments&&!u.direct_access&&!u.single_use&&!e.exposed(u)&&!n(u)&&(c=r.fixed_value())===o.value&&c instanceof Ct&&c.properties.every(e=>"string"==typeof e.key)){a(o,this);const e=new Map,t=[];return c.properties.forEach((function(n){t.push(ci(ft,o,{name:s(r,n.key,e),value:n.value}))})),i.set(u.id,e),f.splice(t)}}else if(o instanceof mt&&o.expression instanceof Jt){const e=i.get(o.expression.definition().id);if(e){const t=e.get(String(Ci(o.property))),n=ci(Jt,o,{name:t.name,scope:o.expression.scope,thedef:t});return n.reference({}),n}}function s(e,n,i){const r=ci(e.CTOR,e,{name:t.make_var_name(e.name+"_"+n),scope:t}),o=t.def_variable(r);return i.set(String(n),o),t.enclosed.push(o),r}}));return t.transform(r)})),function(e){function t(e,t,n){var i=e.length;if(!i)return null;for(var r=[],o=!1,a=0;a<i;a++){var s=e[a].drop_side_effect_free(t,n);o|=s!==e[a],s&&(r.push(s),n=!1)}return o?r.length?r:null:e}e(fe,c),e(on,l),e(nn,l),e(pt,(function(e,n){if(!this.is_expr_pure(e)){if(this.expression.is_call_pure(e)){var i=this.args.slice();return i.unshift(this.expression.expression),(i=t(i,e,n))&&li(this,i)}if(ri(this.expression)&&(!this.expression.name||!this.expression.name.definition().references.length)){var r=this.clone();return r.expression.process_expression(!1,e),r}return this}T(this,Tn)&&e.warn("Dropping __PURE__ call [{file}:{line},{col}]",this.start);var o=t(this.args,e,n);return o&&li(this,o)})),e(Ie,l),e(Le,l),e(Ve,l),e(wt,(function(e){return this.extends?this.extends.drop_side_effect_free(e):null})),e(At,(function(e,t){var n=this.right.drop_side_effect_free(e);if(!n)return this.left.drop_side_effect_free(e,t);if(Fi.has(this.operator)){if(n===this.right)return this;var i=this.clone();return i.right=n,i}var r=this.left.drop_side_effect_free(e,t);return r?li(this,[r,n]):this.right.drop_side_effect_free(e,t)})),e(Tt,(function(e){var t=this.left;if(t.has_side_effects(e)||e.has_directive("use strict")&&t instanceof mt&&t.expression.is_constant())return this;for(Jn(this,Wn);t instanceof mt;)t=t.expression;return t.is_constant_expression(e.find_parent(Ne))?this.right.drop_side_effect_free(e):this})),e(vt,(function(e){var t=this.consequent.drop_side_effect_free(e),n=this.alternative.drop_side_effect_free(e);if(t===this.consequent&&n===this.alternative)return this;if(!t)return n?ci(At,this,{operator:"||",left:this.condition,right:n}):this.condition.drop_side_effect_free(e);if(!n)return ci(At,this,{operator:"&&",left:this.condition,right:t});var i=this.clone();return i.consequent=t,i.alternative=n,i})),e(Dt,(function(e,t){if(Mi.has(this.operator))return this.expression.has_side_effects(e)?Qn(this,Wn):Jn(this,Wn),this;if("typeof"==this.operator&&this.expression instanceof Jt)return null;var n=this.expression.drop_side_effect_free(e,t);return t&&n&&Di(n)?n===this.expression&&"!"==this.operator?this:n.negate(e,t):n})),e(Jt,(function(e){return this.is_declared(e)||Bi.has(this.name)?null:this})),e(Ct,(function(e,n){var i=t(this.properties,e,n);return i&&li(this,i)})),e(Ot,(function(e,t){const n=this instanceof Ft&&this.key instanceof fe&&this.key.drop_side_effect_free(e,t),i=this.value.drop_side_effect_free(e,t);return n&&i?li(this,[n,i]):n||i})),e(yt,(function(e,n){var i=t(this.elements,e,n);return i&&li(this,i)})),e(Et,(function(e,t){return this.expression.may_throw_on_access(e)?this:this.expression.drop_side_effect_free(e,t)})),e(ht,(function(e,t){if(this.expression.may_throw_on_access(e))return this;var n=this.expression.drop_side_effect_free(e,t);if(!n)return this.property.drop_side_effect_free(e,t);var i=this.property.drop_side_effect_free(e);return i?li(this,[n,i]):n})),e(dt,(function(e){var t=this.tail_node(),n=t.drop_side_effect_free(e);if(n===t)return this;var i=this.expressions.slice(0,-1);return n&&i.push(n),i.length?li(this,i):ci(sn,this,{value:0})})),e(xe,(function(e,t){return this.expression.drop_side_effect_free(e,t)})),e(Ge,l),e(Ue,(function(e){var n=t(this.segments,e,Mn);return n&&li(this,n)}))}((function(e,t){e.DEFMETHOD("drop_side_effect_free",t)})),ti(me,(function(e,t){if(t.option("side_effects")){var n=e.body,i=n.drop_side_effect_free(t,!0);if(!i)return t.warn("Dropping side-effect-free statement [{file}:{line},{col}]",e.start),ci(Se,e);if(i!==n)return ci(me,e,{body:i})}return e})),ti(Ce,(function(e,t){return t.option("loops")?ci(Oe,e,e).optimize(t):e})),ti(ye,(function(e,t){if(!t.option("loops"))return e;var n=e.condition.tail_node().evaluate(t);if(!(n instanceof fe)){if(n)return ci(Oe,e,{body:ci(ge,e.body,{body:[e.body,ci(me,e.condition,{body:e.condition})]})}).optimize(t);if(!Xi(e,t.parent()))return ci(ge,e.body,{body:[e.body,ci(me,e.condition,{body:e.condition})]}).optimize(t)}return e})),ti(Oe,(function(e,t){if(!t.option("loops"))return e;if(t.option("side_effects")&&e.init&&(e.init=e.init.drop_side_effect_free(t)),e.condition){var n=e.condition.evaluate(t);if(!(n instanceof fe))if(n)e.condition=null;else if(!t.option("dead_code")){var i=e.condition;e.condition=fi(n,e.condition),e.condition=Ni(e.condition.transform(t),i)}if(t.option("dead_code")&&(n instanceof fe&&(n=e.condition.tail_node().evaluate(t)),!n)){var r=[];return yi(t,e.body,r),e.init instanceof pe?r.push(e.init):e.init&&r.push(ci(me,e.init,{body:e.init})),r.push(ci(me,e.condition,{body:e.condition})),ci(ge,e,{body:r}).optimize(t)}}return function e(t,n){var i=t.body instanceof ge?t.body.body[0]:t.body;if(n.option("dead_code")&&o(i)){var r=[];return t.init instanceof pe?r.push(t.init):t.init&&r.push(ci(me,t.init,{body:t.init})),t.condition&&r.push(ci(me,t.condition,{body:t.condition})),yi(n,t.body,r),ci(ge,t,{body:r})}return i instanceof je&&(o(i.body)?(t.condition?t.condition=ci(At,t.condition,{left:t.condition,operator:"&&",right:i.condition.negate(n)}):t.condition=i.condition.negate(n),a(i.alternative)):o(i.alternative)&&(t.condition?t.condition=ci(At,t.condition,{left:t.condition,operator:"&&",right:i.condition}):t.condition=i.condition,a(i.body))),t;function o(e){return e instanceof qe&&n.loopcontrol_target(e)===n.self()}function a(i){i=di(i),t.body instanceof ge?(t.body=t.body.clone(),t.body.body=i.concat(t.body.body.slice(1)),t.body=t.body.transform(n)):t.body=ci(ge,t.body,{body:i}).transform(n),t=e(t,n)}}(e,t)})),ti(je,(function(e,t){if(mi(e.alternative)&&(e.alternative=null),!t.option("conditionals"))return e;var n=e.condition.evaluate(t);if(!(t.option("dead_code")||n instanceof fe)){var i=e.condition;e.condition=fi(n,i),e.condition=Ni(e.condition.transform(t),i)}if(t.option("dead_code")){if(n instanceof fe&&(n=e.condition.tail_node().evaluate(t)),!n){t.warn("Condition always false [{file}:{line},{col}]",e.condition.start);var r=[];return yi(t,e.body,r),r.push(ci(me,e.condition,{body:e.condition})),e.alternative&&r.push(e.alternative),ci(ge,e,{body:r}).optimize(t)}if(!(n instanceof fe))return t.warn("Condition always true [{file}:{line},{col}]",e.condition.start),(r=[]).push(ci(me,e.condition,{body:e.condition})),r.push(e.body),e.alternative&&yi(t,e.alternative,r),ci(ge,e,{body:r}).optimize(t)}var o=e.condition.negate(t),a=e.condition.print_to_string().length,s=o.print_to_string().length,u=s<a;if(e.alternative&&u){u=!1,e.condition=o;var c=e.body;e.body=e.alternative||ci(Se,e),e.alternative=c}if(mi(e.body)&&mi(e.alternative))return ci(me,e.condition,{body:e.condition.clone()}).optimize(t);if(e.body instanceof me&&e.alternative instanceof me)return ci(me,e,{body:ci(vt,e,{condition:e.condition,consequent:e.body.body,alternative:e.alternative.body})}).optimize(t);if(mi(e.alternative)&&e.body instanceof me)return a===s&&!u&&e.condition instanceof At&&"||"==e.condition.operator&&(u=!0),u?ci(me,e,{body:ci(At,e,{operator:"||",left:o,right:e.body.body})}).optimize(t):ci(me,e,{body:ci(At,e,{operator:"&&",left:e.condition,right:e.body.body})}).optimize(t);if(e.body instanceof Se&&e.alternative instanceof me)return ci(me,e,{body:ci(At,e,{operator:"||",left:e.condition,right:e.alternative.body})}).optimize(t);if(e.body instanceof Xe&&e.alternative instanceof Xe&&e.body.TYPE==e.alternative.TYPE)return ci(e.body.CTOR,e,{value:ci(vt,e,{condition:e.condition,consequent:e.body.value||ci(_n,e.body),alternative:e.alternative.value||ci(_n,e.alternative)}).transform(t)}).optimize(t);if(e.body instanceof je&&!e.body.alternative&&!e.alternative&&(e=ci(je,e,{condition:ci(At,e.condition,{operator:"&&",left:e.condition,right:e.body.condition}),body:e.body.body,alternative:null})),Ki(e.body)&&e.alternative){var l=e.alternative;return e.alternative=null,ci(ge,e,{body:[e,l]}).optimize(t)}if(Ki(e.alternative)){r=e.body;return e.body=e.alternative,e.condition=u?o:e.condition.negate(t),e.alternative=null,ci(ge,e,{body:[e,r]}).optimize(t)}return e})),ti(Ze,(function(e,t){if(!t.option("switches"))return e;var n,i=e.expression.evaluate(t);if(!(i instanceof fe)){var r=e.expression;e.expression=fi(i,r),e.expression=Ni(e.expression.transform(t),r)}if(!t.option("dead_code"))return e;i instanceof fe&&(i=e.expression.tail_node().evaluate(t));for(var o,a,s=[],u=[],c=0,l=e.body.length;c<l&&!a;c++){if((n=e.body[c])instanceof Qe)o?D(n,u[u.length-1]):o=n;else if(!(i instanceof fe)){if(!((E=n.expression.evaluate(t))instanceof fe)&&E!==i){D(n,u[u.length-1]);continue}if(E instanceof fe&&(E=n.expression.tail_node().evaluate(t)),E===i&&(a=n,o)){var f=u.indexOf(o);u.splice(f,1),D(o,u[f-1]),o=null}}if(Ki(n)){var p=u[u.length-1];Ki(p)&&p.body.length==n.body.length&&ci(ge,p,p).equivalent_to(ci(ge,n,n))&&(p.body=[])}u.push(n)}for(;c<l;)D(e.body[c++],u[u.length-1]);for(u.length>0&&(u[0].body=s.concat(u[0].body)),e.body=u;n=u[u.length-1];){var _=n.body[n.body.length-1];if(_ instanceof qe&&t.loopcontrol_target(_)===e&&n.body.pop(),n.body.length||n instanceof et&&(o||n.expression.has_side_effects(t)))break;u.pop()===o&&(o=null)}if(0==u.length)return ci(ge,e,{body:s.concat(ci(me,e.expression,{body:e.expression}))}).optimize(t);if(1==u.length&&(u[0]===a||u[0]===o)){var d=!1,m=new An((function(t){if(d||t instanceof ke||t instanceof me)return!0;t instanceof qe&&m.loopcontrol_target(t)===e&&(d=!0)}));if(e.walk(m),!d){var E,h=u[0].body.slice();return(E=u[0].expression)&&h.unshift(ci(me,E,{body:E})),h.unshift(ci(me,e.expression,{body:e.expression})),ci(ge,e,{body:h}).optimize(t)}}return e;function D(e,n){n&&!Ki(n)?n.body=n.body.concat(e.body):yi(t,e,s)}})),ti(tt,(function(e,t){if(bi(e.body,t),e.bcatch&&e.bfinally&&e.bfinally.body.every(mi)&&(e.bfinally=null),t.option("dead_code")&&e.body.every(mi)){var n=[];return e.bcatch&&yi(t,e.bcatch,n),e.bfinally&&n.push(...e.bfinally.body),ci(ge,e,{body:n}).optimize(t)}return e})),rt.DEFMETHOD("remove_initializers",(function(){var e=[];this.definitions.forEach((function(t){t.name instanceof Vt?(t.value=null,e.push(t)):t.name.walk(new An((function(n){n instanceof Vt&&e.push(ci(ft,t,{name:n,value:null}))})))})),this.definitions=e})),rt.DEFMETHOD("to_assignments",(function(e){var t=e.option("reduce_vars"),n=this.definitions.reduce((function(e,n){if(!n.value||n.name instanceof Be){if(n.value){var i=ci(ft,n,{name:n.name,value:n.value}),r=ci(ot,n,{definitions:[i]});e.push(r)}}else{var o=ci(Jt,n.name,n.name);e.push(ci(Tt,n,{operator:"=",left:o,right:n.value})),t&&(o.definition().fixed=!1)}return(n=n.name.definition()).eliminated++,n.replaced--,e}),[]);return 0==n.length?null:li(this,n)})),ti(rt,(function(e,t){return 0==e.definitions.length?ci(Se,e):e})),ti(ct,(function(e,t){return e})),ti(pt,(function(e,t){var n=e.expression,i=n;er(e,t,e.args);var r=e.args.every(e=>!(e instanceof xe));if(t.option("reduce_vars")&&i instanceof Jt&&!T(e,yn)){const e=i.fixed_value();zi(e,t)||(i=e)}var o=i instanceof ke;if(t.option("unused")&&r&&o&&!i.uses_arguments&&!i.pinned()){for(var a=0,s=0,u=0,c=e.args.length;u<c;u++){if(i.argnames[u]instanceof xe){if(Zn(i.argnames[u].expression,Hn))for(;u<c;){(D=e.args[u++].drop_side_effect_free(t))&&(e.args[a++]=D)}else for(;u<c;)e.args[a++]=e.args[u++];s=a;break}var l=u>=i.argnames.length;if(l||Zn(i.argnames[u],Hn)){if(D=e.args[u].drop_side_effect_free(t))e.args[a++]=D;else if(!l){e.args[a++]=ci(sn,e.args[u],{value:0});continue}}else e.args[a++]=e.args[u];s=a}e.args.length=s}if(t.option("unsafe"))if(gi(n))switch(n.name){case"Array":if(1!=e.args.length)return ci(yt,e,{elements:e.args}).optimize(t);if(e.args[0]instanceof sn&&e.args[0].value<=11){const t=[];for(let n=0;n<e.args[0].value;n++)t.push(new dn);return new yt({elements:t})}break;case"Object":if(0==e.args.length)return ci(Ct,e,{properties:[]});break;case"String":if(0==e.args.length)return ci(an,e,{value:""});if(e.args.length<=1)return ci(At,e,{left:e.args[0],operator:"+",right:ci(an,e,{value:""})}).optimize(t);break;case"Number":if(0==e.args.length)return ci(sn,e,{value:0});if(1==e.args.length&&t.option("unsafe_math"))return ci(gt,e,{expression:e.args[0],operator:"+"}).optimize(t);break;case"Boolean":if(0==e.args.length)return ci(hn,e);if(1==e.args.length)return ci(gt,e,{expression:ci(gt,e,{expression:e.args[0],operator:"!"}),operator:"!"}).optimize(t);break;case"RegExp":var f=[];if(e.args.length>=1&&e.args.length<=2&&e.args.every(e=>{var n=e.evaluate(t);return f.push(n),e!==n})){const[n,i]=f,r=ci(cn,e,{value:{source:n,flags:i}});if(r._eval(t)!==r)return r;t.warn("Error converting {expr} [{file}:{line},{col}]",{expr:e.print_to_string(),file:e.start.file,line:e.start.line,col:e.start.col})}}else if(n instanceof Et)switch(n.property){case"toString":if(0==e.args.length&&!n.expression.may_throw_on_access(t))return ci(At,e,{left:ci(an,e,{value:""}),operator:"+",right:n.expression}).optimize(t);break;case"join":if(n.expression instanceof yt)e:{var p;if(!(e.args.length>0&&(p=e.args[0].evaluate(t))===e.args[0])){var _,d=[],m=[];for(u=0,c=n.expression.elements.length;u<c;u++){var E=n.expression.elements[u];if(E instanceof xe)break e;var h=E.evaluate(t);h!==E?m.push(h):(m.length>0&&(d.push(ci(an,e,{value:m.join(p)})),m.length=0),d.push(E))}return m.length>0&&d.push(ci(an,e,{value:m.join(p)})),0==d.length?ci(an,e,{value:""}):1==d.length?d[0].is_string(t)?d[0]:ci(At,d[0],{operator:"+",left:ci(an,e,{value:""}),right:d[0]}):""==p?(_=d[0].is_string(t)||d[1].is_string(t)?d.shift():ci(an,e,{value:""}),d.reduce((function(e,t){return ci(At,t,{operator:"+",left:e,right:t})}),_).optimize(t)):((D=e.clone()).expression=D.expression.clone(),D.expression.expression=D.expression.expression.clone(),D.expression.expression.elements=d,xi(t,e,D));var D}}break;case"charAt":if(n.expression.is_string(t)){var g=e.args[0],S=g?g.evaluate(t):0;if(S!==g)return ci(ht,n,{expression:n.expression,property:fi(0|S,g||n)}).optimize(t)}break;case"apply":if(2==e.args.length&&e.args[1]instanceof yt)return(N=e.args[1].elements.slice()).unshift(e.args[0]),ci(pt,e,{expression:ci(Et,n,{expression:n.expression,property:"call"}),args:N}).optimize(t);break;case"call":var A=n.expression;if(A instanceof Jt&&(A=A.fixed_value()),A instanceof ke&&!A.contains_this())return(e.args.length?li(this,[e.args[0],ci(pt,e,{expression:n.expression,args:e.args.slice(1)})]):ci(pt,e,{expression:n.expression,args:[]})).optimize(t)}if(t.option("unsafe_Function")&&gi(n)&&"Function"==n.name){if(0==e.args.length)return ci(Le,e,{argnames:[],body:[]}).optimize(t);if(e.args.every(e=>e instanceof an))try{var v=ue(O="n(function("+e.args.slice(0,-1).map((function(e){return e.value})).join(",")+"){"+e.args[e.args.length-1].value+"})"),b={ie8:t.option("ie8")};v.figure_out_scope(b);var y,C=new ei(t.options);(v=v.transform(C)).figure_out_scope(b),Gn.reset(),v.compute_char_frequency(b),v.mangle_names(b),v.walk(new An((function(e){return!!y||(ri(e)?(y=e,!0):void 0)}))),y.body instanceof fe&&(y.body=[ci(ze,y.body,{value:y.body})]);var O=In();return ge.prototype._codegen.call(y,y,O),e.args=[ci(an,e,{value:y.argnames.map((function(e){return e.print_to_string()})).join(",")}),ci(an,e.args[e.args.length-1],{value:O.get().replace(/^{|}$/g,"")})],e}catch(n){if(!(n instanceof J))throw n;t.warn("Error parsing code passed to new Function [{file}:{line},{col}]",e.args[e.args.length-1].start),t.warn(n.toString())}}var F=o&&i.body;F instanceof fe?F=ci(ze,F,{value:F}):F&&(F=F[0]);var M=o&&!i.is_generator&&!i.async,R=M&&t.option("inline")&&!e.is_expr_pure(t);if(R&&F instanceof ze){let n=F.value;if(!n||n.is_constant_expression()){n=n?n.clone(!0):ci(_n,e);var N=e.args.concat(n);return li(e,N).optimize(t)}if(1===i.argnames.length&&i.argnames[0]instanceof Gt&&e.args.length<2&&"name"===n.start.type&&n.name===i.argnames[0].name)return(e.args[0]||ci(_n)).optimize(t)}if(R){var w,x,k=-1;let o,a;if(r&&!i.uses_arguments&&!i.pinned()&&!(t.parent()instanceof wt)&&!(i.name&&i instanceof Le)&&(!(t.find_parent(ke)instanceof Ve)||0==i.argnames.length&&(i.body instanceof fe||1==i.body.length))&&(a=function(e){var n=i.body instanceof fe?[i.body]:i.body,r=n.length;if(t.option("inline")<3)return 1==r&&L(e);e=null;for(var o=0;o<r;o++){var a=n[o];if(a instanceof ot){if(e&&!a.definitions.every(e=>!e.value))return!1}else{if(e)return!1;a instanceof Se||(e=a)}}return L(e)}(F))&&(n===i||T(e,bn)||t.option("unused")&&1==(o=n.definition()).references.length&&!Yi(t,o)&&i.is_constant_expression(n.scope))&&!T(e,Tn|yn)&&!i.contains_this()&&function(){var n=new Set;do{if(!(w=t.parent(++k)).is_block_scope()||t.parent(k-1)instanceof Ne||w.block_scope&&w.block_scope.variables.forEach((function(e){n.add(e.name)})),w instanceof nt)w.argname&&n.add(w.argname.name);else if(w instanceof Te)x=[];else if(w instanceof Jt&&w.fixed_value()instanceof Ne)return!1}while(!(w instanceof Ne)||w instanceof Ve);var r=!(w instanceof we)||t.toplevel.vars,o=t.option("inline");return!!function(e,t){for(var n=i.body.length,r=0;r<n;r++){var o=i.body[r];if(o instanceof ot){if(!t)return!1;for(var a=o.definitions.length;--a>=0;){var s=o.definitions[a].name;if(s instanceof Be||e.has(s.name)||vi.has(s.name)||w.var_names().has(s.name))return!1;x&&x.push(s.definition())}}}return!0}(n,o>=3&&r)&&(!!function(e,t){for(var n=0,r=i.argnames.length;n<r;n++){var o=i.argnames[n];if(o instanceof bt){if(Zn(o.left,Hn))continue;return!1}if(o instanceof Be)return!1;if(o instanceof xe){if(Zn(o.expression,Hn))continue;return!1}if(!Zn(o,Hn)){if(!t||e.has(o.name)||vi.has(o.name)||w.var_names().has(o.name))return!1;x&&x.push(o.definition())}}return!0}(n,o>=2&&r)&&(!!function(){var t=new Set,n=new An((function(e){if(e instanceof Ne){var n=new Set;return e.enclosed.forEach((function(e){n.add(e.name)})),e.variables.forEach((function(e){n.delete(e)})),n.forEach((function(e){t.add(e)})),!0}return!1}));if(e.args.forEach((function(e){e.walk(n)})),0==t.size)return!0;for(var r=0,o=i.argnames.length;r<o;r++){var a=i.argnames[r];if(!(a instanceof bt&&Zn(a.left,Hn))&&(!(a instanceof xe&&Zn(a.expression,Hn))&&!Zn(a,Hn)&&t.has(a.name)))return!1}for(r=0,o=i.body.length;r<o;r++){var s=i.body[r];if(s instanceof ot)for(var u=s.definitions.length;--u>=0;){var c=s.definitions[u].name;if(c instanceof Be||t.has(c.name))return!1}}return!0}()&&(!x||0==x.length||!$i(i,x))))}()&&!(w instanceof wt))return Jn(i,Yn),si(t,!0).add_child_scope(i),li(e,function(n){var r=[],o=[];(function(t,n){for(var r=i.argnames.length,o=e.args.length;--o>=r;)n.push(e.args[o]);for(o=r;--o>=0;){var a=i.argnames[o],s=e.args[o];if(Zn(a,Hn)||!a.name||w.var_names().has(a.name))s&&n.push(s);else{var u=ci(Pt,a,a);a.definition().orig.push(u),!s&&x&&(s=ci(_n,e)),V(t,n,u,s)}}t.reverse(),n.reverse()})(r,o),function(e,t){for(var n=t.length,r=0,o=i.body.length;r<o;r++){var a=i.body[r];if(a instanceof ot)for(var s=0,u=a.definitions.length;s<u;s++){var c=a.definitions[s],l=c.name;if(V(e,t,l,c.value),x&&i.argnames.every(e=>e.name!=l.name)){var f=i.variables.get(l.name),p=ci(Jt,l,l);f.references.push(p),t.splice(n++,0,ci(Tt,c,{operator:"=",left:p,right:ci(_n,l)}))}}}}(r,o),o.push(n),r.length&&(u=w.body.indexOf(t.parent(k-1))+1,w.body.splice(u,0,ci(ot,i,{definitions:r})));return o.map(e=>e.clone(!0))}(a)).optimize(t)}if(M&&t.option("side_effects")&&!(i.body instanceof fe)&&i.body.every(mi)){N=e.args.concat(ci(_n,e));return li(e,N).optimize(t)}if(t.option("negate_iife")&&t.parent()instanceof me&&Di(e))return e.negate(t,!0);var I=e.evaluate(t);return I!==e?(I=fi(I,e).optimize(t),xi(t,I,e)):e;function L(t){return t?t instanceof ze?t.value?t.value.clone(!0):ci(_n,e):t instanceof me?ci(gt,t,{operator:"void",expression:t.body.clone(!0)}):void 0:ci(_n,e)}function V(t,n,i,r){var o=i.definition();w.variables.set(i.name,o),w.enclosed.push(o),w.var_names().has(i.name)||(w.add_var_name(i.name),t.push(ci(ft,i,{name:i,value:null})));var a=ci(Jt,i,i);o.references.push(a),r&&n.push(ci(Tt,e,{operator:"=",left:a,right:r.clone()}))}})),ti(_t,(function(e,t){return t.option("unsafe")&&gi(e.expression)&&["Object","RegExp","Function","Error","Array"].includes(e.expression.name)?ci(pt,e,e).transform(t):e})),ti(dt,(function(e,t){if(!t.option("side_effects"))return e;var n,i,r=[];n=Mn(t),i=e.expressions.length-1,e.expressions.forEach((function(e,o){o<i&&(e=e.drop_side_effect_free(t,n)),e&&(_i(r,e),n=!1)}));var o=r.length-1;return function(){for(;o>0&&Oi(r[o],t);)o--;o<r.length-1&&(r[o]=ci(gt,e,{operator:"void",expression:r[o]}),r.length=o+1)}(),0==o?((e=pi(t.parent(),t.self(),r[0]))instanceof dt||(e=e.optimize(t)),e):(e.expressions=r,e)})),Dt.DEFMETHOD("lift_sequences",(function(e){if(e.option("sequences")&&this.expression instanceof dt){var t=this.expression.expressions.slice(),n=this.clone();return n.expression=t.pop(),t.push(n),li(this,t).optimize(e)}return this})),ti(St,(function(e,t){return e.lift_sequences(t)})),ti(gt,(function(e,t){var n=e.expression;if("delete"==e.operator&&!(n instanceof Jt||n instanceof mt||Ti(n))){if(n instanceof dt){const i=n.expressions.slice();return i.push(ci(Dn,e)),li(e,i).optimize(t)}return li(e,[n,ci(Dn,e)]).optimize(t)}var i=e.lift_sequences(t);if(i!==e)return i;if(t.option("side_effects")&&"void"==e.operator)return(n=n.drop_side_effect_free(t))?(e.expression=n,e):ci(_n,e).optimize(t);if(t.in_boolean_context())switch(e.operator){case"!":if(n instanceof gt&&"!"==n.operator)return n.expression;n instanceof At&&(e=xi(t,e,n.negate(t,Mn(t))));break;case"typeof":return t.warn("Boolean expression always true [{file}:{line},{col}]",e.start),(n instanceof Jt?ci(Dn,e):li(e,[n,ci(Dn,e)])).optimize(t)}if("-"==e.operator&&n instanceof mn&&(n=n.transform(t)),n instanceof At&&("+"==e.operator||"-"==e.operator)&&("*"==n.operator||"/"==n.operator||"%"==n.operator))return ci(At,e,{operator:n.operator,left:ci(gt,n.left,{operator:e.operator,expression:n.left}),right:n.right});if("-"!=e.operator||!(n instanceof sn||n instanceof mn||n instanceof un)){var r=e.evaluate(t);if(r!==e)return xi(t,r=fi(r,e).optimize(t),e)}return e})),At.DEFMETHOD("lift_sequences",(function(e){if(e.option("sequences")){if(this.left instanceof dt){var t=this.left.expressions.slice();return(n=this.clone()).left=t.pop(),t.push(n),li(this,t).optimize(e)}if(this.right instanceof dt&&!this.left.has_side_effects(e)){for(var n,i="="==this.operator&&this.left instanceof Jt,r=(t=this.right.expressions).length-1,o=0;o<r&&(i||!t[o].has_side_effects(e));o++);if(o==r)return t=t.slice(),(n=this.clone()).right=t.pop(),t.push(n),li(this,t).optimize(e);if(o>0)return(n=this.clone()).right=li(this.right,t.slice(o)),(t=t.slice(0,o)).push(n),li(this,t).optimize(e)}}return this}));var Wi=E("== === != !== * & | ^");function Yi(e,t){for(var n,i=0;n=e.parent(i);i++)if(n instanceof ke){var r=n.name;if(r&&r.definition()===t)break}return n}function qi(e,t){return e instanceof Jt||e.TYPE===t.TYPE}function $i(e,t){var n=!1,r=new An((function(e){return!!n||(e instanceof Jt&&i(e.definition(),t)?n=!0:void 0)})),o=new An((function(t){if(n)return!0;if(t instanceof Ne&&t!==e){var i=o.parent();if(i instanceof pt&&i.expression===t)return;return t.walk(r),!0}}));return e.walk(o),n}ti(At,(function(e,t){function n(){return e.left.is_constant()||e.right.is_constant()||!e.left.has_side_effects(t)&&!e.right.has_side_effects(t)}function i(t){if(n()){t&&(e.operator=t);var i=e.left;e.left=e.right,e.right=i}}if(Wi.has(e.operator)&&e.right.is_constant()&&!e.left.is_constant()&&(e.left instanceof At&&ae[e.left.operator]>=ae[e.operator]||i()),e=e.lift_sequences(t),t.option("comparisons"))switch(e.operator){case"===":case"!==":var r=!0;(e.left.is_string(t)&&e.right.is_string(t)||e.left.is_number(t)&&e.right.is_number(t)||e.left.is_boolean()&&e.right.is_boolean()||e.left.equivalent_to(e.right))&&(e.operator=e.operator.substr(0,2));case"==":case"!=":if(!r&&Oi(e.left,t))e.left=ci(fn,e.left);else if(t.option("typeofs")&&e.left instanceof an&&"undefined"==e.left.value&&e.right instanceof gt&&"typeof"==e.right.operator){var o=e.right.expression;(o instanceof Jt?!o.is_declared(t):o instanceof mt&&t.option("ie8"))||(e.right=o,e.left=ci(_n,e.left).optimize(t),2==e.operator.length&&(e.operator+="="))}else if(e.left instanceof Jt&&e.right instanceof Jt&&e.left.definition()===e.right.definition()&&((u=e.left.fixed_value())instanceof yt||u instanceof ke||u instanceof Ct||u instanceof wt))return ci("="==e.operator[0]?Dn:hn,e);break;case"&&":case"||":var a=e.left;if(a.operator==e.operator&&(a=a.right),a instanceof At&&a.operator==("&&"==e.operator?"!==":"===")&&e.right instanceof At&&a.operator==e.right.operator&&(Oi(a.left,t)&&e.right.left instanceof fn||a.left instanceof fn&&Oi(e.right.left,t))&&!a.right.has_side_effects(t)&&a.right.equivalent_to(e.right.right)){var s=ci(At,e,{operator:a.operator.slice(0,-1),left:ci(fn,e),right:a.right});return a!==e.left&&(s=ci(At,e,{operator:e.operator,left:e.left.left,right:s})),s}}var u;if("+"==e.operator&&t.in_boolean_context()){var c=e.left.evaluate(t),l=e.right.evaluate(t);if(c&&"string"==typeof c)return t.warn("+ in boolean context always true [{file}:{line},{col}]",e.start),li(e,[e.right,ci(Dn,e)]).optimize(t);if(l&&"string"==typeof l)return t.warn("+ in boolean context always true [{file}:{line},{col}]",e.start),li(e,[e.left,ci(Dn,e)]).optimize(t)}if(t.option("comparisons")&&e.is_boolean()){if(!(t.parent()instanceof At)||t.parent()instanceof Tt){var f=ci(gt,e,{operator:"!",expression:e.negate(t,Mn(t))});e=xi(t,e,f)}if(t.option("unsafe_comps"))switch(e.operator){case"<":i(">");break;case"<=":i(">=")}}if("+"==e.operator){if(e.right instanceof an&&""==e.right.getValue()&&e.left.is_string(t))return e.left;if(e.left instanceof an&&""==e.left.getValue()&&e.right.is_string(t))return e.right;if(e.left instanceof At&&"+"==e.left.operator&&e.left.left instanceof an&&""==e.left.left.getValue()&&e.right.is_string(t))return e.left=e.left.right,e.transform(t)}if(t.option("evaluate")){switch(e.operator){case"&&":if(!(c=!!Zn(e.left,2)||!Zn(e.left,4)&&e.left.evaluate(t)))return t.warn("Condition left of && always false [{file}:{line},{col}]",e.start),pi(t.parent(),t.self(),e.left).optimize(t);if(!(c instanceof fe))return t.warn("Condition left of && always true [{file}:{line},{col}]",e.start),li(e,[e.left,e.right]).optimize(t);if(l=e.right.evaluate(t)){if(!(l instanceof fe)){if("&&"==(p=t.parent()).operator&&p.left===t.self()||t.in_boolean_context())return t.warn("Dropping side-effect-free && [{file}:{line},{col}]",e.start),e.left.optimize(t)}}else{if(t.in_boolean_context())return t.warn("Boolean && always false [{file}:{line},{col}]",e.start),li(e,[e.left,ci(hn,e)]).optimize(t);Jn(e,4)}if("||"==e.left.operator)if(!(_=e.left.right.evaluate(t)))return ci(vt,e,{condition:e.left.left,consequent:e.right,alternative:e.left.right}).optimize(t);break;case"||":var p,_;if(!(c=!!Zn(e.left,2)||!Zn(e.left,4)&&e.left.evaluate(t)))return t.warn("Condition left of || always false [{file}:{line},{col}]",e.start),li(e,[e.left,e.right]).optimize(t);if(!(c instanceof fe))return t.warn("Condition left of || always true [{file}:{line},{col}]",e.start),pi(t.parent(),t.self(),e.left).optimize(t);if(l=e.right.evaluate(t)){if(!(l instanceof fe)){if(t.in_boolean_context())return t.warn("Boolean || always true [{file}:{line},{col}]",e.start),li(e,[e.left,ci(Dn,e)]).optimize(t);Jn(e,2)}}else if("||"==(p=t.parent()).operator&&p.left===t.self()||t.in_boolean_context())return t.warn("Dropping side-effect-free || [{file}:{line},{col}]",e.start),e.left.optimize(t);if("&&"==e.left.operator)if((_=e.left.right.evaluate(t))&&!(_ instanceof fe))return ci(vt,e,{condition:e.left.left,consequent:e.left.right,alternative:e.right}).optimize(t)}var d=!0;switch(e.operator){case"+":if(e.left instanceof on&&e.right instanceof At&&"+"==e.right.operator&&e.right.left instanceof on&&e.right.is_string(t)&&(e=ci(At,e,{operator:"+",left:ci(an,e.left,{value:""+e.left.getValue()+e.right.left.getValue(),start:e.left.start,end:e.right.left.end}),right:e.right.right})),e.right instanceof on&&e.left instanceof At&&"+"==e.left.operator&&e.left.right instanceof on&&e.left.is_string(t)&&(e=ci(At,e,{operator:"+",left:e.left.left,right:ci(an,e.right,{value:""+e.left.right.getValue()+e.right.getValue(),start:e.left.right.start,end:e.right.end})})),e.left instanceof At&&"+"==e.left.operator&&e.left.is_string(t)&&e.left.right instanceof on&&e.right instanceof At&&"+"==e.right.operator&&e.right.left instanceof on&&e.right.is_string(t)&&(e=ci(At,e,{operator:"+",left:ci(At,e.left,{operator:"+",left:e.left.left,right:ci(an,e.left.right,{value:""+e.left.right.getValue()+e.right.left.getValue(),start:e.left.right.start,end:e.right.left.end})}),right:e.right.right})),e.right instanceof gt&&"-"==e.right.operator&&e.left.is_number(t)){e=ci(At,e,{operator:"-",left:e.left,right:e.right.expression});break}if(e.left instanceof gt&&"-"==e.left.operator&&n()&&e.right.is_number(t)){e=ci(At,e,{operator:"-",left:e.right,right:e.left.expression});break}case"*":d=t.option("unsafe_math");case"&":case"|":case"^":if(e.left.is_number(t)&&e.right.is_number(t)&&n()&&!(e.left instanceof At&&e.left.operator!=e.operator&&ae[e.left.operator]>=ae[e.operator])){var m=ci(At,e,{operator:e.operator,left:e.right,right:e.left});e=e.right instanceof on&&!(e.left instanceof on)?xi(t,m,e):xi(t,e,m)}d&&e.is_number(t)&&(e.right instanceof At&&e.right.operator==e.operator&&(e=ci(At,e,{operator:e.operator,left:ci(At,e.left,{operator:e.operator,left:e.left,right:e.right.left,start:e.left.start,end:e.right.left.end}),right:e.right.right})),e.right instanceof on&&e.left instanceof At&&e.left.operator==e.operator&&(e.left.left instanceof on?e=ci(At,e,{operator:e.operator,left:ci(At,e.left,{operator:e.operator,left:e.left.left,right:e.right,start:e.left.left.start,end:e.right.end}),right:e.left.right}):e.left.right instanceof on&&(e=ci(At,e,{operator:e.operator,left:ci(At,e.left,{operator:e.operator,left:e.left.right,right:e.right,start:e.left.right.start,end:e.right.end}),right:e.left.left}))),e.left instanceof At&&e.left.operator==e.operator&&e.left.right instanceof on&&e.right instanceof At&&e.right.operator==e.operator&&e.right.left instanceof on&&(e=ci(At,e,{operator:e.operator,left:ci(At,e.left,{operator:e.operator,left:ci(At,e.left.left,{operator:e.operator,left:e.left.right,right:e.right.left,start:e.left.right.start,end:e.right.left.end}),right:e.left.left}),right:e.right.right})))}}if(e.right instanceof At&&e.right.operator==e.operator&&(Fi.has(e.operator)||"+"==e.operator&&(e.right.left.is_string(t)||e.left.is_string(t)&&e.right.right.is_string(t))))return e.left=ci(At,e.left,{operator:e.operator,left:e.left,right:e.right.left}),e.right=e.right.right,e.transform(t);var E=e.evaluate(t);return E!==e?(E=fi(E,e).optimize(t),xi(t,E,e)):e})),ti(Qt,(function(e,t){return e})),ti(Jt,(function(e,t){if(!t.option("ie8")&&gi(e)&&(!e.scope.uses_with||!t.find_parent(Re)))switch(e.name){case"undefined":return ci(_n,e).optimize(t);case"NaN":return ci(pn,e).optimize(t);case"Infinity":return ci(mn,e).optimize(t)}var n,i=t.parent();if(t.option("reduce_vars")&&Ri(e,i)!==e){const p=e.definition();if(t.top_retain&&p.global&&t.top_retain(p))return p.fixed=!1,p.should_replace=!1,p.single_use=!1,e;var r=e.fixed_value(),o=p.single_use&&!(i instanceof pt&&i.is_expr_pure(t));if(o&&(r instanceof ke||r instanceof wt))if(zi(r,t))o=!1;else if(p.scope!==e.scope&&(1==p.escaped||Zn(r,zn)||function(e){for(var t,n=0;t=e.parent(n++);){if(t instanceof pe)return!1;if(t instanceof yt||t instanceof Ft||t instanceof Ct)return!0}return!1}(t)))o=!1;else if(Yi(t,p))o=!1;else if((p.scope!==e.scope||p.orig[0]instanceof Gt)&&"f"==(o=r.is_constant_expression(e.scope))){var a=e.scope;do{(a instanceof Pe||ri(a))&&Jn(a,zn)}while(a=a.parent_scope)}if(o&&r instanceof ke&&(o=p.scope===e.scope||i instanceof pt&&i.expression===e),o&&r instanceof wt&&r.extends&&(o=!r.extends.may_throw(t)&&(!(r.extends instanceof pt)||r.extends.is_expr_pure(t))),o&&r){if(r instanceof xt&&(r=ci(kt,r,r)),r instanceof Pe&&(Jn(r,Yn),r=ci(Le,r,r)),p.recursive_refs>0&&r.name instanceof Ht){const e=r.name.definition();let t=r.variables.get(r.name.name),n=t&&t.orig[0];n instanceof zt||((n=ci(zt,r.name,r.name)).scope=r,r.name=n,t=r.def_function(n)),r.walk(new An((function(n){n instanceof Jt&&n.definition()===e&&(n.thedef=t,t.references.push(n))})))}return(r instanceof ke||r instanceof wt)&&si(t,!0).add_child_scope(r),r.optimize(t)}if(r&&void 0===p.should_replace){let e;if(r instanceof nn)p.orig[0]instanceof Gt||!p.references.every(e=>p.scope===e.scope)||(e=r);else{var s=r.evaluate(t);s===r||!t.option("unsafe_regexp")&&s instanceof RegExp||(e=fi(s,r))}if(e){var u,c=e.optimize(t).print_to_string().length;r.walk(new An((function(e){if(e instanceof Jt&&(n=!0),n)return!0}))),n?u=function(){var n=e.optimize(t);return n===e?n.clone(!0):n}:(c=Math.min(c,r.print_to_string().length),u=function(){var n=Ni(e.optimize(t),r);return n===e||n===r?n.clone(!0):n});var l=p.name.length,f=0;t.option("unused")&&!t.exposed(p)&&(f=(l+2+c)/(p.references.length-p.assignments)),p.should_replace=c<=l+f&&u}else p.should_replace=!1}if(p.should_replace)return p.should_replace()}return e})),ti(_n,(function(e,t){if(t.option("unsafe_undefined")){var n=ui(t,"undefined");if(n){var i=ci(Jt,e,{name:"undefined",scope:n.scope,thedef:n});return Jn(i,Xn),i}}var r=Ri(t.self(),t.parent());return r&&qi(r,e)?e:ci(gt,e,{operator:"void",expression:ci(sn,e,{value:0})})})),ti(mn,(function(e,t){var n=Ri(t.self(),t.parent());return n&&qi(n,e)?e:!t.option("keep_infinity")||n&&!qi(n,e)||ui(t,"Infinity")?ci(At,e,{operator:"/",left:ci(sn,e,{value:1}),right:ci(sn,e,{value:0})}):e})),ti(pn,(function(e,t){var n=Ri(t.self(),t.parent());return n&&!qi(n,e)||ui(t,"NaN")?ci(At,e,{operator:"/",left:ci(sn,e,{value:0}),right:ci(sn,e,{value:0})}):e}));const ji=E("+ - / * % >> << >>> | ^ &"),Zi=E("* | ^ &");function Ji(e,t){return e instanceof Jt&&(e=e.fixed_value()),!!e&&(!(e instanceof ke||e instanceof wt)||t.parent()instanceof _t||!e.contains_this())}function Qi(e,t){return t.in_boolean_context()?xi(t,e,li(e,[e,ci(Dn,e)]).optimize(t)):e}function er(e,t,n){for(var i=0;i<n.length;i++){var r=n[i];if(r instanceof xe){var o=r.expression;o instanceof yt&&(n.splice(i,1,...o.elements),i--)}}return e}function tr(e,t){if(!t.option("computed_props"))return e;if(!(e.key instanceof on))return e;if(e.key instanceof an||e.key instanceof sn){if("__proto__"===e.key.value)return e;if("constructor"==e.key.value&&t.parent()instanceof wt)return e;e.key=e instanceof Ft?e.key.value:ci(Xt,e.key,{name:e.key.value})}return e}ti(Tt,(function(e,t){var n;if(t.option("dead_code")&&e.left instanceof Jt&&(n=e.left.definition()).scope===t.find_parent(ke)){var i,r=0,o=e;do{if(i=o,(o=t.parent(r++))instanceof Xe){if(a(r,o))break;if($i(n.scope,[n]))break;return"="==e.operator?e.right:(n.fixed=!1,ci(At,e,{operator:e.operator.slice(0,-1),left:e.left,right:e.right}).optimize(t))}}while(o instanceof At&&o.right===i||o instanceof dt&&o.tail_node()===i)}return"="==(e=e.lift_sequences(t)).operator&&e.left instanceof Jt&&e.right instanceof At&&(e.right.left instanceof Jt&&e.right.left.name==e.left.name&&ji.has(e.right.operator)?(e.operator=e.right.operator+"=",e.right=e.right.right):e.right.right instanceof Jt&&e.right.right.name==e.left.name&&Zi.has(e.right.operator)&&!e.right.left.has_side_effects(t)&&(e.operator=e.right.operator+"=",e.right=e.right.left)),e;function a(n,i){var r=e.right;e.right=ci(fn,r);var o=i.may_throw(t);e.right=r;for(var a,s=e.left.definition().scope;(a=t.parent(n++))!==s;)if(a instanceof tt){if(a.bfinally)return!0;if(o&&a.bcatch)return!0}}})),ti(bt,(function(e,t){if(!t.option("evaluate"))return e;var n=e.right.evaluate(t);return void 0===n?e=e.left:n!==e.right&&(n=fi(n,e.right),e.right=Ni(n,e.right)),e})),ti(vt,(function(e,t){if(!t.option("conditionals"))return e;if(e.condition instanceof dt){var n=e.condition.expressions.slice();return e.condition=n.pop(),n.push(e),li(e,n)}var i=e.condition.evaluate(t);if(i!==e.condition)return i?(t.warn("Condition always true [{file}:{line},{col}]",e.start),pi(t.parent(),t.self(),e.consequent)):(t.warn("Condition always false [{file}:{line},{col}]",e.start),pi(t.parent(),t.self(),e.alternative));var r=i.negate(t,Mn(t));xi(t,i,r)===r&&(e=ci(vt,e,{condition:r,consequent:e.alternative,alternative:e.consequent}));var o,a=e.condition,s=e.consequent,u=e.alternative;if(a instanceof Jt&&s instanceof Jt&&a.definition()===s.definition())return ci(At,e,{operator:"||",left:a,right:u});if(s instanceof Tt&&u instanceof Tt&&s.operator==u.operator&&s.left.equivalent_to(u.left)&&(!e.condition.has_side_effects(t)||"="==s.operator&&!s.left.has_side_effects(t)))return ci(Tt,e,{operator:s.operator,left:s.left,right:ci(vt,e,{condition:e.condition,consequent:s.right,alternative:u.right})});if(s instanceof pt&&u.TYPE===s.TYPE&&s.args.length>0&&s.args.length==u.args.length&&s.expression.equivalent_to(u.expression)&&!e.condition.has_side_effects(t)&&!s.expression.has_side_effects(t)&&"number"==typeof(o=function(){for(var e=s.args,t=u.args,n=0,i=e.length;n<i;n++){if(e[n]instanceof xe)return;if(!e[n].equivalent_to(t[n])){if(t[n]instanceof xe)return;for(var r=n+1;r<i;r++){if(e[r]instanceof xe)return;if(!e[r].equivalent_to(t[r]))return}return n}}}())){var c=s.clone();return c.args[o]=ci(vt,e,{condition:e.condition,consequent:s.args[o],alternative:u.args[o]}),c}if(s instanceof vt&&s.alternative.equivalent_to(u))return ci(vt,e,{condition:ci(At,e,{left:e.condition,operator:"&&",right:s.condition}),consequent:s.consequent,alternative:u});if(s.equivalent_to(u))return li(e,[e.condition,s]).optimize(t);if(s instanceof At&&"||"==s.operator&&s.right.equivalent_to(u))return ci(At,e,{operator:"||",left:ci(At,e,{operator:"&&",left:e.condition,right:s.left}),right:u}).optimize(t);var l=t.in_boolean_context();return p(e.consequent)?_(e.alternative)?f(e.condition):ci(At,e,{operator:"||",left:f(e.condition),right:e.alternative}):_(e.consequent)?p(e.alternative)?f(e.condition.negate(t)):ci(At,e,{operator:"&&",left:f(e.condition.negate(t)),right:e.alternative}):p(e.alternative)?ci(At,e,{operator:"||",left:f(e.condition.negate(t)),right:e.consequent}):_(e.alternative)?ci(At,e,{operator:"&&",left:f(e.condition),right:e.consequent}):e;function f(e){return e.is_boolean()?e:ci(gt,e,{operator:"!",expression:e.negate(t)})}function p(e){return e instanceof Dn||l&&e instanceof on&&e.getValue()||e instanceof gt&&"!"==e.operator&&e.expression instanceof on&&!e.expression.getValue()}function _(e){return e instanceof hn||l&&e instanceof on&&!e.getValue()||e instanceof gt&&"!"==e.operator&&e.expression instanceof on&&e.expression.getValue()}})),ti(En,(function(e,t){if(t.in_boolean_context())return ci(sn,e,{value:+e.value});var n=t.parent();return t.option("booleans_as_integers")?(n instanceof At&&("==="==n.operator||"!=="==n.operator)&&(n.operator=n.operator.replace(/=$/,"")),ci(sn,e,{value:+e.value})):t.option("booleans")?n instanceof At&&("=="==n.operator||"!="==n.operator)?(t.warn("Non-strict equality against boolean: {operator} {value} [{file}:{line},{col}]",{operator:n.operator,value:e.value,file:n.start.file,line:n.start.line,col:n.start.col}),ci(sn,e,{value:+e.value})):ci(gt,e,{operator:"!",expression:ci(sn,e,{value:1-e.value})}):e})),ti(ht,(function(e,t){var n,i=e.expression,r=e.property;if(t.option("properties")){var o=r.evaluate(t);if(o!==r){if("string"==typeof o)if("undefined"==o)o=void 0;else(A=parseFloat(o)).toString()==o&&(o=A);r=e.property=Ni(r,fi(o,r).transform(t));var a=""+o;if(j(a)&&a.length<=r.print_to_string().length+1)return ci(Et,e,{expression:i,property:a,quote:r.quote}).optimize(t)}}e:if(t.option("arguments")&&i instanceof Jt&&"arguments"==i.name&&1==i.definition().orig.length&&(n=i.scope)instanceof ke&&n.uses_arguments&&!(n instanceof Ve)&&r instanceof sn){for(var s=r.getValue(),u=new Set,c=n.argnames,l=0;l<c.length;l++){if(!(c[l]instanceof Gt))break e;var f=c[l].name;if(u.has(f))break e;u.add(f)}var p=n.argnames[s];if(p&&t.has_directive("use strict")){var _=p.definition();(!t.option("reduce_vars")||_.assignments||_.orig.length>1)&&(p=null)}else if(!p&&!t.option("keep_fargs")&&s<n.argnames.length+5)for(;s>=n.argnames.length;)p=ci(Gt,n,{name:n.make_var_name("argument_"+n.argnames.length),scope:n}),n.argnames.push(p),n.enclosed.push(n.def_variable(p));if(p){var d=ci(Jt,e,p);return d.reference({}),Qn(p,Hn),d}}if(Ri(e,t.parent()))return e;if(o!==r){var m=e.flatten_object(a,t);m&&(i=e.expression=m.expression,r=e.property=m.property)}if(t.option("properties")&&t.option("side_effects")&&r instanceof sn&&i instanceof yt){s=r.getValue();var E=i.elements,h=E[s];e:if(Ji(h,t)){for(var D=!0,g=[],S=E.length;--S>s;){(A=E[S].drop_side_effect_free(t))&&(g.unshift(A),D&&A.has_side_effects(t)&&(D=!1))}if(h instanceof xe)break e;for(h=h instanceof dn?ci(_n,h):h,D||g.unshift(h);--S>=0;){var A;if((A=E[S])instanceof xe)break e;(A=A.drop_side_effect_free(t))?g.unshift(A):s--}return D?(g.push(h),li(e,g).optimize(t)):ci(ht,e,{expression:ci(yt,i,{elements:g}),property:ci(sn,r,{value:s})})}}var v=e.evaluate(t);return v!==e?xi(t,v=fi(v,e).optimize(t),e):e})),ke.DEFMETHOD("contains_this",(function(){var e,t=this;return t.walk(new An((function(n){return!!e||(n instanceof nn?e=!0:n!==t&&n instanceof Ne&&!(n instanceof Ve)||void 0)}))),e})),mt.DEFMETHOD("flatten_object",(function(e,t){if(t.option("properties")){var n=t.option("unsafe_arrows")&&t.option("ecma")>=6,i=this.expression;if(i instanceof Ct)for(var r=i.properties,o=r.length;--o>=0;){var a=r[o];if(""+(a instanceof Nt?a.key.name:a.key)==e){if(!r.every(e=>e instanceof Ft||n&&e instanceof Nt&&!e.is_generator))break;if(!Ji(a.value,t))break;return ci(ht,this,{expression:ci(yt,i,{elements:r.map((function(e){var t=e.value;t instanceof Ie&&(t=ci(Le,t,t));var n=e.key;return n instanceof fe&&!(n instanceof Xt)?li(e,[n,t]):t}))}),property:ci(sn,this,{value:o})})}}}})),ti(Et,(function(e,t){if("arguments"!=e.property&&"caller"!=e.property||t.warn("Function.prototype.{prop} not supported [{file}:{line},{col}]",{prop:e.property,file:e.start.file,line:e.start.line,col:e.start.col}),Ri(e,t.parent()))return e;if(t.option("unsafe_proto")&&e.expression instanceof Et&&"prototype"==e.expression.property){var n=e.expression.expression;if(gi(n))switch(n.name){case"Array":e.expression=ci(yt,e.expression,{elements:[]});break;case"Function":e.expression=ci(Le,e.expression,{argnames:[],body:[]});break;case"Number":e.expression=ci(sn,e.expression,{value:0});break;case"Object":e.expression=ci(Ct,e.expression,{properties:[]});break;case"RegExp":e.expression=ci(cn,e.expression,{value:{source:"t",flags:""}});break;case"String":e.expression=ci(an,e.expression,{value:""})}}var i=e.flatten_object(e.property,t);if(i)return i.optimize(t);var r=e.evaluate(t);return r!==e?xi(t,r=fi(r,e).optimize(t),e):e})),ti(yt,(function(e,t){var n=Qi(e,t);return n!==e?n:er(e,0,e.elements)})),ti(Ct,(function(e,t){var n=Qi(e,t);if(n!==e)return n;for(var i=e.properties,r=0;r<i.length;r++){var o=i[r];if(o instanceof xe){var a=o.expression;a instanceof Ct?(i.splice.apply(i,[r,1].concat(o.expression.properties)),r--):a instanceof on&&!(a instanceof an)&&i.splice(r,1)}}return e})),ti(cn,Qi),ti(ze,(function(e,t){return e.value&&Oi(e.value,t)&&(e.value=null),e})),ti(Ve,(function(e,t){if(e.body instanceof fe||(e=Gi(e,t)),t.option("arrows")&&1==e.body.length&&e.body[0]instanceof ze){var n=e.body[0].value;e.body=n||[]}return e})),ti(Le,(function(e,t){if(e=Gi(e,t),t.option("unsafe_arrows")&&t.option("ecma")>=6&&!e.name&&!e.is_generator&&!e.uses_arguments&&!e.pinned()){var n=!1;if(e.walk(new An((function(e){return!!n||(e instanceof nn?(n=!0,!0):void 0)}))),!n)return ci(Ve,e,e).optimize(t)}return e})),ti(wt,(function(e,t){return e})),ti(Sn,(function(e,t){return e.expression&&!e.is_star&&Oi(e.expression,t)&&(e.expression=null),e})),ti(Ue,(function(e,t){if(!t.option("evaluate")||t.parent()instanceof Ke)return e;for(var n=[],i=0;i<e.segments.length;i++){var r=e.segments[i];if(r instanceof fe){var o=r.evaluate(t);if(o!==r&&(o+"").length<=r.print_to_string().length+"${}".length){n[n.length-1].value=n[n.length-1].value+o+e.segments[++i].value;continue}}n.push(r)}return e.segments=n,1==n.length?ci(an,e,n[0]):e})),ti(Ke,(function(e,t){return e})),ti(Ot,tr),ti(Nt,(function(e,t){if(tr(e,t),t.option("arrows")&&t.parent()instanceof Ct&&!e.is_generator&&!e.value.uses_arguments&&!e.value.pinned()&&1==e.value.body.length&&e.value.body[0]instanceof ze&&e.value.body[0].value&&!e.value.contains_this()){var n=ci(Ve,e.value,e.value);return n.async=e.async,n.is_generator=e.is_generator,ci(Ft,e,{key:e.key instanceof Xt?e.key.name:e.key,value:n,quote:e.quote})}return e})),ti(Ft,(function(e,t){tr(e,t);var n=t.option("unsafe_methods");if(n&&t.option("ecma")>=6&&(!(n instanceof RegExp)||n.test(e.key+""))){var i=e.key,r=e.value;if((r instanceof Ve&&Array.isArray(r.body)&&!r.contains_this()||r instanceof Le)&&!r.name)return ci(Nt,e,{async:r.async,is_generator:r.is_generator,key:i instanceof fe?i:ci(Xt,e,{name:i}),value:ci(Ie,r,r),quote:e.quote})}return e})),ti(Be,(function(e,t){if(1==t.option("pure_getters")&&t.option("unused")&&!e.is_array&&Array.isArray(e.names)&&!function(e){for(var t=[/^VarDef$/,/^(Const|Let|Var)$/,/^Export$/],n=0,i=0,r=t.length;n<r;i++){var o=e.parent(i);if(!o)return!1;if(0!==n||"Destructuring"!=o.TYPE){if(!t[n].test(o.TYPE))return!1;n++}}return!0}(t)){for(var n=[],i=0;i<e.names.length;i++){var r=e.names[i];r instanceof Ft&&"string"==typeof r.key&&r.value instanceof Vt&&!o(t,r.value.definition())||n.push(r)}n.length!=e.names.length&&(e.names=n)}return e;function o(e,t){return!!t.references.length||!!t.global&&(!e.toplevel.vars||!!e.top_retain&&e.top_retain(t))}}));var nr=["$&","$'","$*","$+","$1","$2","$3","$4","$5","$6","$7","$8","$9","$_","$`","$input","@@iterator","ABORT_ERR","ACTIVE","ACTIVE_ATTRIBUTES","ACTIVE_TEXTURE","ACTIVE_UNIFORMS","ADDITION","ALIASED_LINE_WIDTH_RANGE","ALIASED_POINT_SIZE_RANGE","ALLOW_KEYBOARD_INPUT","ALLPASS","ALPHA","ALPHA_BITS","ALT_MASK","ALWAYS","ANY_TYPE","ANY_UNORDERED_NODE_TYPE","ARRAY_BUFFER","ARRAY_BUFFER_BINDING","ATTACHED_SHADERS","ATTRIBUTE_NODE","AT_TARGET","AddSearchProvider","AnalyserNode","AnimationEvent","AnonXMLHttpRequest","ApplicationCache","ApplicationCacheErrorEvent","Array","ArrayBuffer","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioContext","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioProcessingEvent","AudioStreamTrack","AutocompleteErrorEvent","BACK","BAD_BOUNDARYPOINTS_ERR","BANDPASS","BLEND","BLEND_COLOR","BLEND_DST_ALPHA","BLEND_DST_RGB","BLEND_EQUATION","BLEND_EQUATION_ALPHA","BLEND_EQUATION_RGB","BLEND_SRC_ALPHA","BLEND_SRC_RGB","BLUE_BITS","BLUR","BOOL","BOOLEAN_TYPE","BOOL_VEC2","BOOL_VEC3","BOOL_VEC4","BOTH","BROWSER_DEFAULT_WEBGL","BUBBLING_PHASE","BUFFER_SIZE","BUFFER_USAGE","BYTE","BYTES_PER_ELEMENT","BarProp","BaseHref","BatteryManager","BeforeLoadEvent","BeforeUnloadEvent","BiquadFilterNode","Blob","BlobEvent","Boolean","CAPTURING_PHASE","CCW","CDATASection","CDATA_SECTION_NODE","CHANGE","CHARSET_RULE","CHECKING","CLAMP_TO_EDGE","CLICK","CLOSED","CLOSING","COLOR_ATTACHMENT0","COLOR_BUFFER_BIT","COLOR_CLEAR_VALUE","COLOR_WRITEMASK","COMMENT_NODE","COMPILE_STATUS","COMPRESSED_RGBA_S3TC_DXT1_EXT","COMPRESSED_RGBA_S3TC_DXT3_EXT","COMPRESSED_RGBA_S3TC_DXT5_EXT","COMPRESSED_RGB_S3TC_DXT1_EXT","COMPRESSED_TEXTURE_FORMATS","CONNECTING","CONSTANT_ALPHA","CONSTANT_COLOR","CONSTRAINT_ERR","CONTEXT_LOST_WEBGL","CONTROL_MASK","COUNTER_STYLE_RULE","CSS","CSS2Properties","CSSCharsetRule","CSSConditionRule","CSSCounterStyleRule","CSSFontFaceRule","CSSFontFeatureValuesRule","CSSGroupingRule","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSMediaRule","CSSMozDocumentRule","CSSNameSpaceRule","CSSPageRule","CSSPrimitiveValue","CSSRule","CSSRuleList","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSSupportsRule","CSSUnknownRule","CSSValue","CSSValueList","CSSVariablesDeclaration","CSSVariablesRule","CSSViewportRule","CSS_ATTR","CSS_CM","CSS_COUNTER","CSS_CUSTOM","CSS_DEG","CSS_DIMENSION","CSS_EMS","CSS_EXS","CSS_FILTER_BLUR","CSS_FILTER_BRIGHTNESS","CSS_FILTER_CONTRAST","CSS_FILTER_CUSTOM","CSS_FILTER_DROP_SHADOW","CSS_FILTER_GRAYSCALE","CSS_FILTER_HUE_ROTATE","CSS_FILTER_INVERT","CSS_FILTER_OPACITY","CSS_FILTER_REFERENCE","CSS_FILTER_SATURATE","CSS_FILTER_SEPIA","CSS_GRAD","CSS_HZ","CSS_IDENT","CSS_IN","CSS_INHERIT","CSS_KHZ","CSS_MATRIX","CSS_MATRIX3D","CSS_MM","CSS_MS","CSS_NUMBER","CSS_PC","CSS_PERCENTAGE","CSS_PERSPECTIVE","CSS_PRIMITIVE_VALUE","CSS_PT","CSS_PX","CSS_RAD","CSS_RECT","CSS_RGBCOLOR","CSS_ROTATE","CSS_ROTATE3D","CSS_ROTATEX","CSS_ROTATEY","CSS_ROTATEZ","CSS_S","CSS_SCALE","CSS_SCALE3D","CSS_SCALEX","CSS_SCALEY","CSS_SCALEZ","CSS_SKEW","CSS_SKEWX","CSS_SKEWY","CSS_STRING","CSS_TRANSLATE","CSS_TRANSLATE3D","CSS_TRANSLATEX","CSS_TRANSLATEY","CSS_TRANSLATEZ","CSS_UNKNOWN","CSS_URI","CSS_VALUE_LIST","CSS_VH","CSS_VMAX","CSS_VMIN","CSS_VW","CULL_FACE","CULL_FACE_MODE","CURRENT_PROGRAM","CURRENT_VERTEX_ATTRIB","CUSTOM","CW","CanvasGradient","CanvasPattern","CanvasRenderingContext2D","CaretPosition","ChannelMergerNode","ChannelSplitterNode","CharacterData","ClientRect","ClientRectList","Clipboard","ClipboardEvent","CloseEvent","Collator","CommandEvent","Comment","CompositionEvent","Console","Controllers","ConvolverNode","Counter","Crypto","CryptoKey","CustomEvent","DATABASE_ERR","DATA_CLONE_ERR","DATA_ERR","DBLCLICK","DECR","DECR_WRAP","DELETE_STATUS","DEPTH_ATTACHMENT","DEPTH_BITS","DEPTH_BUFFER_BIT","DEPTH_CLEAR_VALUE","DEPTH_COMPONENT","DEPTH_COMPONENT16","DEPTH_FUNC","DEPTH_RANGE","DEPTH_STENCIL","DEPTH_STENCIL_ATTACHMENT","DEPTH_TEST","DEPTH_WRITEMASK","DIRECTION_DOWN","DIRECTION_LEFT","DIRECTION_RIGHT","DIRECTION_UP","DISABLED","DISPATCH_REQUEST_ERR","DITHER","DOCUMENT_FRAGMENT_NODE","DOCUMENT_NODE","DOCUMENT_POSITION_CONTAINED_BY","DOCUMENT_POSITION_CONTAINS","DOCUMENT_POSITION_DISCONNECTED","DOCUMENT_POSITION_FOLLOWING","DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC","DOCUMENT_POSITION_PRECEDING","DOCUMENT_TYPE_NODE","DOMCursor","DOMError","DOMException","DOMImplementation","DOMImplementationLS","DOMMatrix","DOMMatrixReadOnly","DOMParser","DOMPoint","DOMPointReadOnly","DOMQuad","DOMRect","DOMRectList","DOMRectReadOnly","DOMRequest","DOMSTRING_SIZE_ERR","DOMSettableTokenList","DOMStringList","DOMStringMap","DOMTokenList","DOMTransactionEvent","DOM_DELTA_LINE","DOM_DELTA_PAGE","DOM_DELTA_PIXEL","DOM_INPUT_METHOD_DROP","DOM_INPUT_METHOD_HANDWRITING","DOM_INPUT_METHOD_IME","DOM_INPUT_METHOD_KEYBOARD","DOM_INPUT_METHOD_MULTIMODAL","DOM_INPUT_METHOD_OPTION","DOM_INPUT_METHOD_PASTE","DOM_INPUT_METHOD_SCRIPT","DOM_INPUT_METHOD_UNKNOWN","DOM_INPUT_METHOD_VOICE","DOM_KEY_LOCATION_JOYSTICK","DOM_KEY_LOCATION_LEFT","DOM_KEY_LOCATION_MOBILE","DOM_KEY_LOCATION_NUMPAD","DOM_KEY_LOCATION_RIGHT","DOM_KEY_LOCATION_STANDARD","DOM_VK_0","DOM_VK_1","DOM_VK_2","DOM_VK_3","DOM_VK_4","DOM_VK_5","DOM_VK_6","DOM_VK_7","DOM_VK_8","DOM_VK_9","DOM_VK_A","DOM_VK_ACCEPT","DOM_VK_ADD","DOM_VK_ALT","DOM_VK_ALTGR","DOM_VK_AMPERSAND","DOM_VK_ASTERISK","DOM_VK_AT","DOM_VK_ATTN","DOM_VK_B","DOM_VK_BACKSPACE","DOM_VK_BACK_QUOTE","DOM_VK_BACK_SLASH","DOM_VK_BACK_SPACE","DOM_VK_C","DOM_VK_CANCEL","DOM_VK_CAPS_LOCK","DOM_VK_CIRCUMFLEX","DOM_VK_CLEAR","DOM_VK_CLOSE_BRACKET","DOM_VK_CLOSE_CURLY_BRACKET","DOM_VK_CLOSE_PAREN","DOM_VK_COLON","DOM_VK_COMMA","DOM_VK_CONTEXT_MENU","DOM_VK_CONTROL","DOM_VK_CONVERT","DOM_VK_CRSEL","DOM_VK_CTRL","DOM_VK_D","DOM_VK_DECIMAL","DOM_VK_DELETE","DOM_VK_DIVIDE","DOM_VK_DOLLAR","DOM_VK_DOUBLE_QUOTE","DOM_VK_DOWN","DOM_VK_E","DOM_VK_EISU","DOM_VK_END","DOM_VK_ENTER","DOM_VK_EQUALS","DOM_VK_EREOF","DOM_VK_ESCAPE","DOM_VK_EXCLAMATION","DOM_VK_EXECUTE","DOM_VK_EXSEL","DOM_VK_F","DOM_VK_F1","DOM_VK_F10","DOM_VK_F11","DOM_VK_F12","DOM_VK_F13","DOM_VK_F14","DOM_VK_F15","DOM_VK_F16","DOM_VK_F17","DOM_VK_F18","DOM_VK_F19","DOM_VK_F2","DOM_VK_F20","DOM_VK_F21","DOM_VK_F22","DOM_VK_F23","DOM_VK_F24","DOM_VK_F25","DOM_VK_F26","DOM_VK_F27","DOM_VK_F28","DOM_VK_F29","DOM_VK_F3","DOM_VK_F30","DOM_VK_F31","DOM_VK_F32","DOM_VK_F33","DOM_VK_F34","DOM_VK_F35","DOM_VK_F36","DOM_VK_F4","DOM_VK_F5","DOM_VK_F6","DOM_VK_F7","DOM_VK_F8","DOM_VK_F9","DOM_VK_FINAL","DOM_VK_FRONT","DOM_VK_G","DOM_VK_GREATER_THAN","DOM_VK_H","DOM_VK_HANGUL","DOM_VK_HANJA","DOM_VK_HASH","DOM_VK_HELP","DOM_VK_HK_TOGGLE","DOM_VK_HOME","DOM_VK_HYPHEN_MINUS","DOM_VK_I","DOM_VK_INSERT","DOM_VK_J","DOM_VK_JUNJA","DOM_VK_K","DOM_VK_KANA","DOM_VK_KANJI","DOM_VK_L","DOM_VK_LEFT","DOM_VK_LEFT_TAB","DOM_VK_LESS_THAN","DOM_VK_M","DOM_VK_META","DOM_VK_MODECHANGE","DOM_VK_MULTIPLY","DOM_VK_N","DOM_VK_NONCONVERT","DOM_VK_NUMPAD0","DOM_VK_NUMPAD1","DOM_VK_NUMPAD2","DOM_VK_NUMPAD3","DOM_VK_NUMPAD4","DOM_VK_NUMPAD5","DOM_VK_NUMPAD6","DOM_VK_NUMPAD7","DOM_VK_NUMPAD8","DOM_VK_NUMPAD9","DOM_VK_NUM_LOCK","DOM_VK_O","DOM_VK_OEM_1","DOM_VK_OEM_102","DOM_VK_OEM_2","DOM_VK_OEM_3","DOM_VK_OEM_4","DOM_VK_OEM_5","DOM_VK_OEM_6","DOM_VK_OEM_7","DOM_VK_OEM_8","DOM_VK_OEM_COMMA","DOM_VK_OEM_MINUS","DOM_VK_OEM_PERIOD","DOM_VK_OEM_PLUS","DOM_VK_OPEN_BRACKET","DOM_VK_OPEN_CURLY_BRACKET","DOM_VK_OPEN_PAREN","DOM_VK_P","DOM_VK_PA1","DOM_VK_PAGEDOWN","DOM_VK_PAGEUP","DOM_VK_PAGE_DOWN","DOM_VK_PAGE_UP","DOM_VK_PAUSE","DOM_VK_PERCENT","DOM_VK_PERIOD","DOM_VK_PIPE","DOM_VK_PLAY","DOM_VK_PLUS","DOM_VK_PRINT","DOM_VK_PRINTSCREEN","DOM_VK_PROCESSKEY","DOM_VK_PROPERITES","DOM_VK_Q","DOM_VK_QUESTION_MARK","DOM_VK_QUOTE","DOM_VK_R","DOM_VK_REDO","DOM_VK_RETURN","DOM_VK_RIGHT","DOM_VK_S","DOM_VK_SCROLL_LOCK","DOM_VK_SELECT","DOM_VK_SEMICOLON","DOM_VK_SEPARATOR","DOM_VK_SHIFT","DOM_VK_SLASH","DOM_VK_SLEEP","DOM_VK_SPACE","DOM_VK_SUBTRACT","DOM_VK_T","DOM_VK_TAB","DOM_VK_TILDE","DOM_VK_U","DOM_VK_UNDERSCORE","DOM_VK_UNDO","DOM_VK_UNICODE","DOM_VK_UP","DOM_VK_V","DOM_VK_VOLUME_DOWN","DOM_VK_VOLUME_MUTE","DOM_VK_VOLUME_UP","DOM_VK_W","DOM_VK_WIN","DOM_VK_WINDOW","DOM_VK_WIN_ICO_00","DOM_VK_WIN_ICO_CLEAR","DOM_VK_WIN_ICO_HELP","DOM_VK_WIN_OEM_ATTN","DOM_VK_WIN_OEM_AUTO","DOM_VK_WIN_OEM_BACKTAB","DOM_VK_WIN_OEM_CLEAR","DOM_VK_WIN_OEM_COPY","DOM_VK_WIN_OEM_CUSEL","DOM_VK_WIN_OEM_ENLW","DOM_VK_WIN_OEM_FINISH","DOM_VK_WIN_OEM_FJ_JISHO","DOM_VK_WIN_OEM_FJ_LOYA","DOM_VK_WIN_OEM_FJ_MASSHOU","DOM_VK_WIN_OEM_FJ_ROYA","DOM_VK_WIN_OEM_FJ_TOUROKU","DOM_VK_WIN_OEM_JUMP","DOM_VK_WIN_OEM_PA1","DOM_VK_WIN_OEM_PA2","DOM_VK_WIN_OEM_PA3","DOM_VK_WIN_OEM_RESET","DOM_VK_WIN_OEM_WSCTRL","DOM_VK_X","DOM_VK_XF86XK_ADD_FAVORITE","DOM_VK_XF86XK_APPLICATION_LEFT","DOM_VK_XF86XK_APPLICATION_RIGHT","DOM_VK_XF86XK_AUDIO_CYCLE_TRACK","DOM_VK_XF86XK_AUDIO_FORWARD","DOM_VK_XF86XK_AUDIO_LOWER_VOLUME","DOM_VK_XF86XK_AUDIO_MEDIA","DOM_VK_XF86XK_AUDIO_MUTE","DOM_VK_XF86XK_AUDIO_NEXT","DOM_VK_XF86XK_AUDIO_PAUSE","DOM_VK_XF86XK_AUDIO_PLAY","DOM_VK_XF86XK_AUDIO_PREV","DOM_VK_XF86XK_AUDIO_RAISE_VOLUME","DOM_VK_XF86XK_AUDIO_RANDOM_PLAY","DOM_VK_XF86XK_AUDIO_RECORD","DOM_VK_XF86XK_AUDIO_REPEAT","DOM_VK_XF86XK_AUDIO_REWIND","DOM_VK_XF86XK_AUDIO_STOP","DOM_VK_XF86XK_AWAY","DOM_VK_XF86XK_BACK","DOM_VK_XF86XK_BACK_FORWARD","DOM_VK_XF86XK_BATTERY","DOM_VK_XF86XK_BLUE","DOM_VK_XF86XK_BLUETOOTH","DOM_VK_XF86XK_BOOK","DOM_VK_XF86XK_BRIGHTNESS_ADJUST","DOM_VK_XF86XK_CALCULATOR","DOM_VK_XF86XK_CALENDAR","DOM_VK_XF86XK_CD","DOM_VK_XF86XK_CLOSE","DOM_VK_XF86XK_COMMUNITY","DOM_VK_XF86XK_CONTRAST_ADJUST","DOM_VK_XF86XK_COPY","DOM_VK_XF86XK_CUT","DOM_VK_XF86XK_CYCLE_ANGLE","DOM_VK_XF86XK_DISPLAY","DOM_VK_XF86XK_DOCUMENTS","DOM_VK_XF86XK_DOS","DOM_VK_XF86XK_EJECT","DOM_VK_XF86XK_EXCEL","DOM_VK_XF86XK_EXPLORER","DOM_VK_XF86XK_FAVORITES","DOM_VK_XF86XK_FINANCE","DOM_VK_XF86XK_FORWARD","DOM_VK_XF86XK_FRAME_BACK","DOM_VK_XF86XK_FRAME_FORWARD","DOM_VK_XF86XK_GAME","DOM_VK_XF86XK_GO","DOM_VK_XF86XK_GREEN","DOM_VK_XF86XK_HIBERNATE","DOM_VK_XF86XK_HISTORY","DOM_VK_XF86XK_HOME_PAGE","DOM_VK_XF86XK_HOT_LINKS","DOM_VK_XF86XK_I_TOUCH","DOM_VK_XF86XK_KBD_BRIGHTNESS_DOWN","DOM_VK_XF86XK_KBD_BRIGHTNESS_UP","DOM_VK_XF86XK_KBD_LIGHT_ON_OFF","DOM_VK_XF86XK_LAUNCH0","DOM_VK_XF86XK_LAUNCH1","DOM_VK_XF86XK_LAUNCH2","DOM_VK_XF86XK_LAUNCH3","DOM_VK_XF86XK_LAUNCH4","DOM_VK_XF86XK_LAUNCH5","DOM_VK_XF86XK_LAUNCH6","DOM_VK_XF86XK_LAUNCH7","DOM_VK_XF86XK_LAUNCH8","DOM_VK_XF86XK_LAUNCH9","DOM_VK_XF86XK_LAUNCH_A","DOM_VK_XF86XK_LAUNCH_B","DOM_VK_XF86XK_LAUNCH_C","DOM_VK_XF86XK_LAUNCH_D","DOM_VK_XF86XK_LAUNCH_E","DOM_VK_XF86XK_LAUNCH_F","DOM_VK_XF86XK_LIGHT_BULB","DOM_VK_XF86XK_LOG_OFF","DOM_VK_XF86XK_MAIL","DOM_VK_XF86XK_MAIL_FORWARD","DOM_VK_XF86XK_MARKET","DOM_VK_XF86XK_MEETING","DOM_VK_XF86XK_MEMO","DOM_VK_XF86XK_MENU_KB","DOM_VK_XF86XK_MENU_PB","DOM_VK_XF86XK_MESSENGER","DOM_VK_XF86XK_MON_BRIGHTNESS_DOWN","DOM_VK_XF86XK_MON_BRIGHTNESS_UP","DOM_VK_XF86XK_MUSIC","DOM_VK_XF86XK_MY_COMPUTER","DOM_VK_XF86XK_MY_SITES","DOM_VK_XF86XK_NEW","DOM_VK_XF86XK_NEWS","DOM_VK_XF86XK_OFFICE_HOME","DOM_VK_XF86XK_OPEN","DOM_VK_XF86XK_OPEN_URL","DOM_VK_XF86XK_OPTION","DOM_VK_XF86XK_PASTE","DOM_VK_XF86XK_PHONE","DOM_VK_XF86XK_PICTURES","DOM_VK_XF86XK_POWER_DOWN","DOM_VK_XF86XK_POWER_OFF","DOM_VK_XF86XK_RED","DOM_VK_XF86XK_REFRESH","DOM_VK_XF86XK_RELOAD","DOM_VK_XF86XK_REPLY","DOM_VK_XF86XK_ROCKER_DOWN","DOM_VK_XF86XK_ROCKER_ENTER","DOM_VK_XF86XK_ROCKER_UP","DOM_VK_XF86XK_ROTATE_WINDOWS","DOM_VK_XF86XK_ROTATION_KB","DOM_VK_XF86XK_ROTATION_PB","DOM_VK_XF86XK_SAVE","DOM_VK_XF86XK_SCREEN_SAVER","DOM_VK_XF86XK_SCROLL_CLICK","DOM_VK_XF86XK_SCROLL_DOWN","DOM_VK_XF86XK_SCROLL_UP","DOM_VK_XF86XK_SEARCH","DOM_VK_XF86XK_SEND","DOM_VK_XF86XK_SHOP","DOM_VK_XF86XK_SPELL","DOM_VK_XF86XK_SPLIT_SCREEN","DOM_VK_XF86XK_STANDBY","DOM_VK_XF86XK_START","DOM_VK_XF86XK_STOP","DOM_VK_XF86XK_SUBTITLE","DOM_VK_XF86XK_SUPPORT","DOM_VK_XF86XK_SUSPEND","DOM_VK_XF86XK_TASK_PANE","DOM_VK_XF86XK_TERMINAL","DOM_VK_XF86XK_TIME","DOM_VK_XF86XK_TOOLS","DOM_VK_XF86XK_TOP_MENU","DOM_VK_XF86XK_TO_DO_LIST","DOM_VK_XF86XK_TRAVEL","DOM_VK_XF86XK_USER1KB","DOM_VK_XF86XK_USER2KB","DOM_VK_XF86XK_USER_PB","DOM_VK_XF86XK_UWB","DOM_VK_XF86XK_VENDOR_HOME","DOM_VK_XF86XK_VIDEO","DOM_VK_XF86XK_VIEW","DOM_VK_XF86XK_WAKE_UP","DOM_VK_XF86XK_WEB_CAM","DOM_VK_XF86XK_WHEEL_BUTTON","DOM_VK_XF86XK_WLAN","DOM_VK_XF86XK_WORD","DOM_VK_XF86XK_WWW","DOM_VK_XF86XK_XFER","DOM_VK_XF86XK_YELLOW","DOM_VK_XF86XK_ZOOM_IN","DOM_VK_XF86XK_ZOOM_OUT","DOM_VK_Y","DOM_VK_Z","DOM_VK_ZOOM","DONE","DONT_CARE","DOWNLOADING","DRAGDROP","DST_ALPHA","DST_COLOR","DYNAMIC_DRAW","DataChannel","DataTransfer","DataTransferItem","DataTransferItemList","DataView","Date","DateTimeFormat","DelayNode","DesktopNotification","DesktopNotificationCenter","DeviceLightEvent","DeviceMotionEvent","DeviceOrientationEvent","DeviceProximityEvent","DeviceStorage","DeviceStorageChangeEvent","Document","DocumentFragment","DocumentType","DragEvent","DynamicsCompressorNode","E","ELEMENT_ARRAY_BUFFER","ELEMENT_ARRAY_BUFFER_BINDING","ELEMENT_NODE","EMPTY","ENCODING_ERR","ENDED","END_TO_END","END_TO_START","ENTITY_NODE","ENTITY_REFERENCE_NODE","EPSILON","EQUAL","EQUALPOWER","ERROR","EXPONENTIAL_DISTANCE","Element","ElementQuery","Entity","EntityReference","Error","ErrorEvent","EvalError","Event","EventException","EventSource","EventTarget","External","FASTEST","FIDOSDK","FILTER_ACCEPT","FILTER_INTERRUPT","FILTER_REJECT","FILTER_SKIP","FINISHED_STATE","FIRST_ORDERED_NODE_TYPE","FLOAT","FLOAT_MAT2","FLOAT_MAT3","FLOAT_MAT4","FLOAT_VEC2","FLOAT_VEC3","FLOAT_VEC4","FOCUS","FONT_FACE_RULE","FONT_FEATURE_VALUES_RULE","FRAGMENT_SHADER","FRAGMENT_SHADER_DERIVATIVE_HINT_OES","FRAMEBUFFER","FRAMEBUFFER_ATTACHMENT_OBJECT_NAME","FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE","FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE","FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL","FRAMEBUFFER_BINDING","FRAMEBUFFER_COMPLETE","FRAMEBUFFER_INCOMPLETE_ATTACHMENT","FRAMEBUFFER_INCOMPLETE_DIMENSIONS","FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT","FRAMEBUFFER_UNSUPPORTED","FRONT","FRONT_AND_BACK","FRONT_FACE","FUNC_ADD","FUNC_REVERSE_SUBTRACT","FUNC_SUBTRACT","Feed","FeedEntry","File","FileError","FileList","FileReader","FindInPage","Float32Array","Float64Array","FocusEvent","FontFace","FormData","Function","GENERATE_MIPMAP_HINT","GEQUAL","GREATER","GREEN_BITS","GainNode","Gamepad","GamepadButton","GamepadEvent","GestureEvent","HAVE_CURRENT_DATA","HAVE_ENOUGH_DATA","HAVE_FUTURE_DATA","HAVE_METADATA","HAVE_NOTHING","HEADERS_RECEIVED","HIDDEN","HIERARCHY_REQUEST_ERR","HIGHPASS","HIGHSHELF","HIGH_FLOAT","HIGH_INT","HORIZONTAL","HORIZONTAL_AXIS","HRTF","HTMLAllCollection","HTMLAnchorElement","HTMLAppletElement","HTMLAreaElement","HTMLAudioElement","HTMLBRElement","HTMLBaseElement","HTMLBaseFontElement","HTMLBlockquoteElement","HTMLBodyElement","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLCommandElement","HTMLContentElement","HTMLDListElement","HTMLDataElement","HTMLDataListElement","HTMLDetailsElement","HTMLDialogElement","HTMLDirectoryElement","HTMLDivElement","HTMLDocument","HTMLElement","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameSetElement","HTMLHRElement","HTMLHeadElement","HTMLHeadingElement","HTMLHtmlElement","HTMLIFrameElement","HTMLImageElement","HTMLInputElement","HTMLIsIndexElement","HTMLKeygenElement","HTMLLIElement","HTMLLabelElement","HTMLLegendElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMediaElement","HTMLMenuElement","HTMLMenuItemElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLOListElement","HTMLObjectElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLPropertiesCollection","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLShadowElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","HashChangeEvent","Headers","History","ICE_CHECKING","ICE_CLOSED","ICE_COMPLETED","ICE_CONNECTED","ICE_FAILED","ICE_GATHERING","ICE_WAITING","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBDatabaseException","IDBFactory","IDBFileHandle","IDBFileRequest","IDBIndex","IDBKeyRange","IDBMutableFile","IDBObjectStore","IDBOpenDBRequest","IDBRequest","IDBTransaction","IDBVersionChangeEvent","IDLE","IMPLEMENTATION_COLOR_READ_FORMAT","IMPLEMENTATION_COLOR_READ_TYPE","IMPORT_RULE","INCR","INCR_WRAP","INDEX_SIZE_ERR","INT","INT_VEC2","INT_VEC3","INT_VEC4","INUSE_ATTRIBUTE_ERR","INVALID_ACCESS_ERR","INVALID_CHARACTER_ERR","INVALID_ENUM","INVALID_EXPRESSION_ERR","INVALID_FRAMEBUFFER_OPERATION","INVALID_MODIFICATION_ERR","INVALID_NODE_TYPE_ERR","INVALID_OPERATION","INVALID_STATE_ERR","INVALID_VALUE","INVERSE_DISTANCE","INVERT","IceCandidate","Image","ImageBitmap","ImageData","Infinity","InputEvent","InputMethodContext","InstallTrigger","Int16Array","Int32Array","Int8Array","Intent","InternalError","Intl","IsSearchProviderInstalled","Iterator","JSON","KEEP","KEYDOWN","KEYFRAMES_RULE","KEYFRAME_RULE","KEYPRESS","KEYUP","KeyEvent","KeyboardEvent","LENGTHADJUST_SPACING","LENGTHADJUST_SPACINGANDGLYPHS","LENGTHADJUST_UNKNOWN","LEQUAL","LESS","LINEAR","LINEAR_DISTANCE","LINEAR_MIPMAP_LINEAR","LINEAR_MIPMAP_NEAREST","LINES","LINE_LOOP","LINE_STRIP","LINE_WIDTH","LINK_STATUS","LIVE","LN10","LN2","LOADED","LOADING","LOG10E","LOG2E","LOWPASS","LOWSHELF","LOW_FLOAT","LOW_INT","LSException","LSParserFilter","LUMINANCE","LUMINANCE_ALPHA","LocalMediaStream","Location","MAX_COMBINED_TEXTURE_IMAGE_UNITS","MAX_CUBE_MAP_TEXTURE_SIZE","MAX_FRAGMENT_UNIFORM_VECTORS","MAX_RENDERBUFFER_SIZE","MAX_SAFE_INTEGER","MAX_TEXTURE_IMAGE_UNITS","MAX_TEXTURE_MAX_ANISOTROPY_EXT","MAX_TEXTURE_SIZE","MAX_VALUE","MAX_VARYING_VECTORS","MAX_VERTEX_ATTRIBS","MAX_VERTEX_TEXTURE_IMAGE_UNITS","MAX_VERTEX_UNIFORM_VECTORS","MAX_VIEWPORT_DIMS","MEDIA_ERR_ABORTED","MEDIA_ERR_DECODE","MEDIA_ERR_ENCRYPTED","MEDIA_ERR_NETWORK","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_KEYERR_CLIENT","MEDIA_KEYERR_DOMAIN","MEDIA_KEYERR_HARDWARECHANGE","MEDIA_KEYERR_OUTPUT","MEDIA_KEYERR_SERVICE","MEDIA_KEYERR_UNKNOWN","MEDIA_RULE","MEDIUM_FLOAT","MEDIUM_INT","META_MASK","MIN_SAFE_INTEGER","MIN_VALUE","MIRRORED_REPEAT","MODE_ASYNCHRONOUS","MODE_SYNCHRONOUS","MODIFICATION","MOUSEDOWN","MOUSEDRAG","MOUSEMOVE","MOUSEOUT","MOUSEOVER","MOUSEUP","MOZ_KEYFRAMES_RULE","MOZ_KEYFRAME_RULE","MOZ_SOURCE_CURSOR","MOZ_SOURCE_ERASER","MOZ_SOURCE_KEYBOARD","MOZ_SOURCE_MOUSE","MOZ_SOURCE_PEN","MOZ_SOURCE_TOUCH","MOZ_SOURCE_UNKNOWN","MSGESTURE_FLAG_BEGIN","MSGESTURE_FLAG_CANCEL","MSGESTURE_FLAG_END","MSGESTURE_FLAG_INERTIA","MSGESTURE_FLAG_NONE","MSPOINTER_TYPE_MOUSE","MSPOINTER_TYPE_PEN","MSPOINTER_TYPE_TOUCH","MS_ASYNC_CALLBACK_STATUS_ASSIGN_DELEGATE","MS_ASYNC_CALLBACK_STATUS_CANCEL","MS_ASYNC_CALLBACK_STATUS_CHOOSEANY","MS_ASYNC_CALLBACK_STATUS_ERROR","MS_ASYNC_CALLBACK_STATUS_JOIN","MS_ASYNC_OP_STATUS_CANCELED","MS_ASYNC_OP_STATUS_ERROR","MS_ASYNC_OP_STATUS_SUCCESS","MS_MANIPULATION_STATE_ACTIVE","MS_MANIPULATION_STATE_CANCELLED","MS_MANIPULATION_STATE_COMMITTED","MS_MANIPULATION_STATE_DRAGGING","MS_MANIPULATION_STATE_INERTIA","MS_MANIPULATION_STATE_PRESELECT","MS_MANIPULATION_STATE_SELECTING","MS_MANIPULATION_STATE_STOPPED","MS_MEDIA_ERR_ENCRYPTED","MS_MEDIA_KEYERR_CLIENT","MS_MEDIA_KEYERR_DOMAIN","MS_MEDIA_KEYERR_HARDWARECHANGE","MS_MEDIA_KEYERR_OUTPUT","MS_MEDIA_KEYERR_SERVICE","MS_MEDIA_KEYERR_UNKNOWN","Map","Math","MediaController","MediaDevices","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaError","MediaKeyError","MediaKeyEvent","MediaKeyMessageEvent","MediaKeyNeededEvent","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaKeys","MediaList","MediaQueryList","MediaQueryListEvent","MediaRecorder","MediaSource","MediaStream","MediaStreamAudioDestinationNode","MediaStreamAudioSourceNode","MediaStreamEvent","MediaStreamTrack","MediaStreamTrackEvent","MessageChannel","MessageEvent","MessagePort","Methods","MimeType","MimeTypeArray","MouseEvent","MouseScrollEvent","MozAnimation","MozAnimationDelay","MozAnimationDirection","MozAnimationDuration","MozAnimationFillMode","MozAnimationIterationCount","MozAnimationName","MozAnimationPlayState","MozAnimationTimingFunction","MozAppearance","MozBackfaceVisibility","MozBinding","MozBorderBottomColors","MozBorderEnd","MozBorderEndColor","MozBorderEndStyle","MozBorderEndWidth","MozBorderImage","MozBorderLeftColors","MozBorderRightColors","MozBorderStart","MozBorderStartColor","MozBorderStartStyle","MozBorderStartWidth","MozBorderTopColors","MozBoxAlign","MozBoxDirection","MozBoxFlex","MozBoxOrdinalGroup","MozBoxOrient","MozBoxPack","MozBoxSizing","MozCSSKeyframeRule","MozCSSKeyframesRule","MozColumnCount","MozColumnFill","MozColumnGap","MozColumnRule","MozColumnRuleColor","MozColumnRuleStyle","MozColumnRuleWidth","MozColumnWidth","MozColumns","MozContactChangeEvent","MozFloatEdge","MozFontFeatureSettings","MozFontLanguageOverride","MozForceBrokenImageIcon","MozHyphens","MozImageRegion","MozMarginEnd","MozMarginStart","MozMmsEvent","MozMmsMessage","MozMobileMessageThread","MozOSXFontSmoothing","MozOrient","MozOutlineRadius","MozOutlineRadiusBottomleft","MozOutlineRadiusBottomright","MozOutlineRadiusTopleft","MozOutlineRadiusTopright","MozPaddingEnd","MozPaddingStart","MozPerspective","MozPerspectiveOrigin","MozPowerManager","MozSettingsEvent","MozSmsEvent","MozSmsMessage","MozStackSizing","MozTabSize","MozTextAlignLast","MozTextDecorationColor","MozTextDecorationLine","MozTextDecorationStyle","MozTextSizeAdjust","MozTransform","MozTransformOrigin","MozTransformStyle","MozTransition","MozTransitionDelay","MozTransitionDuration","MozTransitionProperty","MozTransitionTimingFunction","MozUserFocus","MozUserInput","MozUserModify","MozUserSelect","MozWindowDragging","MozWindowShadow","MutationEvent","MutationObserver","MutationRecord","NAMESPACE_ERR","NAMESPACE_RULE","NEAREST","NEAREST_MIPMAP_LINEAR","NEAREST_MIPMAP_NEAREST","NEGATIVE_INFINITY","NETWORK_EMPTY","NETWORK_ERR","NETWORK_IDLE","NETWORK_LOADED","NETWORK_LOADING","NETWORK_NO_SOURCE","NEVER","NEW","NEXT","NEXT_NO_DUPLICATE","NICEST","NODE_AFTER","NODE_BEFORE","NODE_BEFORE_AND_AFTER","NODE_INSIDE","NONE","NON_TRANSIENT_ERR","NOTATION_NODE","NOTCH","NOTEQUAL","NOT_ALLOWED_ERR","NOT_FOUND_ERR","NOT_READABLE_ERR","NOT_SUPPORTED_ERR","NO_DATA_ALLOWED_ERR","NO_ERR","NO_ERROR","NO_MODIFICATION_ALLOWED_ERR","NUMBER_TYPE","NUM_COMPRESSED_TEXTURE_FORMATS","NaN","NamedNodeMap","Navigator","NearbyLinks","NetworkInformation","Node","NodeFilter","NodeIterator","NodeList","Notation","Notification","NotifyPaintEvent","Number","NumberFormat","OBSOLETE","ONE","ONE_MINUS_CONSTANT_ALPHA","ONE_MINUS_CONSTANT_COLOR","ONE_MINUS_DST_ALPHA","ONE_MINUS_DST_COLOR","ONE_MINUS_SRC_ALPHA","ONE_MINUS_SRC_COLOR","OPEN","OPENED","OPENING","ORDERED_NODE_ITERATOR_TYPE","ORDERED_NODE_SNAPSHOT_TYPE","OUT_OF_MEMORY","Object","OfflineAudioCompletionEvent","OfflineAudioContext","OfflineResourceList","Option","OscillatorNode","OverflowEvent","PACK_ALIGNMENT","PAGE_RULE","PARSE_ERR","PATHSEG_ARC_ABS","PATHSEG_ARC_REL","PATHSEG_CLOSEPATH","PATHSEG_CURVETO_CUBIC_ABS","PATHSEG_CURVETO_CUBIC_REL","PATHSEG_CURVETO_CUBIC_SMOOTH_ABS","PATHSEG_CURVETO_CUBIC_SMOOTH_REL","PATHSEG_CURVETO_QUADRATIC_ABS","PATHSEG_CURVETO_QUADRATIC_REL","PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS","PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL","PATHSEG_LINETO_ABS","PATHSEG_LINETO_HORIZONTAL_ABS","PATHSEG_LINETO_HORIZONTAL_REL","PATHSEG_LINETO_REL","PATHSEG_LINETO_VERTICAL_ABS","PATHSEG_LINETO_VERTICAL_REL","PATHSEG_MOVETO_ABS","PATHSEG_MOVETO_REL","PATHSEG_UNKNOWN","PATH_EXISTS_ERR","PEAKING","PERMISSION_DENIED","PERSISTENT","PI","PLAYING_STATE","POINTS","POLYGON_OFFSET_FACTOR","POLYGON_OFFSET_FILL","POLYGON_OFFSET_UNITS","POSITION_UNAVAILABLE","POSITIVE_INFINITY","PREV","PREV_NO_DUPLICATE","PROCESSING_INSTRUCTION_NODE","PageChangeEvent","PageTransitionEvent","PaintRequest","PaintRequestList","PannerNode","Path2D","Performance","PerformanceEntry","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceResourceTiming","PerformanceTiming","PeriodicWave","Plugin","PluginArray","PopStateEvent","PopupBlockedEvent","ProcessingInstruction","ProgressEvent","Promise","PropertyNodeList","Proxy","PushManager","PushSubscription","Q","QUOTA_ERR","QUOTA_EXCEEDED_ERR","QueryInterface","READ_ONLY","READ_ONLY_ERR","READ_WRITE","RED_BITS","REMOVAL","RENDERBUFFER","RENDERBUFFER_ALPHA_SIZE","RENDERBUFFER_BINDING","RENDERBUFFER_BLUE_SIZE","RENDERBUFFER_DEPTH_SIZE","RENDERBUFFER_GREEN_SIZE","RENDERBUFFER_HEIGHT","RENDERBUFFER_INTERNAL_FORMAT","RENDERBUFFER_RED_SIZE","RENDERBUFFER_STENCIL_SIZE","RENDERBUFFER_WIDTH","RENDERER","RENDERING_INTENT_ABSOLUTE_COLORIMETRIC","RENDERING_INTENT_AUTO","RENDERING_INTENT_PERCEPTUAL","RENDERING_INTENT_RELATIVE_COLORIMETRIC","RENDERING_INTENT_SATURATION","RENDERING_INTENT_UNKNOWN","REPEAT","REPLACE","RGB","RGB565","RGB5_A1","RGBA","RGBA4","RGBColor","ROTATION_CLOCKWISE","ROTATION_COUNTERCLOCKWISE","RTCDataChannelEvent","RTCIceCandidate","RTCPeerConnectionIceEvent","RTCRtpReceiver","RTCRtpSender","RTCSessionDescription","RTCStatsReport","RadioNodeList","Range","RangeError","RangeException","RecordErrorEvent","Rect","ReferenceError","RegExp","Request","Response","SAMPLER_2D","SAMPLER_CUBE","SAMPLES","SAMPLE_ALPHA_TO_COVERAGE","SAMPLE_BUFFERS","SAMPLE_COVERAGE","SAMPLE_COVERAGE_INVERT","SAMPLE_COVERAGE_VALUE","SAWTOOTH","SCHEDULED_STATE","SCISSOR_BOX","SCISSOR_TEST","SCROLL_PAGE_DOWN","SCROLL_PAGE_UP","SDP_ANSWER","SDP_OFFER","SDP_PRANSWER","SECURITY_ERR","SELECT","SERIALIZE_ERR","SEVERITY_ERROR","SEVERITY_FATAL_ERROR","SEVERITY_WARNING","SHADER_COMPILER","SHADER_TYPE","SHADING_LANGUAGE_VERSION","SHIFT_MASK","SHORT","SHOWING","SHOW_ALL","SHOW_ATTRIBUTE","SHOW_CDATA_SECTION","SHOW_COMMENT","SHOW_DOCUMENT","SHOW_DOCUMENT_FRAGMENT","SHOW_DOCUMENT_TYPE","SHOW_ELEMENT","SHOW_ENTITY","SHOW_ENTITY_REFERENCE","SHOW_NOTATION","SHOW_PROCESSING_INSTRUCTION","SHOW_TEXT","SINE","SOUNDFIELD","SQLException","SQRT1_2","SQRT2","SQUARE","SRC_ALPHA","SRC_ALPHA_SATURATE","SRC_COLOR","START_TO_END","START_TO_START","STATIC_DRAW","STENCIL_ATTACHMENT","STENCIL_BACK_FAIL","STENCIL_BACK_FUNC","STENCIL_BACK_PASS_DEPTH_FAIL","STENCIL_BACK_PASS_DEPTH_PASS","STENCIL_BACK_REF","STENCIL_BACK_VALUE_MASK","STENCIL_BACK_WRITEMASK","STENCIL_BITS","STENCIL_BUFFER_BIT","STENCIL_CLEAR_VALUE","STENCIL_FAIL","STENCIL_FUNC","STENCIL_INDEX","STENCIL_INDEX8","STENCIL_PASS_DEPTH_FAIL","STENCIL_PASS_DEPTH_PASS","STENCIL_REF","STENCIL_TEST","STENCIL_VALUE_MASK","STENCIL_WRITEMASK","STREAM_DRAW","STRING_TYPE","STYLE_RULE","SUBPIXEL_BITS","SUPPORTS_RULE","SVGAElement","SVGAltGlyphDefElement","SVGAltGlyphElement","SVGAltGlyphItemElement","SVGAngle","SVGAnimateColorElement","SVGAnimateElement","SVGAnimateMotionElement","SVGAnimateTransformElement","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGAnimationElement","SVGCircleElement","SVGClipPathElement","SVGColor","SVGComponentTransferFunctionElement","SVGCursorElement","SVGDefsElement","SVGDescElement","SVGDiscardElement","SVGDocument","SVGElement","SVGElementInstance","SVGElementInstanceList","SVGEllipseElement","SVGException","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEDropShadowElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGFontElement","SVGFontFaceElement","SVGFontFaceFormatElement","SVGFontFaceNameElement","SVGFontFaceSrcElement","SVGFontFaceUriElement","SVGForeignObjectElement","SVGGElement","SVGGeometryElement","SVGGlyphElement","SVGGlyphRefElement","SVGGradientElement","SVGGraphicsElement","SVGHKernElement","SVGImageElement","SVGLength","SVGLengthList","SVGLineElement","SVGLinearGradientElement","SVGMPathElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGMissingGlyphElement","SVGNumber","SVGNumberList","SVGPaint","SVGPathElement","SVGPathSeg","SVGPathSegArcAbs","SVGPathSegArcRel","SVGPathSegClosePath","SVGPathSegCurvetoCubicAbs","SVGPathSegCurvetoCubicRel","SVGPathSegCurvetoCubicSmoothAbs","SVGPathSegCurvetoCubicSmoothRel","SVGPathSegCurvetoQuadraticAbs","SVGPathSegCurvetoQuadraticRel","SVGPathSegCurvetoQuadraticSmoothAbs","SVGPathSegCurvetoQuadraticSmoothRel","SVGPathSegLinetoAbs","SVGPathSegLinetoHorizontalAbs","SVGPathSegLinetoHorizontalRel","SVGPathSegLinetoRel","SVGPathSegLinetoVerticalAbs","SVGPathSegLinetoVerticalRel","SVGPathSegList","SVGPathSegMovetoAbs","SVGPathSegMovetoRel","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGRenderingIntent","SVGSVGElement","SVGScriptElement","SVGSetElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSwitchElement","SVGSymbolElement","SVGTRefElement","SVGTSpanElement","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGUnitTypes","SVGUseElement","SVGVKernElement","SVGViewElement","SVGViewSpec","SVGZoomAndPan","SVGZoomEvent","SVG_ANGLETYPE_DEG","SVG_ANGLETYPE_GRAD","SVG_ANGLETYPE_RAD","SVG_ANGLETYPE_UNKNOWN","SVG_ANGLETYPE_UNSPECIFIED","SVG_CHANNEL_A","SVG_CHANNEL_B","SVG_CHANNEL_G","SVG_CHANNEL_R","SVG_CHANNEL_UNKNOWN","SVG_COLORTYPE_CURRENTCOLOR","SVG_COLORTYPE_RGBCOLOR","SVG_COLORTYPE_RGBCOLOR_ICCCOLOR","SVG_COLORTYPE_UNKNOWN","SVG_EDGEMODE_DUPLICATE","SVG_EDGEMODE_NONE","SVG_EDGEMODE_UNKNOWN","SVG_EDGEMODE_WRAP","SVG_FEBLEND_MODE_COLOR","SVG_FEBLEND_MODE_COLOR_BURN","SVG_FEBLEND_MODE_COLOR_DODGE","SVG_FEBLEND_MODE_DARKEN","SVG_FEBLEND_MODE_DIFFERENCE","SVG_FEBLEND_MODE_EXCLUSION","SVG_FEBLEND_MODE_HARD_LIGHT","SVG_FEBLEND_MODE_HUE","SVG_FEBLEND_MODE_LIGHTEN","SVG_FEBLEND_MODE_LUMINOSITY","SVG_FEBLEND_MODE_MULTIPLY","SVG_FEBLEND_MODE_NORMAL","SVG_FEBLEND_MODE_OVERLAY","SVG_FEBLEND_MODE_SATURATION","SVG_FEBLEND_MODE_SCREEN","SVG_FEBLEND_MODE_SOFT_LIGHT","SVG_FEBLEND_MODE_UNKNOWN","SVG_FECOLORMATRIX_TYPE_HUEROTATE","SVG_FECOLORMATRIX_TYPE_LUMINANCETOALPHA","SVG_FECOLORMATRIX_TYPE_MATRIX","SVG_FECOLORMATRIX_TYPE_SATURATE","SVG_FECOLORMATRIX_TYPE_UNKNOWN","SVG_FECOMPONENTTRANSFER_TYPE_DISCRETE","SVG_FECOMPONENTTRANSFER_TYPE_GAMMA","SVG_FECOMPONENTTRANSFER_TYPE_IDENTITY","SVG_FECOMPONENTTRANSFER_TYPE_LINEAR","SVG_FECOMPONENTTRANSFER_TYPE_TABLE","SVG_FECOMPONENTTRANSFER_TYPE_UNKNOWN","SVG_FECOMPOSITE_OPERATOR_ARITHMETIC","SVG_FECOMPOSITE_OPERATOR_ATOP","SVG_FECOMPOSITE_OPERATOR_IN","SVG_FECOMPOSITE_OPERATOR_OUT","SVG_FECOMPOSITE_OPERATOR_OVER","SVG_FECOMPOSITE_OPERATOR_UNKNOWN","SVG_FECOMPOSITE_OPERATOR_XOR","SVG_INVALID_VALUE_ERR","SVG_LENGTHTYPE_CM","SVG_LENGTHTYPE_EMS","SVG_LENGTHTYPE_EXS","SVG_LENGTHTYPE_IN","SVG_LENGTHTYPE_MM","SVG_LENGTHTYPE_NUMBER","SVG_LENGTHTYPE_PC","SVG_LENGTHTYPE_PERCENTAGE","SVG_LENGTHTYPE_PT","SVG_LENGTHTYPE_PX","SVG_LENGTHTYPE_UNKNOWN","SVG_MARKERUNITS_STROKEWIDTH","SVG_MARKERUNITS_UNKNOWN","SVG_MARKERUNITS_USERSPACEONUSE","SVG_MARKER_ORIENT_ANGLE","SVG_MARKER_ORIENT_AUTO","SVG_MARKER_ORIENT_UNKNOWN","SVG_MASKTYPE_ALPHA","SVG_MASKTYPE_LUMINANCE","SVG_MATRIX_NOT_INVERTABLE","SVG_MEETORSLICE_MEET","SVG_MEETORSLICE_SLICE","SVG_MEETORSLICE_UNKNOWN","SVG_MORPHOLOGY_OPERATOR_DILATE","SVG_MORPHOLOGY_OPERATOR_ERODE","SVG_MORPHOLOGY_OPERATOR_UNKNOWN","SVG_PAINTTYPE_CURRENTCOLOR","SVG_PAINTTYPE_NONE","SVG_PAINTTYPE_RGBCOLOR","SVG_PAINTTYPE_RGBCOLOR_ICCCOLOR","SVG_PAINTTYPE_UNKNOWN","SVG_PAINTTYPE_URI","SVG_PAINTTYPE_URI_CURRENTCOLOR","SVG_PAINTTYPE_URI_NONE","SVG_PAINTTYPE_URI_RGBCOLOR","SVG_PAINTTYPE_URI_RGBCOLOR_ICCCOLOR","SVG_PRESERVEASPECTRATIO_NONE","SVG_PRESERVEASPECTRATIO_UNKNOWN","SVG_PRESERVEASPECTRATIO_XMAXYMAX","SVG_PRESERVEASPECTRATIO_XMAXYMID","SVG_PRESERVEASPECTRATIO_XMAXYMIN","SVG_PRESERVEASPECTRATIO_XMIDYMAX","SVG_PRESERVEASPECTRATIO_XMIDYMID","SVG_PRESERVEASPECTRATIO_XMIDYMIN","SVG_PRESERVEASPECTRATIO_XMINYMAX","SVG_PRESERVEASPECTRATIO_XMINYMID","SVG_PRESERVEASPECTRATIO_XMINYMIN","SVG_SPREADMETHOD_PAD","SVG_SPREADMETHOD_REFLECT","SVG_SPREADMETHOD_REPEAT","SVG_SPREADMETHOD_UNKNOWN","SVG_STITCHTYPE_NOSTITCH","SVG_STITCHTYPE_STITCH","SVG_STITCHTYPE_UNKNOWN","SVG_TRANSFORM_MATRIX","SVG_TRANSFORM_ROTATE","SVG_TRANSFORM_SCALE","SVG_TRANSFORM_SKEWX","SVG_TRANSFORM_SKEWY","SVG_TRANSFORM_TRANSLATE","SVG_TRANSFORM_UNKNOWN","SVG_TURBULENCE_TYPE_FRACTALNOISE","SVG_TURBULENCE_TYPE_TURBULENCE","SVG_TURBULENCE_TYPE_UNKNOWN","SVG_UNIT_TYPE_OBJECTBOUNDINGBOX","SVG_UNIT_TYPE_UNKNOWN","SVG_UNIT_TYPE_USERSPACEONUSE","SVG_WRONG_TYPE_ERR","SVG_ZOOMANDPAN_DISABLE","SVG_ZOOMANDPAN_MAGNIFY","SVG_ZOOMANDPAN_UNKNOWN","SYNTAX_ERR","SavedPages","Screen","ScreenOrientation","Script","ScriptProcessorNode","ScrollAreaEvent","SecurityPolicyViolationEvent","Selection","ServiceWorker","ServiceWorkerContainer","ServiceWorkerRegistration","SessionDescription","Set","ShadowRoot","SharedWorker","SimpleGestureEvent","SpeechSynthesisEvent","SpeechSynthesisUtterance","StopIteration","Storage","StorageEvent","String","StyleSheet","StyleSheetList","SubtleCrypto","Symbol","SyntaxError","TEMPORARY","TEXTPATH_METHODTYPE_ALIGN","TEXTPATH_METHODTYPE_STRETCH","TEXTPATH_METHODTYPE_UNKNOWN","TEXTPATH_SPACINGTYPE_AUTO","TEXTPATH_SPACINGTYPE_EXACT","TEXTPATH_SPACINGTYPE_UNKNOWN","TEXTURE","TEXTURE0","TEXTURE1","TEXTURE10","TEXTURE11","TEXTURE12","TEXTURE13","TEXTURE14","TEXTURE15","TEXTURE16","TEXTURE17","TEXTURE18","TEXTURE19","TEXTURE2","TEXTURE20","TEXTURE21","TEXTURE22","TEXTURE23","TEXTURE24","TEXTURE25","TEXTURE26","TEXTURE27","TEXTURE28","TEXTURE29","TEXTURE3","TEXTURE30","TEXTURE31","TEXTURE4","TEXTURE5","TEXTURE6","TEXTURE7","TEXTURE8","TEXTURE9","TEXTURE_2D","TEXTURE_BINDING_2D","TEXTURE_BINDING_CUBE_MAP","TEXTURE_CUBE_MAP","TEXTURE_CUBE_MAP_NEGATIVE_X","TEXTURE_CUBE_MAP_NEGATIVE_Y","TEXTURE_CUBE_MAP_NEGATIVE_Z","TEXTURE_CUBE_MAP_POSITIVE_X","TEXTURE_CUBE_MAP_POSITIVE_Y","TEXTURE_CUBE_MAP_POSITIVE_Z","TEXTURE_MAG_FILTER","TEXTURE_MAX_ANISOTROPY_EXT","TEXTURE_MIN_FILTER","TEXTURE_WRAP_S","TEXTURE_WRAP_T","TEXT_NODE","TIMEOUT","TIMEOUT_ERR","TOO_LARGE_ERR","TRANSACTION_INACTIVE_ERR","TRIANGLE","TRIANGLES","TRIANGLE_FAN","TRIANGLE_STRIP","TYPE_BACK_FORWARD","TYPE_ERR","TYPE_MISMATCH_ERR","TYPE_NAVIGATE","TYPE_RELOAD","TYPE_RESERVED","Text","TextDecoder","TextEncoder","TextEvent","TextMetrics","TextTrack","TextTrackCue","TextTrackCueList","TextTrackList","TimeEvent","TimeRanges","Touch","TouchEvent","TouchList","TrackEvent","TransitionEvent","TreeWalker","TypeError","UIEvent","UNCACHED","UNKNOWN_ERR","UNKNOWN_RULE","UNMASKED_RENDERER_WEBGL","UNMASKED_VENDOR_WEBGL","UNORDERED_NODE_ITERATOR_TYPE","UNORDERED_NODE_SNAPSHOT_TYPE","UNPACK_ALIGNMENT","UNPACK_COLORSPACE_CONVERSION_WEBGL","UNPACK_FLIP_Y_WEBGL","UNPACK_PREMULTIPLY_ALPHA_WEBGL","UNSCHEDULED_STATE","UNSENT","UNSIGNED_BYTE","UNSIGNED_INT","UNSIGNED_SHORT","UNSIGNED_SHORT_4_4_4_4","UNSIGNED_SHORT_5_5_5_1","UNSIGNED_SHORT_5_6_5","UNSPECIFIED_EVENT_TYPE_ERR","UPDATEREADY","URIError","URL","URLSearchParams","URLUnencoded","URL_MISMATCH_ERR","UTC","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","UserMessageHandler","UserMessageHandlersNamespace","UserProximityEvent","VALIDATE_STATUS","VALIDATION_ERR","VARIABLES_RULE","VENDOR","VERSION","VERSION_CHANGE","VERSION_ERR","VERTEX_ATTRIB_ARRAY_BUFFER_BINDING","VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE","VERTEX_ATTRIB_ARRAY_ENABLED","VERTEX_ATTRIB_ARRAY_NORMALIZED","VERTEX_ATTRIB_ARRAY_POINTER","VERTEX_ATTRIB_ARRAY_SIZE","VERTEX_ATTRIB_ARRAY_STRIDE","VERTEX_ATTRIB_ARRAY_TYPE","VERTEX_SHADER","VERTICAL","VERTICAL_AXIS","VER_ERR","VIEWPORT","VIEWPORT_RULE","VTTCue","VTTRegion","ValidityState","VideoStreamTrack","WEBKIT_FILTER_RULE","WEBKIT_KEYFRAMES_RULE","WEBKIT_KEYFRAME_RULE","WEBKIT_REGION_RULE","WRONG_DOCUMENT_ERR","WaveShaperNode","WeakMap","WeakSet","WebGLActiveInfo","WebGLBuffer","WebGLContextEvent","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLRenderingContext","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArray","WebKitAnimationEvent","WebKitBlobBuilder","WebKitCSSFilterRule","WebKitCSSFilterValue","WebKitCSSKeyframeRule","WebKitCSSKeyframesRule","WebKitCSSMatrix","WebKitCSSRegionRule","WebKitCSSTransformValue","WebKitDataCue","WebKitGamepad","WebKitMediaKeyError","WebKitMediaKeyMessageEvent","WebKitMediaKeySession","WebKitMediaKeys","WebKitMediaSource","WebKitMutationObserver","WebKitNamespace","WebKitPlaybackTargetAvailabilityEvent","WebKitPoint","WebKitShadowRoot","WebKitSourceBuffer","WebKitSourceBufferList","WebKitTransitionEvent","WebSocket","WheelEvent","Window","Worker","XMLDocument","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestException","XMLHttpRequestProgressEvent","XMLHttpRequestUpload","XMLSerializer","XMLStylesheetProcessingInstruction","XPathEvaluator","XPathException","XPathExpression","XPathNSResolver","XPathResult","XSLTProcessor","ZERO","_XD0M_","_YD0M_","__defineGetter__","__defineSetter__","__lookupGetter__","__lookupSetter__","__opera","__proto__","_browserjsran","a","aLink","abbr","abort","abs","absolute","acceleration","accelerationIncludingGravity","accelerator","accept","acceptCharset","acceptNode","accessKey","accessKeyLabel","accuracy","acos","acosh","action","actionURL","active","activeCues","activeElement","activeSourceBuffers","activeSourceCount","activeTexture","add","addBehavior","addCandidate","addColorStop","addCue","addElement","addEventListener","addFilter","addFromString","addFromUri","addIceCandidate","addImport","addListener","addNamed","addPageRule","addPath","addPointer","addRange","addRegion","addRule","addSearchEngine","addSourceBuffer","addStream","addTextTrack","addTrack","addWakeLockListener","addedNodes","additionalName","additiveSymbols","addons","adoptNode","adr","advance","alert","algorithm","align","align-content","align-items","align-self","alignContent","alignItems","alignSelf","alignmentBaseline","alinkColor","all","allSettled","allowFullscreen","allowedDirections","alpha","alt","altGraphKey","altHtml","altKey","altLeft","altitude","altitudeAccuracy","amplitude","ancestorOrigins","anchor","anchorNode","anchorOffset","anchors","angle","animVal","animate","animatedInstanceRoot","animatedNormalizedPathSegList","animatedPathSegList","animatedPoints","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","animationDelay","animationDirection","animationDuration","animationFillMode","animationIterationCount","animationName","animationPlayState","animationStartTime","animationTimingFunction","animationsPaused","anniversary","any","app","appCodeName","appMinorVersion","appName","appNotifications","appVersion","append","appendBuffer","appendChild","appendData","appendItem","appendMedium","appendNamed","appendRule","appendStream","appendWindowEnd","appendWindowStart","applets","applicationCache","apply","applyElement","arc","arcTo","archive","areas","arguments","arrayBuffer","asin","asinh","assert","assign","async","atEnd","atan","atan2","atanh","atob","attachEvent","attachShader","attachShadow","attachments","attack","attrChange","attrName","attributeFilter","attributeName","attributeNamespace","attributeOldValue","attributes","audioTracks","autoIncrement","autobuffer","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","availHeight","availLeft","availTop","availWidth","availability","available","aversion","axes","axis","azimuth","b","back","backface-visibility","backfaceVisibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","backgroundAttachment","backgroundBlendMode","backgroundClip","backgroundColor","backgroundImage","backgroundOrigin","backgroundPosition","backgroundPositionX","backgroundPositionY","backgroundRepeat","backgroundSize","badInput","balance","baseFrequencyX","baseFrequencyY","baseNode","baseOffset","baseURI","baseVal","baselineShift","battery","bday","beginElement","beginElementAt","beginPath","behavior","behaviorCookie","behaviorPart","behaviorUrns","beta","bezierCurveTo","bgColor","bgProperties","bias","big","binaryType","bind","bindAttribLocation","bindBuffer","bindFramebuffer","bindRenderbuffer","bindTexture","blendColor","blendEquation","blendEquationSeparate","blendFunc","blendFuncSeparate","blink","blob","blockDirection","blue","blur","body","bodyUsed","bold","bookmarks","booleanValue","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","borderBottom","borderBottomColor","borderBottomLeftRadius","borderBottomRightRadius","borderBottomStyle","borderBottomWidth","borderCollapse","borderColor","borderColorDark","borderColorLight","borderImage","borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth","borderLeft","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRadius","borderRight","borderRightColor","borderRightStyle","borderRightWidth","borderSpacing","borderStyle","borderTop","borderTopColor","borderTopLeftRadius","borderTopRightRadius","borderTopStyle","borderTopWidth","borderWidth","bottom","bottomMargin","bound","boundElements","boundingClientRect","boundingHeight","boundingLeft","boundingTop","boundingWidth","bounds","box-decoration-break","box-shadow","box-sizing","boxDecorationBreak","boxShadow","boxSizing","breakAfter","breakBefore","breakInside","browserLanguage","btoa","bubbles","buffer","bufferData","bufferDepth","bufferSize","bufferSubData","buffered","bufferedAmount","buildID","buildNumber","button","buttonID","buttons","byteLength","byteOffset","c","call","caller","canBeFormatted","canBeMounted","canBeShared","canHaveChildren","canHaveHTML","canPlayType","cancel","cancelAnimationFrame","cancelBubble","cancelScheduledValues","cancelable","candidate","canvas","caption","caption-side","captionSide","capture","captureEvents","captureStackTrace","caretPositionFromPoint","caretRangeFromPoint","cast","catch","category","cbrt","cd","ceil","cellIndex","cellPadding","cellSpacing","cells","ch","chOff","chain","challenge","changedTouches","channel","channelCount","channelCountMode","channelInterpretation","char","charAt","charCode","charCodeAt","charIndex","characterSet","characterData","characterDataOldValue","charging","chargingTime","charset","checkEnclosure","checkFramebufferStatus","checkIntersection","checkValidity","checked","childElementCount","childList","childNodes","children","chrome","ciphertext","cite","classList","className","classid","clear","clearAttributes","clearColor","clearData","clearDepth","clearImmediate","clearInterval","clearMarks","clearMeasures","clearParameters","clearRect","clearResourceTimings","clearShadow","clearStencil","clearTimeout","clearWatch","click","clickCount","clientHeight","clientInformation","clientLeft","clientRect","clientRects","clientTop","clientWidth","clientX","clientY","clip","clip-path","clip-rule","clipBottom","clipLeft","clipPath","clipPathUnits","clipRight","clipRule","clipTop","clipboardData","clone","cloneContents","cloneNode","cloneRange","close","closePath","closed","closest","clz","clz32","cmp","code","codeBase","codePointAt","codeType","colSpan","collapse","collapseToEnd","collapseToStart","collapsed","collect","colno","color","color-interpolation","color-interpolation-filters","colorDepth","colorInterpolation","colorInterpolationFilters","colorMask","colorType","cols","columnCount","columnFill","columnGap","columnNumber","columnRule","columnRuleColor","columnRuleStyle","columnRuleWidth","columnSpan","columnWidth","columns","command","commitPreferences","commonAncestorContainer","compact","compareBoundaryPoints","compareDocumentPosition","compareEndPoints","compareNode","comparePoint","compatMode","compatible","compile","compileShader","complete","componentFromPoint","compositionEndOffset","compositionStartOffset","compressedTexImage2D","compressedTexSubImage2D","concat","conditionText","coneInnerAngle","coneOuterAngle","coneOuterGain","confirm","confirmComposition","confirmSiteSpecificTrackingException","confirmWebWideTrackingException","connect","connectEnd","connectStart","connected","connection","connectionSpeed","console","consolidate","constrictionActive","constructor","contactID","contains","containsNode","content","contentDocument","contentEditable","contentOverflow","contentScriptType","contentStyleType","contentType","contentWindow","context","contextMenu","contextmenu","continue","continuous","control","controller","controls","convertToSpecifiedUnits","cookie","cookieEnabled","coords","copyFromChannel","copyTexImage2D","copyTexSubImage2D","copyToChannel","copyWithin","correspondingElement","correspondingUseElement","cos","cosh","count","counter-increment","counter-reset","counterIncrement","counterReset","cpuClass","cpuSleepAllowed","create","createAnalyser","createAnswer","createAttribute","createAttributeNS","createBiquadFilter","createBuffer","createBufferSource","createCDATASection","createCSSStyleSheet","createCaption","createChannelMerger","createChannelSplitter","createComment","createContextualFragment","createControlRange","createConvolver","createDTMFSender","createDataChannel","createDelay","createDelayNode","createDocument","createDocumentFragment","createDocumentType","createDynamicsCompressor","createElement","createElementNS","createEntityReference","createEvent","createEventObject","createExpression","createFramebuffer","createFunction","createGain","createGainNode","createHTMLDocument","createImageBitmap","createImageData","createIndex","createJavaScriptNode","createLinearGradient","createMediaElementSource","createMediaKeys","createMediaStreamDestination","createMediaStreamSource","createMutableFile","createNSResolver","createNodeIterator","createNotification","createObjectStore","createObjectURL","createOffer","createOscillator","createPanner","createPattern","createPeriodicWave","createPopup","createProcessingInstruction","createProgram","createRadialGradient","createRange","createRangeCollection","createRenderbuffer","createSVGAngle","createSVGLength","createSVGMatrix","createSVGNumber","createSVGPathSegArcAbs","createSVGPathSegArcRel","createSVGPathSegClosePath","createSVGPathSegCurvetoCubicAbs","createSVGPathSegCurvetoCubicRel","createSVGPathSegCurvetoCubicSmoothAbs","createSVGPathSegCurvetoCubicSmoothRel","createSVGPathSegCurvetoQuadraticAbs","createSVGPathSegCurvetoQuadraticRel","createSVGPathSegCurvetoQuadraticSmoothAbs","createSVGPathSegCurvetoQuadraticSmoothRel","createSVGPathSegLinetoAbs","createSVGPathSegLinetoHorizontalAbs","createSVGPathSegLinetoHorizontalRel","createSVGPathSegLinetoRel","createSVGPathSegLinetoVerticalAbs","createSVGPathSegLinetoVerticalRel","createSVGPathSegMovetoAbs","createSVGPathSegMovetoRel","createSVGPoint","createSVGRect","createSVGTransform","createSVGTransformFromMatrix","createScriptProcessor","createSession","createShader","createShadowRoot","createStereoPanner","createStyleSheet","createTBody","createTFoot","createTHead","createTextNode","createTextRange","createTexture","createTouch","createTouchList","createTreeWalker","createWaveShaper","creationTime","crossOrigin","crypto","csi","cssFloat","cssRules","cssText","cssValueType","ctrlKey","ctrlLeft","cues","cullFace","currentNode","currentPage","currentScale","currentScript","currentSrc","currentState","currentStyle","currentTarget","currentTime","currentTranslate","currentView","cursor","curve","customError","cx","cy","d","data","dataFld","dataFormatAs","dataPageSize","dataSrc","dataTransfer","database","dataset","dateTime","db","debug","debuggerEnabled","declare","decode","decodeAudioData","decodingInfo","decodeURI","decodeURIComponent","decrypt","default","defaultCharset","defaultChecked","defaultMuted","defaultPlaybackRate","defaultPrevented","defaultSelected","defaultStatus","defaultURL","defaultValue","defaultView","defaultstatus","defer","defineMagicFunction","defineMagicVariable","defineProperties","defineProperty","delayTime","delete","deleteBuffer","deleteCaption","deleteCell","deleteContents","deleteData","deleteDatabase","deleteFramebuffer","deleteFromDocument","deleteIndex","deleteMedium","deleteObjectStore","deleteProgram","deleteRenderbuffer","deleteRow","deleteRule","deleteShader","deleteTFoot","deleteTHead","deleteTexture","deliverChangeRecords","delivery","deliveryInfo","deliveryStatus","deliveryTimestamp","delta","deltaMode","deltaX","deltaY","deltaZ","depthFunc","depthMask","depthRange","deriveBits","deriveKey","description","deselectAll","designMode","destination","destinationURL","detach","detachEvent","detachShader","detail","detune","devicePixelRatio","deviceXDPI","deviceYDPI","diffuseConstant","digest","dimensions","dir","dirName","direction","dirxml","disable","disableVertexAttribArray","disabled","dischargingTime","disconnect","dispatchEvent","display","distanceModel","divisor","djsapi","djsproxy","doImport","doNotTrack","doScroll","doctype","document","documentElement","documentMode","documentURI","dolphin","dolphinGameCenter","dolphininfo","dolphinmeta","domComplete","domContentLoadedEventEnd","domContentLoadedEventStart","domInteractive","domLoading","domain","domainLookupEnd","domainLookupStart","dominant-baseline","dominantBaseline","done","dopplerFactor","download","dragDrop","draggable","drawArrays","drawArraysInstancedANGLE","drawCustomFocusRing","drawElements","drawElementsInstancedANGLE","drawFocusIfNeeded","drawImage","drawImageFromRect","drawSystemFocusRing","drawingBufferHeight","drawingBufferWidth","dropEffect","droppedVideoFrames","dropzone","dump","duplicate","duration","dvname","dvnum","dx","dy","dynsrc","e","edgeMode","effectAllowed","elapsedTime","elementFromPoint","elements","elevation","ellipse","email","embeds","empty","empty-cells","emptyCells","enable","enableBackground","enableStyleSheetsForSet","enableVertexAttribArray","enabled","enabledPlugin","encode","encodeURI","encodeURIComponent","encoding","encrypt","enctype","end","endContainer","endElement","endElementAt","endOfStream","endOffset","endTime","ended","endsWith","entities","entries","entryType","enumerate","enumerateEditable","error","errorCode","escape","eval","evaluate","event","eventPhase","every","exception","exec","execCommand","execCommandShowHelp","execScript","exitFullscreen","exitPointerLock","exp","expand","expandEntityReferences","expando","expansion","expiryDate","explicitOriginalTarget","expm1","exponent","exponentialRampToValueAtTime","exportKey","extend","extensions","extentNode","extentOffset","external","externalResourcesRequired","extractContents","extractable","f","face","factoryReset","fallback","familyName","farthestViewportElement","fastSeek","fatal","fetch","fetchStart","fftSize","fgColor","fileCreatedDate","fileHandle","fileModifiedDate","fileName","fileSize","fileUpdatedDate","filename","files","fill","fill-opacity","fill-rule","fillOpacity","fillRect","fillRule","fillStyle","fillText","filter","filterResX","filterResY","filterUnits","filters","finally","find","findIndex","findRule","findText","finish","fireEvent","firstChild","firstElementChild","firstPage","fixed","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","flexBasis","flexDirection","flexFlow","flexGrow","flexShrink","flexWrap","flipX","flipY","float","flood-color","flood-opacity","floodColor","floodOpacity","floor","flush","focus","focusNode","focusOffset","font","font-family","font-feature-settings","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","fontFamily","fontFeatureSettings","fontKerning","fontLanguageOverride","fontSize","fontSizeAdjust","fontSmoothingEnabled","fontStretch","fontStyle","fontSynthesis","fontVariant","fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition","fontWeight","fontcolor","fonts","fontsize","for","forEach","forceRedraw","form","formAction","formEnctype","formMethod","formNoValidate","formTarget","format","formatToParts","forms","forward","fr","frame","frameBorder","frameElement","frameSpacing","framebufferRenderbuffer","framebufferTexture2D","frames","freeSpace","freeze","frequency","frequencyBinCount","from","fromCharCode","fromCodePoint","fromElement","frontFace","fround","fullScreen","fullscreenElement","fullscreenEnabled","fx","fy","gain","gamepad","gamma","genderIdentity","generateKey","generateMipmap","generateRequest","geolocation","gestureObject","get","getActiveAttrib","getActiveUniform","getAdjacentText","getAll","getAllResponseHeaders","getAsFile","getAsString","getAttachedShaders","getAttribLocation","getAttribute","getAttributeNS","getAttributeNode","getAttributeNodeNS","getAudioTracks","getBBox","getBattery","getBlob","getBookmark","getBoundingClientRect","getBufferParameter","getByteFrequencyData","getByteTimeDomainData","getCSSCanvasContext","getCTM","getCandidateWindowClientRect","getChannelData","getCharNumAtPosition","getClientRect","getClientRects","getCompositionAlternatives","getComputedStyle","getComputedTextLength","getConfiguration","getContext","getContextAttributes","getCounterValue","getCueAsHTML","getCueById","getCurrentPosition","getCurrentTime","getData","getDatabaseNames","getDate","getDay","getDefaultComputedStyle","getDestinationInsertionPoints","getDistributedNodes","getEditable","getElementById","getElementsByClassName","getElementsByName","getElementsByTagName","getElementsByTagNameNS","getEnclosureList","getEndPositionOfChar","getEntries","getEntriesByName","getEntriesByType","getError","getExtension","getExtentOfChar","getFeature","getFile","getFloat32","getFloat64","getFloatFrequencyData","getFloatTimeDomainData","getFloatValue","getFramebufferAttachmentParameter","getFrequencyResponse","getFullYear","getGamepads","getHours","getImageData","getInt16","getInt32","getInt8","getIntersectionList","getItem","getItems","getKey","getLineDash","getLocalStreams","getMarks","getMatchedCSSRules","getMeasures","getMetadata","getMilliseconds","getMinutes","getModifierState","getMonth","getNamedItem","getNamedItemNS","getNotifier","getNumberOfChars","getOverrideHistoryNavigationMode","getOverrideStyle","getOwnPropertyDescriptor","getOwnPropertyNames","getOwnPropertySymbols","getParameter","getPathSegAtLength","getPointAtLength","getPreference","getPreferenceDefault","getPresentationAttribute","getPreventDefault","getProgramInfoLog","getProgramParameter","getPropertyCSSValue","getPropertyPriority","getPropertyShorthand","getPropertyValue","getPrototypeOf","getRGBColorValue","getRandomValues","getRangeAt","getReceivers","getRectValue","getRegistration","getRemoteStreams","getRenderbufferParameter","getResponseHeader","getRoot","getRotationOfChar","getSVGDocument","getScreenCTM","getSeconds","getSelection","getSenders","getShaderInfoLog","getShaderParameter","getShaderPrecisionFormat","getShaderSource","getSimpleDuration","getSiteIcons","getSources","getSpeculativeParserUrls","getStartPositionOfChar","getStartTime","getStats","getStorageUpdates","getStreamById","getStringValue","getSubStringLength","getSubscription","getSupportedExtensions","getTexParameter","getTime","getTimezoneOffset","getTotalLength","getTrackById","getTracks","getTransformToElement","getUTCDate","getUTCDay","getUTCFullYear","getUTCHours","getUTCMilliseconds","getUTCMinutes","getUTCMonth","getUTCSeconds","getUint16","getUint32","getUint8","getUniform","getUniformLocation","getUserMedia","getValues","getVarDate","getVariableValue","getVertexAttrib","getVertexAttribOffset","getVideoPlaybackQuality","getVideoTracks","getWakeLockState","getYear","givenName","global","globalAlpha","globalCompositeOperation","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","go","gradientTransform","gradientUnits","grammars","green","group","groupCollapsed","groupEnd","hardwareConcurrency","has","hasAttribute","hasAttributeNS","hasAttributes","hasChildNodes","hasComposition","hasExtension","hasFeature","hasFocus","hasLayout","hasOwnProperty","hash","head","headers","heading","height","hidden","hide","hideFocus","high","hint","history","honorificPrefix","honorificSuffix","horizontalOverflow","host","hostname","href","hreflang","hspace","html5TagCheckInerface","htmlFor","htmlText","httpEquiv","hwTimestamp","hypot","iccId","iceConnectionState","iceGatheringState","icon","id","identifier","identity","ignoreBOM","ignoreCase","image-orientation","image-rendering","imageOrientation","imageRendering","images","ime-mode","imeMode","implementation","importKey","importNode","importStylesheet","imports","impp","imul","in1","in2","inBandMetadataTrackDispatchType","inRange","includes","incremental","indeterminate","index","indexNames","indexOf","indexedDB","inertiaDestinationX","inertiaDestinationY","info","init","initAnimationEvent","initBeforeLoadEvent","initClipboardEvent","initCloseEvent","initCommandEvent","initCompositionEvent","initCustomEvent","initData","initDeviceMotionEvent","initDeviceOrientationEvent","initDragEvent","initErrorEvent","initEvent","initFocusEvent","initGestureEvent","initHashChangeEvent","initKeyEvent","initKeyboardEvent","initMSManipulationEvent","initMessageEvent","initMouseEvent","initMouseScrollEvent","initMouseWheelEvent","initMutationEvent","initNSMouseEvent","initOverflowEvent","initPageEvent","initPageTransitionEvent","initPointerEvent","initPopStateEvent","initProgressEvent","initScrollAreaEvent","initSimpleGestureEvent","initStorageEvent","initTextEvent","initTimeEvent","initTouchEvent","initTransitionEvent","initUIEvent","initWebKitAnimationEvent","initWebKitTransitionEvent","initWebKitWheelEvent","initWheelEvent","initialTime","initialize","initiatorType","inner","innerHTML","innerHeight","innerText","innerWidth","input","inputBuffer","inputEncoding","inputMethod","insertAdjacentElement","insertAdjacentHTML","insertAdjacentText","insertBefore","insertCell","insertData","insertItemBefore","insertNode","insertRow","insertRule","instanceRoot","intercept","interimResults","internalSubset","intersectsNode","interval","invalidIteratorState","inverse","invertSelf","is","is2D","isAlternate","isArray","isBingCurrentSearchDefault","isBuffer","isCandidateWindowVisible","isChar","isCollapsed","isComposing","isContentEditable","isContentHandlerRegistered","isContextLost","isDefaultNamespace","isDisabled","isEnabled","isEqual","isEqualNode","isExtensible","isFinite","isFramebuffer","isFrozen","isGenerator","isId","isInjected","isInteger","isMap","isMultiLine","isNaN","isOpen","isPointInFill","isPointInPath","isPointInRange","isPointInStroke","isPrefAlternate","isPrimary","isProgram","isPropertyImplicit","isProtocolHandlerRegistered","isPrototypeOf","isRenderbuffer","isSafeInteger","isSameNode","isSealed","isShader","isSupported","isTextEdit","isTexture","isTrusted","isTypeSupported","isView","isolation","italics","item","itemId","itemProp","itemRef","itemScope","itemType","itemValue","iterateNext","iterator","javaEnabled","jobTitle","join","json","justify-content","justifyContent","k1","k2","k3","k4","kernelMatrix","kernelUnitLengthX","kernelUnitLengthY","kerning","key","keyCode","keyFor","keyIdentifier","keyLightEnabled","keyLocation","keyPath","keySystem","keyText","keyUsage","keys","keytype","kind","knee","label","labels","lang","language","languages","largeArcFlag","lastChild","lastElementChild","lastEventId","lastIndex","lastIndexOf","lastMatch","lastMessageSubject","lastMessageType","lastModified","lastModifiedDate","lastPage","lastParen","lastState","lastStyleSheetSet","latitude","layerX","layerY","layoutFlow","layoutGrid","layoutGridChar","layoutGridLine","layoutGridMode","layoutGridType","lbound","left","leftContext","leftMargin","length","lengthAdjust","lengthComputable","letter-spacing","letterSpacing","level","lighting-color","lightingColor","limitingConeAngle","line","line-height","lineAlign","lineBreak","lineCap","lineDashOffset","lineHeight","lineJoin","lineNumber","lineTo","lineWidth","linearRampToValueAtTime","lineno","link","linkColor","linkProgram","links","list","list-style","list-style-image","list-style-position","list-style-type","listStyle","listStyleImage","listStylePosition","listStyleType","listener","load","loadEventEnd","loadEventStart","loadTimes","loaded","localDescription","localName","localStorage","locale","localeCompare","location","locationbar","lock","lockedFile","log","log10","log1p","log2","logicalXDPI","logicalYDPI","longDesc","longitude","lookupNamespaceURI","lookupPrefix","loop","loopEnd","loopStart","looping","low","lower","lowerBound","lowerOpen","lowsrc","m11","m12","m13","m14","m21","m22","m23","m24","m31","m32","m33","m34","m41","m42","m43","m44","manifest","map","mapping","margin","margin-bottom","margin-left","margin-right","margin-top","marginBottom","marginHeight","marginLeft","marginRight","marginTop","marginWidth","mark","marker","marker-end","marker-mid","marker-offset","marker-start","markerEnd","markerHeight","markerMid","markerOffset","markerStart","markerUnits","markerWidth","marks","mask","mask-type","maskContentUnits","maskType","maskUnits","match","matchMedia","matchMedium","matches","matrix","matrixTransform","max","max-height","max-width","maxAlternatives","maxChannelCount","maxConnectionsPerServer","maxDecibels","maxDistance","maxHeight","maxLength","maxTouchPoints","maxValue","maxWidth","measure","measureText","media","mediaCapabilities","mediaDevices","mediaElement","mediaGroup","mediaKeys","mediaText","meetOrSlice","memory","menubar","mergeAttributes","message","messageClass","messageHandlers","metaKey","method","mimeType","mimeTypes","min","min-height","min-width","minDecibels","minHeight","minValue","minWidth","miterLimit","mix-blend-mode","mixBlendMode","mode","modify","mount","move","moveBy","moveEnd","moveFirst","moveFocusDown","moveFocusLeft","moveFocusRight","moveFocusUp","moveNext","moveRow","moveStart","moveTo","moveToBookmark","moveToElementText","moveToPoint","mozAdd","mozAnimationStartTime","mozAnon","mozApps","mozAudioCaptured","mozAudioChannelType","mozAutoplayEnabled","mozCancelAnimationFrame","mozCancelFullScreen","mozCancelRequestAnimationFrame","mozCaptureStream","mozCaptureStreamUntilEnded","mozClearDataAt","mozContact","mozContacts","mozCreateFileHandle","mozCurrentTransform","mozCurrentTransformInverse","mozCursor","mozDash","mozDashOffset","mozDecodedFrames","mozExitPointerLock","mozFillRule","mozFragmentEnd","mozFrameDelay","mozFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozGetAll","mozGetAllKeys","mozGetAsFile","mozGetDataAt","mozGetMetadata","mozGetUserMedia","mozHasAudio","mozHasItem","mozHidden","mozImageSmoothingEnabled","mozIndexedDB","mozInnerScreenX","mozInnerScreenY","mozInputSource","mozIsTextField","mozItem","mozItemCount","mozItems","mozLength","mozLockOrientation","mozMatchesSelector","mozMovementX","mozMovementY","mozOpaque","mozOrientation","mozPaintCount","mozPaintedFrames","mozParsedFrames","mozPay","mozPointerLockElement","mozPresentedFrames","mozPreservesPitch","mozPressure","mozPrintCallback","mozRTCIceCandidate","mozRTCPeerConnection","mozRTCSessionDescription","mozRemove","mozRequestAnimationFrame","mozRequestFullScreen","mozRequestPointerLock","mozSetDataAt","mozSetImageElement","mozSourceNode","mozSrcObject","mozSystem","mozTCPSocket","mozTextStyle","mozTypesAt","mozUnlockOrientation","mozUserCancelled","mozVisibilityState","msAnimation","msAnimationDelay","msAnimationDirection","msAnimationDuration","msAnimationFillMode","msAnimationIterationCount","msAnimationName","msAnimationPlayState","msAnimationStartTime","msAnimationTimingFunction","msBackfaceVisibility","msBlockProgression","msCSSOMElementFloatMetrics","msCaching","msCachingEnabled","msCancelRequestAnimationFrame","msCapsLockWarningOff","msClearImmediate","msClose","msContentZoomChaining","msContentZoomFactor","msContentZoomLimit","msContentZoomLimitMax","msContentZoomLimitMin","msContentZoomSnap","msContentZoomSnapPoints","msContentZoomSnapType","msContentZooming","msConvertURL","msCrypto","msDoNotTrack","msElementsFromPoint","msElementsFromRect","msExitFullscreen","msExtendedCode","msFillRule","msFirstPaint","msFlex","msFlexAlign","msFlexDirection","msFlexFlow","msFlexItemAlign","msFlexLinePack","msFlexNegative","msFlexOrder","msFlexPack","msFlexPositive","msFlexPreferredSize","msFlexWrap","msFlowFrom","msFlowInto","msFontFeatureSettings","msFullscreenElement","msFullscreenEnabled","msGetInputContext","msGetRegionContent","msGetUntransformedBounds","msGraphicsTrustStatus","msGridColumn","msGridColumnAlign","msGridColumnSpan","msGridColumns","msGridRow","msGridRowAlign","msGridRowSpan","msGridRows","msHidden","msHighContrastAdjust","msHyphenateLimitChars","msHyphenateLimitLines","msHyphenateLimitZone","msHyphens","msImageSmoothingEnabled","msImeAlign","msIndexedDB","msInterpolationMode","msIsStaticHTML","msKeySystem","msKeys","msLaunchUri","msLockOrientation","msManipulationViewsEnabled","msMatchMedia","msMatchesSelector","msMaxTouchPoints","msOrientation","msOverflowStyle","msPerspective","msPerspectiveOrigin","msPlayToDisabled","msPlayToPreferredSourceUri","msPlayToPrimary","msPointerEnabled","msRegionOverflow","msReleasePointerCapture","msRequestAnimationFrame","msRequestFullscreen","msSaveBlob","msSaveOrOpenBlob","msScrollChaining","msScrollLimit","msScrollLimitXMax","msScrollLimitXMin","msScrollLimitYMax","msScrollLimitYMin","msScrollRails","msScrollSnapPointsX","msScrollSnapPointsY","msScrollSnapType","msScrollSnapX","msScrollSnapY","msScrollTranslation","msSetImmediate","msSetMediaKeys","msSetPointerCapture","msTextCombineHorizontal","msTextSizeAdjust","msToBlob","msTouchAction","msTouchSelect","msTraceAsyncCallbackCompleted","msTraceAsyncCallbackStarting","msTraceAsyncOperationCompleted","msTraceAsyncOperationStarting","msTransform","msTransformOrigin","msTransformStyle","msTransition","msTransitionDelay","msTransitionDuration","msTransitionProperty","msTransitionTimingFunction","msUnlockOrientation","msUpdateAsyncCallbackRelation","msUserSelect","msVisibilityState","msWrapFlow","msWrapMargin","msWrapThrough","msWriteProfilerMark","msZoom","msZoomTo","mt","multiEntry","multiSelectionObj","multiline","multiple","multiply","multiplySelf","mutableFile","muted","n","name","nameProp","namedItem","namedRecordset","names","namespaceURI","namespaces","naturalHeight","naturalWidth","navigate","navigation","navigationMode","navigationStart","navigator","near","nearestViewportElement","negative","netscape","networkState","newScale","newTranslate","newURL","newValue","newValueSpecifiedUnits","newVersion","newhome","next","nextElementSibling","nextNode","nextPage","nextSibling","nickname","noHref","noResize","noShade","noValidate","noWrap","nodeName","nodeType","nodeValue","normalize","normalizedPathSegList","notationName","notations","note","noteGrainOn","noteOff","noteOn","now","numOctaves","number","numberOfChannels","numberOfInputs","numberOfItems","numberOfOutputs","numberValue","oMatchesSelector","object","object-fit","object-position","objectFit","objectPosition","objectStore","objectStoreNames","observe","of","offscreenBuffering","offset","offsetHeight","offsetLeft","offsetNode","offsetParent","offsetTop","offsetWidth","offsetX","offsetY","ok","oldURL","oldValue","oldVersion","olderShadowRoot","onLine","onabort","onactivate","onactive","onaddstream","onaddtrack","onafterprint","onafterscriptexecute","onafterupdate","onaudioend","onaudioprocess","onaudiostart","onautocomplete","onautocompleteerror","onbeforeactivate","onbeforecopy","onbeforecut","onbeforedeactivate","onbeforeeditfocus","onbeforepaste","onbeforeprint","onbeforescriptexecute","onbeforeunload","onbeforeupdate","onblocked","onblur","onbounce","onboundary","oncached","oncancel","oncandidatewindowhide","oncandidatewindowshow","oncandidatewindowupdate","oncanplay","oncanplaythrough","once","oncellchange","onchange","onchargingchange","onchargingtimechange","onchecking","onclick","onclose","oncompassneedscalibration","oncomplete","oncontextmenu","oncontrolselect","oncopy","oncuechange","oncut","ondataavailable","ondatachannel","ondatasetchanged","ondatasetcomplete","ondblclick","ondeactivate","ondevicelight","ondevicemotion","ondeviceorientation","ondeviceproximity","ondischargingtimechange","ondisplay","ondownloading","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onencrypted","onend","onended","onenter","onerror","onerrorupdate","onexit","onfilterchange","onfinish","onfocus","onfocusin","onfocusout","onfullscreenchange","onfullscreenerror","ongesturechange","ongestureend","ongesturestart","ongotpointercapture","onhashchange","onhelp","onicecandidate","oniceconnectionstatechange","oninactive","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onlanguagechange","onlayoutcomplete","onlevelchange","onload","onloadeddata","onloadedmetadata","onloadend","onloadstart","onlosecapture","onlostpointercapture","only","onmark","onmessage","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onmove","onmoveend","onmovestart","onmozfullscreenchange","onmozfullscreenerror","onmozorientationchange","onmozpointerlockchange","onmozpointerlockerror","onmscontentzoom","onmsfullscreenchange","onmsfullscreenerror","onmsgesturechange","onmsgesturedoubletap","onmsgestureend","onmsgesturehold","onmsgesturestart","onmsgesturetap","onmsgotpointercapture","onmsinertiastart","onmslostpointercapture","onmsmanipulationstatechanged","onmsneedkey","onmsorientationchange","onmspointercancel","onmspointerdown","onmspointerenter","onmspointerhover","onmspointerleave","onmspointermove","onmspointerout","onmspointerover","onmspointerup","onmssitemodejumplistitemremoved","onmsthumbnailclick","onnegotiationneeded","onnomatch","onnoupdate","onobsolete","onoffline","ononline","onopen","onorientationchange","onpagechange","onpagehide","onpageshow","onpaste","onpause","onplay","onplaying","onpluginstreamstart","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointerlockchange","onpointerlockerror","onpointermove","onpointerout","onpointerover","onpointerup","onpopstate","onprogress","onpropertychange","onratechange","onreadystatechange","onremovestream","onremovetrack","onreset","onresize","onresizeend","onresizestart","onresourcetimingbufferfull","onresult","onresume","onrowenter","onrowexit","onrowsdelete","onrowsinserted","onscroll","onsearch","onseeked","onseeking","onselect","onselectionchange","onselectstart","onshow","onsignalingstatechange","onsoundend","onsoundstart","onspeechend","onspeechstart","onstalled","onstart","onstatechange","onstop","onstorage","onstoragecommit","onsubmit","onsuccess","onsuspend","ontextinput","ontimeout","ontimeupdate","ontoggle","ontouchcancel","ontouchend","ontouchmove","ontouchstart","ontransitionend","onunload","onupdateready","onupgradeneeded","onuserproximity","onversionchange","onvoiceschanged","onvolumechange","onwaiting","onwarning","onwebkitanimationend","onwebkitanimationiteration","onwebkitanimationstart","onwebkitcurrentplaybacktargetiswirelesschanged","onwebkitfullscreenchange","onwebkitfullscreenerror","onwebkitkeyadded","onwebkitkeyerror","onwebkitkeymessage","onwebkitneedkey","onwebkitorientationchange","onwebkitplaybacktargetavailabilitychanged","onwebkitpointerlockchange","onwebkitpointerlockerror","onwebkitresourcetimingbufferfull","onwebkittransitionend","onwheel","onzoom","opacity","open","openCursor","openDatabase","openKeyCursor","opener","opera","operationType","operator","opr","optimum","options","order","orderX","orderY","ordered","org","orient","orientAngle","orientType","orientation","origin","originalTarget","orphans","oscpu","outerHTML","outerHeight","outerText","outerWidth","outline","outline-color","outline-offset","outline-style","outline-width","outlineColor","outlineOffset","outlineStyle","outlineWidth","outputBuffer","overflow","overflow-x","overflow-y","overflowX","overflowY","overrideMimeType","oversample","ownerDocument","ownerElement","ownerNode","ownerRule","ownerSVGElement","owningElement","p1","p2","p3","p4","pad","padding","padding-bottom","padding-left","padding-right","padding-top","paddingBottom","paddingLeft","paddingRight","paddingTop","page","page-break-after","page-break-before","page-break-inside","pageBreakAfter","pageBreakBefore","pageBreakInside","pageCount","pageX","pageXOffset","pageY","pageYOffset","pages","paint-order","paintOrder","paintRequests","paintType","palette","panningModel","parent","parentElement","parentNode","parentRule","parentStyleSheet","parentTextEdit","parentWindow","parse","parseFloat","parseFromString","parseInt","participants","passive","password","pasteHTML","path","pathLength","pathSegList","pathSegType","pathSegTypeAsLetter","pathname","pattern","patternContentUnits","patternMismatch","patternTransform","patternUnits","pause","pauseAnimations","pauseOnExit","paused","pending","performance","permission","persisted","personalbar","perspective","perspective-origin","perspectiveOrigin","phoneticFamilyName","phoneticGivenName","photo","ping","pitch","pixelBottom","pixelDepth","pixelHeight","pixelLeft","pixelRight","pixelStorei","pixelTop","pixelUnitToMillimeterX","pixelUnitToMillimeterY","pixelWidth","placeholder","platform","play","playbackRate","playbackState","playbackTime","played","plugins","pluginspage","pname","pointer-events","pointerBeforeReferenceNode","pointerEnabled","pointerEvents","pointerId","pointerLockElement","pointerType","points","pointsAtX","pointsAtY","pointsAtZ","polygonOffset","pop","popupWindowFeatures","popupWindowName","popupWindowURI","port","port1","port2","ports","posBottom","posHeight","posLeft","posRight","posTop","posWidth","position","positionAlign","postError","postMessage","poster","pow","powerOff","preMultiplySelf","precision","preferredStyleSheetSet","preferredStylesheetSet","prefix","preload","prepend","preserveAlpha","preserveAspectRatio","preserveAspectRatioString","pressed","pressure","prevValue","preventDefault","preventExtensions","previousElementSibling","previousNode","previousPage","previousScale","previousSibling","previousTranslate","primaryKey","primitiveType","primitiveUnits","principals","print","privateKey","probablySupportsContext","process","processIceMessage","product","productSub","profile","profileEnd","profiles","prompt","properties","propertyIsEnumerable","propertyName","protocol","protocolLong","prototype","pseudoClass","pseudoElement","publicId","publicKey","published","push","pushNotification","pushState","put","putImageData","quadraticCurveTo","qualifier","queryCommandEnabled","queryCommandIndeterm","queryCommandState","queryCommandSupported","queryCommandText","queryCommandValue","querySelector","querySelectorAll","quote","quotes","r","r1","r2","race","radiogroup","radiusX","radiusY","random","range","rangeCount","rangeMax","rangeMin","rangeOffset","rangeOverflow","rangeParent","rangeUnderflow","rate","ratio","raw","read","readAsArrayBuffer","readAsBinaryString","readAsBlob","readAsDataURL","readAsText","readOnly","readPixels","readReportRequested","readyState","reason","reboot","receiver","receivers","recordNumber","recordset","rect","red","redirectCount","redirectEnd","redirectStart","reduce","reduceRight","reduction","refDistance","refX","refY","referenceNode","referrer","refresh","region","regionAnchorX","regionAnchorY","regionId","regions","register","registerContentHandler","registerElement","registerProtocolHandler","reject","rel","relList","relatedNode","relatedTarget","release","releaseCapture","releaseEvents","releasePointerCapture","releaseShaderCompiler","reliable","reload","remainingSpace","remoteDescription","remove","removeAllRanges","removeAttribute","removeAttributeNS","removeAttributeNode","removeBehavior","removeChild","removeCue","removeEventListener","removeFilter","removeImport","removeItem","removeListener","removeNamedItem","removeNamedItemNS","removeNode","removeParameter","removeProperty","removeRange","removeRegion","removeRule","removeSiteSpecificTrackingException","removeSourceBuffer","removeStream","removeTrack","removeVariable","removeWakeLockListener","removeWebWideTrackingException","removedNodes","renderbufferStorage","renderedBuffer","renderingMode","repeat","replace","replaceAdjacentText","replaceChild","replaceData","replaceId","replaceItem","replaceNode","replaceState","replaceTrack","replaceWholeText","reportValidity","requestAnimationFrame","requestAutocomplete","requestData","requestFullscreen","requestMediaKeySystemAccess","requestPermission","requestPointerLock","requestStart","requestingWindow","required","requiredExtensions","requiredFeatures","reset","resetTransform","resize","resizeBy","resizeTo","resolve","response","responseBody","responseEnd","responseStart","responseText","responseType","responseURL","responseXML","restore","result","resultType","resume","returnValue","rev","reverse","reversed","revocable","revokeObjectURL","rgbColor","right","rightContext","rightMargin","rolloffFactor","root","rootElement","rotate","rotateAxisAngle","rotateAxisAngleSelf","rotateFromVector","rotateFromVectorSelf","rotateSelf","rotation","rotationRate","round","rowIndex","rowSpan","rows","rubyAlign","rubyOverhang","rubyPosition","rules","runtime","runtimeStyle","rx","ry","safari","sampleCoverage","sampleRate","sandbox","save","scale","scale3d","scale3dSelf","scaleNonUniform","scaleNonUniformSelf","scaleSelf","scheme","scissor","scope","scopeName","scoped","screen","screenBrightness","screenEnabled","screenLeft","screenPixelToMillimeterX","screenPixelToMillimeterY","screenTop","screenX","screenY","scripts","scroll","scroll-behavior","scrollAmount","scrollBehavior","scrollBy","scrollByLines","scrollByPages","scrollDelay","scrollHeight","scrollIntoView","scrollIntoViewIfNeeded","scrollLeft","scrollLeftMax","scrollMaxX","scrollMaxY","scrollTo","scrollTop","scrollTopMax","scrollWidth","scrollX","scrollY","scrollbar3dLightColor","scrollbarArrowColor","scrollbarBaseColor","scrollbarDarkShadowColor","scrollbarFaceColor","scrollbarHighlightColor","scrollbarShadowColor","scrollbarTrackColor","scrollbars","scrolling","sdp","sdpMLineIndex","sdpMid","seal","search","searchBox","searchBoxJavaBridge_","searchParams","sectionRowIndex","secureConnectionStart","security","seed","seekable","seeking","select","selectAllChildren","selectNode","selectNodeContents","selectNodes","selectSingleNode","selectSubString","selected","selectedIndex","selectedOptions","selectedStyleSheetSet","selectedStylesheetSet","selection","selectionDirection","selectionEnd","selectionStart","selector","selectorText","self","send","sendAsBinary","sendBeacon","sender","sentTimestamp","separator","serializeToString","serviceWorker","sessionId","sessionStorage","set","setActive","setAlpha","setAttribute","setAttributeNS","setAttributeNode","setAttributeNodeNS","setBaseAndExtent","setBingCurrentSearchDefault","setCapture","setColor","setCompositeOperation","setCurrentTime","setCustomValidity","setData","setDate","setDragImage","setEnd","setEndAfter","setEndBefore","setEndPoint","setFillColor","setFilterRes","setFloat32","setFloat64","setFloatValue","setFullYear","setHours","setImmediate","setInt16","setInt32","setInt8","setInterval","setItem","setLineCap","setLineDash","setLineJoin","setLineWidth","setLocalDescription","setMatrix","setMatrixValue","setMediaKeys","setMilliseconds","setMinutes","setMiterLimit","setMonth","setNamedItem","setNamedItemNS","setNonUserCodeExceptions","setOrientToAngle","setOrientToAuto","setOrientation","setOverrideHistoryNavigationMode","setPaint","setParameter","setPeriodicWave","setPointerCapture","setPosition","setPreference","setProperty","setPrototypeOf","setRGBColor","setRGBColorICCColor","setRadius","setRangeText","setRemoteDescription","setRequestHeader","setResizable","setResourceTimingBufferSize","setRotate","setScale","setSeconds","setSelectionRange","setServerCertificate","setShadow","setSkewX","setSkewY","setStart","setStartAfter","setStartBefore","setStdDeviation","setStringValue","setStrokeColor","setSuggestResult","setTargetAtTime","setTargetValueAtTime","setTime","setTimeout","setTransform","setTranslate","setUTCDate","setUTCFullYear","setUTCHours","setUTCMilliseconds","setUTCMinutes","setUTCMonth","setUTCSeconds","setUint16","setUint32","setUint8","setUri","setValueAtTime","setValueCurveAtTime","setVariable","setVelocity","setVersion","setYear","settingName","settingValue","sex","shaderSource","shadowBlur","shadowColor","shadowOffsetX","shadowOffsetY","shadowRoot","shape","shape-rendering","shapeRendering","sheet","shift","shiftKey","shiftLeft","show","showHelp","showModal","showModalDialog","showModelessDialog","showNotification","sidebar","sign","signalingState","sin","singleNodeValue","sinh","size","sizeToContent","sizes","skewX","skewXSelf","skewY","skewYSelf","slice","slope","small","smooth","smil","smoothingTimeConstant","snapToLines","snapshotItem","snapshotLength","some","sort","source","sourceBuffer","sourceBuffers","sourceIndex","spacing","span","speakAs","speaking","specified","specularConstant","specularExponent","speechSynthesis","speed","speedOfSound","spellcheck","splice","split","splitText","spreadMethod","sqrt","src","srcElement","srcFilter","srcUrn","srcdoc","srclang","srcset","stack","stackTraceLimit","stacktrace","standalone","standby","start","startContainer","startIce","startOffset","startRendering","startTime","startsWith","state","status","statusMessage","statusText","statusbar","stdDeviationX","stdDeviationY","stencilFunc","stencilFuncSeparate","stencilMask","stencilMaskSeparate","stencilOp","stencilOpSeparate","step","stepDown","stepMismatch","stepUp","sticky","stitchTiles","stop","stop-color","stop-opacity","stopColor","stopImmediatePropagation","stopOpacity","stopPropagation","storageArea","storageName","storageStatus","storeSiteSpecificTrackingException","storeWebWideTrackingException","stpVersion","stream","strike","stringValue","stringify","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeRect","strokeStyle","strokeText","strokeWidth","style","styleFloat","styleMedia","styleSheet","styleSheetSets","styleSheets","sub","subarray","subject","submit","subscribe","substr","substring","substringData","subtle","subtree","suffix","suffixes","summary","sup","supports","surfaceScale","surroundContents","suspend","suspendRedraw","swapCache","swapNode","sweepFlag","symbols","system","systemCode","systemId","systemLanguage","systemXDPI","systemYDPI","tBodies","tFoot","tHead","tabIndex","table","table-layout","tableLayout","tableValues","tag","tagName","tagUrn","tags","taintEnabled","takeRecords","tan","tanh","target","targetElement","targetTouches","targetX","targetY","tel","terminate","test","texImage2D","texParameterf","texParameteri","texSubImage2D","text","text-align","text-anchor","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-indent","text-overflow","text-rendering","text-shadow","text-transform","textAlign","textAlignLast","textAnchor","textAutospace","textBaseline","textContent","textDecoration","textDecorationBlink","textDecorationColor","textDecorationLine","textDecorationLineThrough","textDecorationNone","textDecorationOverline","textDecorationStyle","textDecorationUnderline","textIndent","textJustify","textJustifyTrim","textKashida","textKashidaSpace","textLength","textOverflow","textRendering","textShadow","textTracks","textTransform","textUnderlinePosition","then","threadId","threshold","tiltX","tiltY","time","timeEnd","timeStamp","timeout","timestamp","timestampOffset","timing","title","toArray","toBlob","toDataURL","toDateString","toElement","toExponential","toFixed","toFloat32Array","toFloat64Array","toGMTString","toISOString","toJSON","toLocaleDateString","toLocaleFormat","toLocaleLowerCase","toLocaleString","toLocaleTimeString","toLocaleUpperCase","toLowerCase","toMethod","toPrecision","toSdp","toSource","toStaticHTML","toString","toStringTag","toTimeString","toUTCString","toUpperCase","toggle","toggleLongPressEnabled","tooLong","toolbar","top","topMargin","total","totalFrameDelay","totalVideoFrames","touchAction","touches","trace","track","transaction","transactions","transform","transform-origin","transform-style","transformOrigin","transformPoint","transformString","transformStyle","transformToDocument","transformToFragment","transition","transition-delay","transition-duration","transition-property","transition-timing-function","transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction","translate","translateSelf","translationX","translationY","trim","trimLeft","trimRight","trueSpeed","trunc","truncate","type","typeDetail","typeMismatch","typeMustMatch","types","ubound","undefined","unescape","uneval","unicode-bidi","unicodeBidi","uniform1f","uniform1fv","uniform1i","uniform1iv","uniform2f","uniform2fv","uniform2i","uniform2iv","uniform3f","uniform3fv","uniform3i","uniform3iv","uniform4f","uniform4fv","uniform4i","uniform4iv","uniformMatrix2fv","uniformMatrix3fv","uniformMatrix4fv","unique","uniqueID","uniqueNumber","unitType","units","unloadEventEnd","unloadEventStart","unlock","unmount","unobserve","unpause","unpauseAnimations","unreadCount","unregister","unregisterContentHandler","unregisterProtocolHandler","unscopables","unselectable","unshift","unsubscribe","unsuspendRedraw","unsuspendRedrawAll","unwatch","unwrapKey","update","updateCommands","updateIce","updateInterval","updateSettings","updated","updating","upload","upper","upperBound","upperOpen","uri","url","urn","urns","usages","useCurrentView","useMap","useProgram","usedSpace","userAgent","userLanguage","username","v8BreakIterator","vAlign","vLink","valid","validateProgram","validationMessage","validity","value","valueAsDate","valueAsNumber","valueAsString","valueInSpecifiedUnits","valueMissing","valueOf","valueText","valueType","values","vector-effect","vectorEffect","velocityAngular","velocityExpansion","velocityX","velocityY","vendor","vendorSub","verify","version","vertexAttrib1f","vertexAttrib1fv","vertexAttrib2f","vertexAttrib2fv","vertexAttrib3f","vertexAttrib3fv","vertexAttrib4f","vertexAttrib4fv","vertexAttribDivisorANGLE","vertexAttribPointer","vertical","vertical-align","verticalAlign","verticalOverflow","vibrate","videoHeight","videoTracks","videoWidth","view","viewBox","viewBoxString","viewTarget","viewTargetString","viewport","viewportAnchorX","viewportAnchorY","viewportElement","visibility","visibilityState","visible","vlinkColor","voice","volume","vrml","vspace","w","wand","warn","wasClean","watch","watchPosition","webdriver","webkitAddKey","webkitAnimation","webkitAnimationDelay","webkitAnimationDirection","webkitAnimationDuration","webkitAnimationFillMode","webkitAnimationIterationCount","webkitAnimationName","webkitAnimationPlayState","webkitAnimationTimingFunction","webkitAppearance","webkitAudioContext","webkitAudioDecodedByteCount","webkitAudioPannerNode","webkitBackfaceVisibility","webkitBackground","webkitBackgroundAttachment","webkitBackgroundClip","webkitBackgroundColor","webkitBackgroundImage","webkitBackgroundOrigin","webkitBackgroundPosition","webkitBackgroundPositionX","webkitBackgroundPositionY","webkitBackgroundRepeat","webkitBackgroundSize","webkitBackingStorePixelRatio","webkitBorderImage","webkitBorderImageOutset","webkitBorderImageRepeat","webkitBorderImageSlice","webkitBorderImageSource","webkitBorderImageWidth","webkitBoxAlign","webkitBoxDirection","webkitBoxFlex","webkitBoxOrdinalGroup","webkitBoxOrient","webkitBoxPack","webkitBoxSizing","webkitCancelAnimationFrame","webkitCancelFullScreen","webkitCancelKeyRequest","webkitCancelRequestAnimationFrame","webkitClearResourceTimings","webkitClosedCaptionsVisible","webkitConvertPointFromNodeToPage","webkitConvertPointFromPageToNode","webkitCreateShadowRoot","webkitCurrentFullScreenElement","webkitCurrentPlaybackTargetIsWireless","webkitDirectionInvertedFromDevice","webkitDisplayingFullscreen","webkitEnterFullScreen","webkitEnterFullscreen","webkitExitFullScreen","webkitExitFullscreen","webkitExitPointerLock","webkitFullScreenKeyboardInputAllowed","webkitFullscreenElement","webkitFullscreenEnabled","webkitGenerateKeyRequest","webkitGetAsEntry","webkitGetDatabaseNames","webkitGetEntries","webkitGetEntriesByName","webkitGetEntriesByType","webkitGetFlowByName","webkitGetGamepads","webkitGetImageDataHD","webkitGetNamedFlows","webkitGetRegionFlowRanges","webkitGetUserMedia","webkitHasClosedCaptions","webkitHidden","webkitIDBCursor","webkitIDBDatabase","webkitIDBDatabaseError","webkitIDBDatabaseException","webkitIDBFactory","webkitIDBIndex","webkitIDBKeyRange","webkitIDBObjectStore","webkitIDBRequest","webkitIDBTransaction","webkitImageSmoothingEnabled","webkitIndexedDB","webkitInitMessageEvent","webkitIsFullScreen","webkitKeys","webkitLineDashOffset","webkitLockOrientation","webkitMatchesSelector","webkitMediaStream","webkitNotifications","webkitOfflineAudioContext","webkitOrientation","webkitPeerConnection00","webkitPersistentStorage","webkitPointerLockElement","webkitPostMessage","webkitPreservesPitch","webkitPutImageDataHD","webkitRTCPeerConnection","webkitRegionOverset","webkitRequestAnimationFrame","webkitRequestFileSystem","webkitRequestFullScreen","webkitRequestFullscreen","webkitRequestPointerLock","webkitResolveLocalFileSystemURL","webkitSetMediaKeys","webkitSetResourceTimingBufferSize","webkitShadowRoot","webkitShowPlaybackTargetPicker","webkitSlice","webkitSpeechGrammar","webkitSpeechGrammarList","webkitSpeechRecognition","webkitSpeechRecognitionError","webkitSpeechRecognitionEvent","webkitStorageInfo","webkitSupportsFullscreen","webkitTemporaryStorage","webkitTextSizeAdjust","webkitTransform","webkitTransformOrigin","webkitTransition","webkitTransitionDelay","webkitTransitionDuration","webkitTransitionProperty","webkitTransitionTimingFunction","webkitURL","webkitUnlockOrientation","webkitUserSelect","webkitVideoDecodedByteCount","webkitVisibilityState","webkitWirelessVideoPlaybackDisabled","webkitdropzone","webstore","weight","whatToShow","wheelDelta","wheelDeltaX","wheelDeltaY","which","white-space","whiteSpace","wholeText","widows","width","will-change","willChange","willValidate","window","withCredentials","word-break","word-spacing","word-wrap","wordBreak","wordSpacing","wordWrap","wrap","wrapKey","write","writeln","writingMode","x","x1","x2","xChannelSelector","xmlEncoding","xmlStandalone","xmlVersion","xmlbase","xmllang","xmlspace","y","y1","y2","yChannelSelector","yandex","z","z-index","zIndex","zoom","zoomAndPan","zoomRectScreen"];function ir(e,t){function n(e){p(t,e)}e.walk(new An((function(e){e instanceof Ft&&e.quote?n(e.key):e instanceof Ot&&e.quote?n(e.key.name):e instanceof ht&&rr(e.property,n)})))}function rr(e,t){e.walk(new An((function(e){return e instanceof dt?rr(e.tail_node(),t):e instanceof an?t(e.value):e instanceof vt&&(rr(e.consequent,t),rr(e.alternative,t)),!0})))}function or(e,t){var n=(t=o(t,{builtins:!1,cache:null,debug:!1,keep_quoted:!1,only_cache:!1,regex:null,reserved:null,undeclared:!1},!0)).reserved;Array.isArray(n)||(n=[n]);var i=new Set(n);t.builtins||function(e){nr.forEach(i);var t={},n="object"==typeof global?global:self;function i(t){e.add(t)}["Symbol","Map","Promise","Proxy","Reflect","Set","WeakMap","WeakSet"].forEach((function(e){t[e]=n[e]||new Function})),["null","true","false","Infinity","-Infinity","undefined"].forEach(i),[Object,Array,Function,Number,String,Boolean,Error,Math,Date,RegExp,t.Symbol,ArrayBuffer,DataView,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,eval,EvalError,Float32Array,Float64Array,Int8Array,Int16Array,Int32Array,isFinite,isNaN,JSON,t.Map,parseFloat,parseInt,t.Promise,t.Proxy,RangeError,ReferenceError,t.Reflect,t.Set,SyntaxError,TypeError,Uint8Array,Uint8ClampedArray,Uint16Array,Uint32Array,URIError,t.WeakMap,t.WeakSet].forEach((function(e){Object.getOwnPropertyNames(e).map(i),e.prototype&&Object.getOwnPropertyNames(e.prototype).map(i)}))}(i);var r,a=-1;t.cache?(r=t.cache.props).forEach((function(e){i.add(e)})):r=new Map;var s,u=t.regex&&new RegExp(t.regex),c=!1!==t.debug;c&&(s=!0===t.debug?"":t.debug);var l=new Set,f=new Set,p="strict"===t.keep_quoted;return e.walk(new An((function(e){if(e instanceof Ft)"string"!=typeof e.key||p&&e.quote||m(e.key);else if(e instanceof Ot)p&&e.key.end.quote||m(e.key.name);else if(e instanceof Et){var n=!!t.undeclared;if(!n){for(var i=e;i.expression;)i=i.expression;n=!(i.thedef&&i.thedef.undeclared)}!n||p&&e.quote||m(e.property)}else e instanceof ht?p||rr(e.property,m):e instanceof pt&&"Object.defineProperty"==e.expression.print_to_string()&&rr(e.args[1],m)}))),e.transform(new vn((function(e){e instanceof Ft?"string"!=typeof e.key||p&&e.quote||(e.key=E(e.key)):e instanceof Ot?p&&e.key.end.quote||(e.key.name=E(e.key.name)):e instanceof Et?p&&e.quote||(e.property=E(e.property)):!t.keep_quoted&&e instanceof ht?e.property=h(e.property):e instanceof pt&&"Object.defineProperty"==e.expression.print_to_string()&&(e.args[1]=h(e.args[1]))})));function _(e){return!f.has(e)&&(!i.has(e)&&(t.only_cache?r.has(e):!/^-?[0-9]+(\.[0-9]+)?(e[+-][0-9]+)?$/.test(e)))}function d(e){return!(u&&!u.test(e))&&(!i.has(e)&&(r.has(e)||l.has(e)))}function m(e){_(e)&&l.add(e),d(e)||f.add(e)}function E(e){if(!d(e))return e;var t=r.get(e);if(!t){if(c){var n="_$"+e+"$"+s+"_";_(n)&&(t=n)}if(!t)do{t=Gn(++a)}while(!_(t));r.set(e,t)}return t}function h(e){return e.transform(new vn((function(e){if(e instanceof dt){var t=e.expressions.length-1;e.expressions[t]=h(e.expressions[t])}else e instanceof an?e.value=E(e.value):e instanceof vt&&(e.consequent=h(e.consequent),e.alternative=h(e.alternative));return e})))}}var ar="undefined"==typeof atob?function(e){return Buffer.from(e,"base64").toString()}:atob,sr="undefined"==typeof btoa?function(e){return Buffer.from(e).toString("base64")}:btoa;function ur(e,t,n){t[e]&&n.forEach((function(n){t[n]&&("object"!=typeof t[n]&&(t[n]={}),e in t[n]||(t[n][e]=t[e]))}))}function cr(e){e&&("props"in e?e.props instanceof Map||(e.props=function(e){var t=new Map;for(var n in e)D(e,n)&&"$"===n.charAt(0)&&t.set(n.substr(1),e[n]);return t}(e.props)):e.props=new Map)}function lr(e){return{props:(t=e.props,n=Object.create(null),t.forEach((function(e,t){n["$"+t]=e})),n)};var t,n}function fr(e,n){var i,r,a=fe.warn_function;try{var s,u=(n=o(n,{compress:{},ecma:void 0,enclose:!1,ie8:!1,keep_classnames:void 0,keep_fnames:!1,mangle:{},module:!1,nameCache:null,output:{},parse:{},rename:void 0,safari10:!1,sourceMap:!1,timings:!1,toplevel:!1,warnings:!1,wrap:!1},!0)).timings&&{start:Date.now()};void 0===n.keep_classnames&&(n.keep_classnames=n.keep_fnames),void 0===n.rename&&(n.rename=n.compress&&n.mangle),ur("ecma",n,["parse","compress","output"]),ur("ie8",n,["compress","mangle","output"]),ur("keep_classnames",n,["compress","mangle"]),ur("keep_fnames",n,["compress","mangle"]),ur("module",n,["parse","compress","mangle"]),ur("safari10",n,["mangle","output"]),ur("toplevel",n,["compress","mangle"]),ur("warnings",n,["compress"]),n.mangle&&(n.mangle=o(n.mangle,{cache:n.nameCache&&(n.nameCache.vars||{}),eval:!1,ie8:!1,keep_classnames:!1,keep_fnames:!1,module:!1,properties:!1,reserved:[],safari10:!1,toplevel:!1},!0),n.mangle.properties&&("object"!=typeof n.mangle.properties&&(n.mangle.properties={}),n.mangle.properties.keep_quoted&&(s=n.mangle.properties.reserved,Array.isArray(s)||(s=[]),n.mangle.properties.reserved=s),!n.nameCache||"cache"in n.mangle.properties||(n.mangle.properties.cache=n.nameCache.props||{})),cr(n.mangle.cache),cr(n.mangle.properties.cache)),n.sourceMap&&(n.sourceMap=o(n.sourceMap,{asObject:!1,content:null,filename:null,includeSources:!1,root:null,url:null},!0));var c,l=[];if(n.warnings&&!fe.warn_function&&(fe.warn_function=function(e){l.push(e)}),u&&(u.parse=Date.now()),e instanceof we)c=e;else{for(var f in"string"==typeof e&&(e=[e]),n.parse=n.parse||{},n.parse.toplevel=null,e)if(D(e,f)&&(n.parse.filename=f,n.parse.toplevel=ue(e[f],n.parse),n.sourceMap&&"inline"==n.sourceMap.content)){if(Object.keys(e).length>1)throw new Error("inline source map only works with singular input");n.sourceMap.content=(i=e[f],r=void 0,(r=/(?:^|[^.])\/\/# sourceMappingURL=data:application\/json(;[\w=-]*)?;base64,([+/0-9A-Za-z]*=*)\s*$/.exec(i))?ar(r[2]):(fe.warn("inline source map not found"),null))}c=n.parse.toplevel}s&&"strict"!==n.mangle.properties.keep_quoted&&ir(c,s),n.wrap&&(c=c.wrap_commonjs(n.wrap)),n.enclose&&(c=c.wrap_enclose(n.enclose)),u&&(u.rename=Date.now()),u&&(u.compress=Date.now()),n.compress&&(c=new ei(n.compress).compress(c)),u&&(u.scope=Date.now()),n.mangle&&c.figure_out_scope(n.mangle),u&&(u.mangle=Date.now()),n.mangle&&(Gn.reset(),c.compute_char_frequency(n.mangle),c.mangle_names(n.mangle)),u&&(u.properties=Date.now()),n.mangle&&n.mangle.properties&&(c=or(c,n.mangle.properties)),u&&(u.output=Date.now());var p={};if(n.output.ast&&(p.ast=c),!D(n.output,"code")||n.output.code){if(n.sourceMap&&("string"==typeof n.sourceMap.content&&(n.sourceMap.content=JSON.parse(n.sourceMap.content)),n.output.source_map=function(e){e=o(e,{file:null,root:null,orig:null,orig_line_diff:0,dest_line_diff:0});var n=new t.SourceMapGenerator({file:e.file,sourceRoot:e.root}),i=e.orig&&new t.SourceMapConsumer(e.orig);return i&&i.sources.forEach((function(e){var t=i.sourceContentFor(e,!0);t&&n.setSourceContent(e,t)})),{add:function(t,r,o,a,s,u){if(i){var c=i.originalPositionFor({line:a,column:s});if(null===c.source)return;t=c.source,a=c.line,s=c.column,u=c.name||u}n.addMapping({generated:{line:r+e.dest_line_diff,column:o},original:{line:a+e.orig_line_diff,column:s},source:t,name:u})},get:function(){return n},toString:function(){return JSON.stringify(n.toJSON())}}}({file:n.sourceMap.filename,orig:n.sourceMap.content,root:n.sourceMap.root}),n.sourceMap.includeSources)){if(e instanceof we)throw new Error("original source content unavailable");for(var f in e)D(e,f)&&n.output.source_map.get().setSourceContent(f,e[f])}delete n.output.ast,delete n.output.code;var _=In(n.output);if(c.print(_),p.code=_.get(),n.sourceMap)if(n.sourceMap.asObject?p.map=n.output.source_map.get().toJSON():p.map=n.output.source_map.toString(),"inline"==n.sourceMap.url){var d="object"==typeof p.map?JSON.stringify(p.map):p.map;p.code+="\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,"+sr(d)}else n.sourceMap.url&&(p.code+="\n//# sourceMappingURL="+n.sourceMap.url)}return n.nameCache&&n.mangle&&(n.mangle.cache&&(n.nameCache.vars=lr(n.mangle.cache)),n.mangle.properties&&n.mangle.properties.cache&&(n.nameCache.props=lr(n.mangle.properties.cache))),u&&(u.end=Date.now(),p.timings={parse:.001*(u.rename-u.parse),rename:.001*(u.compress-u.rename),compress:.001*(u.scope-u.compress),scope:.001*(u.mangle-u.scope),mangle:.001*(u.properties-u.mangle),properties:.001*(u.output-u.properties),output:.001*(u.end-u.output),total:.001*(u.end-u.start)}),l.length&&(p.warnings=l),p}catch(e){return{error:e}}finally{fe.warn_function=a}}function pr(e){var t=fr("",e);return t.error&&t.error.defs}!function(){var e=function(e){for(var t=!0,n=0;n<e.length;n++)t&&e[n]instanceof pe&&e[n].body instanceof an?e[n]=new de({start:e[n].start,end:e[n].end,value:e[n].body.value}):!t||e[n]instanceof pe&&e[n].body instanceof an||(t=!1);return e},t={Program:function(t){return new we({start:i(t),end:r(t),body:e(t.body.map(s))})},ArrayPattern:function(e){return new Be({start:i(e),end:r(e),names:e.elements.map((function(e){return null===e?new dn:s(e)})),is_array:!0})},ObjectPattern:function(e){return new Be({start:i(e),end:r(e),names:e.properties.map(s),is_array:!1})},AssignmentPattern:function(e){var t=At;if(a.length>2){var n=a[a.length-2];"FunctionDeclaration"!==n.type&&"FunctionExpression"!==n.type&&"ArrowFunctionExpression"!==n.type||(t=bt)}return new t({start:i(e),end:r(e),left:s(e.left),operator:"=",right:s(e.right)})},SpreadElement:function(e){return new xe({start:i(e),end:r(e),expression:s(e.argument)})},RestElement:function(e){return new xe({start:i(e),end:r(e),expression:s(e.argument)})},TemplateElement:function(e){return new Ge({start:i(e),end:r(e),value:e.value.cooked,raw:e.value.raw})},TemplateLiteral:function(e){for(var t=[],n=0;n<e.quasis.length;n++)t.push(s(e.quasis[n])),e.expressions[n]&&t.push(s(e.expressions[n]));return new Ue({start:i(e),end:r(e),segments:t})},TaggedTemplateExpression:function(e){return new Ke({start:i(e),end:r(e),template_string:s(e.quasi),prefix:s(e.tag)})},FunctionDeclaration:function(t){return new Pe({start:i(t),end:r(t),name:s(t.id),argnames:t.params.map(s),is_generator:t.generator,async:t.async,body:e(s(t.body).body)})},FunctionExpression:function(t){return new Le({start:i(t),end:r(t),name:s(t.id),argnames:t.params.map(s),is_generator:t.generator,async:t.async,body:e(s(t.body).body)})},ArrowFunctionExpression:function(e){return new Ve({start:i(e),end:r(e),argnames:e.params.map(s),body:s(e.body),async:e.async})},ExpressionStatement:function(e){return new me({start:i(e),end:r(e),body:s(e.expression)})},TryStatement:function(e){var t=e.handlers||[e.handler];if(t.length>1||e.guardedHandlers&&e.guardedHandlers.length)throw new Error("Multiple catch clauses are not supported.");return new tt({start:i(e),end:r(e),body:s(e.block).body,bcatch:s(t[0]),bfinally:e.finalizer?new it(s(e.finalizer)):null})},Property:function(e){var t=e.key,n={start:i(t||e.value),end:r(e.value),key:"Identifier"==t.type?t.name:t.value,value:s(e.value)};return e.computed&&(n.key=s(e.key)),e.method?(n.is_generator=e.value.generator,n.async=e.value.async,e.computed?n.key=s(e.key):n.key=new Xt({name:n.key}),new Nt(n)):"init"==e.kind?("Identifier"!=t.type&&"Literal"!=t.type&&(n.key=s(t)),new Ft(n)):("string"!=typeof n.key&&"number"!=typeof n.key||(n.key=new Xt({name:n.key})),n.value=new Ie(n.value),"get"==e.kind?new Rt(n):"set"==e.kind?new Mt(n):"method"==e.kind?(n.async=e.value.async,n.is_generator=e.value.generator,n.quote=e.computed?'"':null,new Nt(n)):void 0)},MethodDefinition:function(e){var t={start:i(e),end:r(e),key:e.computed?s(e.key):new Xt({name:e.key.name||e.key.value}),value:s(e.value),static:e.static};return"get"==e.kind?new Rt(t):"set"==e.kind?new Mt(t):(t.is_generator=e.value.generator,t.async=e.value.async,new Nt(t))},ArrayExpression:function(e){return new yt({start:i(e),end:r(e),elements:e.elements.map((function(e){return null===e?new dn:s(e)}))})},ObjectExpression:function(e){return new Ct({start:i(e),end:r(e),properties:e.properties.map((function(e){return"SpreadElement"===e.type?s(e):(e.type="Property",s(e))}))})},SequenceExpression:function(e){return new dt({start:i(e),end:r(e),expressions:e.expressions.map(s)})},MemberExpression:function(e){return new(e.computed?ht:Et)({start:i(e),end:r(e),property:e.computed?s(e.property):e.property.name,expression:s(e.object)})},SwitchCase:function(e){return new(e.test?et:Qe)({start:i(e),end:r(e),expression:s(e.test),body:e.consequent.map(s)})},VariableDeclaration:function(e){return new("const"===e.kind?st:"let"===e.kind?at:ot)({start:i(e),end:r(e),definitions:e.declarations.map(s)})},ImportDeclaration:function(e){var t=null,n=null;return e.specifiers.forEach((function(e){"ImportSpecifier"===e.type?(n||(n=[]),n.push(new ut({start:i(e),end:r(e),foreign_name:s(e.imported),name:s(e.local)}))):"ImportDefaultSpecifier"===e.type?t=s(e.local):"ImportNamespaceSpecifier"===e.type&&(n||(n=[]),n.push(new ut({start:i(e),end:r(e),foreign_name:new jt({name:"*"}),name:s(e.local)})))})),new ct({start:i(e),end:r(e),imported_name:t,imported_names:n,module_name:s(e.source)})},ExportAllDeclaration:function(e){return new lt({start:i(e),end:r(e),exported_names:[new ut({name:new en({name:"*"}),foreign_name:new en({name:"*"})})],module_name:s(e.source)})},ExportNamedDeclaration:function(e){return new lt({start:i(e),end:r(e),exported_definition:s(e.declaration),exported_names:e.specifiers&&e.specifiers.length?e.specifiers.map((function(e){return new ut({foreign_name:s(e.exported),name:s(e.local)})})):null,module_name:s(e.source)})},ExportDefaultDeclaration:function(e){return new lt({start:i(e),end:r(e),exported_value:s(e.declaration),is_default:!0})},Literal:function(e){var t=e.value,n={start:i(e),end:r(e)},o=e.regex;if(o&&o.pattern)return n.value={source:o.pattern,flags:o.flags},new cn(n);if(o){const i=e.raw||t,r=i.match(/^\/(.*)\/(\w*)$/);if(!r)throw new Error("Invalid regex source "+i);const[o,a,s]=r;return n.value={source:a,flags:s},new cn(n)}if(null===t)return new fn(n);switch(typeof t){case"string":return n.value=t,new an(n);case"number":return n.value=t,new sn(n);case"boolean":return new(t?Dn:hn)(n)}},MetaProperty:function(e){if("new"===e.meta.name&&"target"===e.property.name)return new Lt({start:i(e),end:r(e)})},Identifier:function(e){var t=a[a.length-2];return new("LabeledStatement"==t.type?Zt:"VariableDeclarator"==t.type&&t.id===e?"const"==t.kind?Kt:"let"==t.kind?Ut:Pt:/Import.*Specifier/.test(t.type)?t.local===e?$t:jt:"ExportSpecifier"==t.type?t.local===e?Qt:en:"FunctionExpression"==t.type?t.id===e?zt:Gt:"FunctionDeclaration"==t.type?t.id===e?Ht:Gt:"ArrowFunctionExpression"==t.type?t.params.includes(e)?Gt:Jt:"ClassExpression"==t.type?t.id===e?Yt:Jt:"Property"==t.type?t.key===e&&t.computed||t.value===e?Jt:Xt:"ClassDeclaration"==t.type?t.id===e?Wt:Jt:"MethodDefinition"==t.type?t.computed?Jt:Xt:"CatchClause"==t.type?qt:"BreakStatement"==t.type||"ContinueStatement"==t.type?tn:Jt)({start:i(e),end:r(e),name:e.name})},BigIntLiteral:e=>new un({start:i(e),end:r(e),value:e.value})};function n(e){if("Literal"==e.type)return null!=e.raw?e.raw:e.value+""}function i(e){var t=e.loc,i=t&&t.start,r=e.range;return new le({file:t&&t.source,line:i&&i.line,col:i&&i.column,pos:r?r[0]:e.start,endline:i&&i.line,endcol:i&&i.column,endpos:r?r[0]:e.start,raw:n(e)})}function r(e){var t=e.loc,i=t&&t.end,r=e.range;return new le({file:t&&t.source,line:i&&i.line,col:i&&i.column,pos:r?r[1]:e.end,endline:i&&i.line,endcol:i&&i.column,endpos:r?r[1]:e.end,raw:n(e)})}function o(e,n,o){var a="function From_Moz_"+e+"(M){\n";a+="return new U2."+n.name+"({\nstart: my_start_token(M),\nend: my_end_token(M)";var c="function To_Moz_"+e+"(M){\n";c+="return {\ntype: "+JSON.stringify(e),o&&o.split(/\s*,\s*/).forEach((function(e){var t=/([a-z0-9$_]+)([=@>%])([a-z0-9$_]+)/i.exec(e);if(!t)throw new Error("Can't understand property map: "+e);var n=t[1],i=t[2],r=t[3];switch(a+=",\n"+r+": ",c+=",\n"+n+": ",i){case"@":a+="M."+n+".map(from_moz)",c+="M."+r+".map(to_moz)";break;case">":a+="from_moz(M."+n+")",c+="to_moz(M."+r+")";break;case"=":a+="M."+n,c+="M."+r;break;case"%":a+="from_moz(M."+n+").body",c+="to_moz_block(M)";break;default:throw new Error("Can't understand operator in propmap: "+e)}})),a+="\n})\n}",c+="\n}\n}",a=new Function("U2","my_start_token","my_end_token","from_moz","return("+a+")")(Cn,i,r,s),c=new Function("to_moz","to_moz_block","to_moz_scope","return("+c+")")(l,p,_),t[e]=a,u(n,c)}t.UpdateExpression=t.UnaryExpression=function(e){return new(("prefix"in e?e.prefix:"UnaryExpression"==e.type)?gt:St)({start:i(e),end:r(e),operator:e.operator,expression:s(e.argument)})},t.ClassDeclaration=t.ClassExpression=function(e){return new("ClassDeclaration"===e.type?xt:kt)({start:i(e),end:r(e),name:s(e.id),extends:s(e.superClass),properties:e.body.body.map(s)})},o("EmptyStatement",Se),o("BlockStatement",ge,"body@body"),o("IfStatement",je,"test>condition, consequent>body, alternate>alternative"),o("LabeledStatement",ve,"label>label, body>body"),o("BreakStatement",qe,"label>label"),o("ContinueStatement",$e,"label>label"),o("WithStatement",Re,"object>expression, body>body"),o("SwitchStatement",Ze,"discriminant>expression, cases@body"),o("ReturnStatement",ze,"argument>value"),o("ThrowStatement",We,"argument>value"),o("WhileStatement",Ce,"test>condition, body>body"),o("DoWhileStatement",ye,"test>condition, body>body"),o("ForStatement",Oe,"init>init, test>condition, update>step, body>body"),o("ForInStatement",Fe,"left>init, right>object, body>body"),o("ForOfStatement",Me,"left>init, right>object, body>body, await=await"),o("AwaitExpression",gn,"argument>expression"),o("YieldExpression",Sn,"argument>expression, delegate=is_star"),o("DebuggerStatement",_e),o("VariableDeclarator",ft,"id>name, init>value"),o("CatchClause",nt,"param>argname, body%body"),o("ThisExpression",nn),o("Super",rn),o("BinaryExpression",At,"operator=operator, left>left, right>right"),o("LogicalExpression",At,"operator=operator, left>left, right>right"),o("AssignmentExpression",Tt,"operator=operator, left>left, right>right"),o("ConditionalExpression",vt,"test>condition, consequent>consequent, alternate>alternative"),o("NewExpression",_t,"callee>expression, arguments@args"),o("CallExpression",pt,"callee>expression, arguments@args"),u(we,(function(e){return _("Program",e)})),u(xe,(function(e,t){return{type:f()?"RestElement":"SpreadElement",argument:l(e.expression)}})),u(Ke,(function(e){return{type:"TaggedTemplateExpression",tag:l(e.prefix),quasi:l(e.template_string)}})),u(Ue,(function(e){for(var t=[],n=[],i=0;i<e.segments.length;i++)i%2!=0?n.push(l(e.segments[i])):t.push({type:"TemplateElement",value:{raw:e.segments[i].raw,cooked:e.segments[i].value},tail:i===e.segments.length-1});return{type:"TemplateLiteral",quasis:t,expressions:n}})),u(Pe,(function(e){return{type:"FunctionDeclaration",id:l(e.name),params:e.argnames.map(l),generator:e.is_generator,async:e.async,body:_("BlockStatement",e)}})),u(Le,(function(e,t){var n=void 0!==t.is_generator?t.is_generator:e.is_generator;return{type:"FunctionExpression",id:l(e.name),params:e.argnames.map(l),generator:n,async:e.async,body:_("BlockStatement",e)}})),u(Ve,(function(e){var t=e.body instanceof Array?{type:"BlockStatement",body:e.body.map(l)}:l(e.body);return{type:"ArrowFunctionExpression",params:e.argnames.map(l),async:e.async,body:t}})),u(Be,(function(e){return e.is_array?{type:"ArrayPattern",elements:e.names.map(l)}:{type:"ObjectPattern",properties:e.names.map(l)}})),u(de,(function(e){return{type:"ExpressionStatement",expression:{type:"Literal",value:e.value}}})),u(me,(function(e){return{type:"ExpressionStatement",expression:l(e.body)}})),u(Je,(function(e){return{type:"SwitchCase",test:l(e.expression),consequent:e.body.map(l)}})),u(tt,(function(e){return{type:"TryStatement",block:p(e),handler:l(e.bcatch),guardedHandlers:[],finalizer:l(e.bfinally)}})),u(nt,(function(e){return{type:"CatchClause",param:l(e.argname),guard:null,body:p(e)}})),u(rt,(function(e){return{type:"VariableDeclaration",kind:e instanceof st?"const":e instanceof at?"let":"var",declarations:e.definitions.map(l)}})),u(lt,(function(e){return e.exported_names?"*"===e.exported_names[0].name.name?{type:"ExportAllDeclaration",source:l(e.module_name)}:{type:"ExportNamedDeclaration",specifiers:e.exported_names.map((function(e){return{type:"ExportSpecifier",exported:l(e.foreign_name),local:l(e.name)}})),declaration:l(e.exported_definition),source:l(e.module_name)}:{type:e.is_default?"ExportDefaultDeclaration":"ExportNamedDeclaration",declaration:l(e.exported_value||e.exported_definition)}})),u(ct,(function(e){var t=[];return e.imported_name&&t.push({type:"ImportDefaultSpecifier",local:l(e.imported_name)}),e.imported_names&&"*"===e.imported_names[0].foreign_name.name?t.push({type:"ImportNamespaceSpecifier",local:l(e.imported_names[0].name)}):e.imported_names&&e.imported_names.forEach((function(e){t.push({type:"ImportSpecifier",local:l(e.name),imported:l(e.foreign_name)})})),{type:"ImportDeclaration",specifiers:t,source:l(e.module_name)}})),u(dt,(function(e){return{type:"SequenceExpression",expressions:e.expressions.map(l)}})),u(mt,(function(e){var t=e instanceof ht;return{type:"MemberExpression",object:l(e.expression),computed:t,property:t?l(e.property):{type:"Identifier",name:e.property}}})),u(Dt,(function(e){return{type:"++"==e.operator||"--"==e.operator?"UpdateExpression":"UnaryExpression",operator:e.operator,prefix:e instanceof gt,argument:l(e.expression)}})),u(At,(function(e){return"="==e.operator&&f()?{type:"AssignmentPattern",left:l(e.left),right:l(e.right)}:{type:"&&"==e.operator||"||"==e.operator?"LogicalExpression":"BinaryExpression",left:l(e.left),operator:e.operator,right:l(e.right)}})),u(yt,(function(e){return{type:"ArrayExpression",elements:e.elements.map(l)}})),u(Ct,(function(e){return{type:"ObjectExpression",properties:e.properties.map(l)}})),u(Ot,(function(e,t){var n,i=e.key instanceof fe?l(e.key):{type:"Identifier",value:e.key};"number"==typeof e.key&&(i={type:"Literal",value:Number(e.key)}),"string"==typeof e.key&&(i={type:"Identifier",name:e.key});var r="string"==typeof e.key||"number"==typeof e.key,o=!r&&(!(e.key instanceof It)||e.key instanceof Jt);return e instanceof Ft?(n="init",o=!r):e instanceof Rt?n="get":e instanceof Mt&&(n="set"),t instanceof wt?{type:"MethodDefinition",computed:o,kind:n,static:e.static,key:l(e.key),value:l(e.value)}:{type:"Property",computed:o,kind:n,key:i,value:l(e.value)}})),u(Nt,(function(e,t){return t instanceof Ct?{type:"Property",computed:!(e.key instanceof It)||e.key instanceof Jt,kind:"init",method:!0,shorthand:!1,key:l(e.key),value:l(e.value)}:{type:"MethodDefinition",computed:!(e.key instanceof It)||e.key instanceof Jt,kind:"constructor"===e.key?"constructor":"method",static:e.static,key:l(e.key),value:l(e.value)}})),u(wt,(function(e){return{type:e instanceof kt?"ClassExpression":"ClassDeclaration",superClass:l(e.extends),id:e.name?l(e.name):null,body:{type:"ClassBody",body:e.properties.map(l)}}})),u(Lt,(function(e){return{type:"MetaProperty",meta:{type:"Identifier",name:"new"},property:{type:"Identifier",name:"target"}}})),u(It,(function(e,t){if(e instanceof Xt&&t.quote)return{type:"Literal",value:e.name};var n=e.definition();return{type:"Identifier",name:n?n.mangled_name||n.name:e.name}})),u(cn,(function(e){const t=e.value.source,n=e.value.flags;return{type:"Literal",value:null,raw:e.print_to_string(),regex:{pattern:t,flags:n}}})),u(on,(function(e){var t=e.value;return"number"==typeof t&&(t<0||0===t&&1/t<0)?{type:"UnaryExpression",operator:"-",prefix:!0,argument:{type:"Literal",value:-t,raw:e.start.raw}}:{type:"Literal",value:t,raw:e.start.raw}})),u(ln,(function(e){return{type:"Identifier",name:String(e.value)}})),u(un,e=>({type:"BigIntLiteral",value:e.value})),En.DEFMETHOD("to_mozilla_ast",on.prototype.to_mozilla_ast),fn.DEFMETHOD("to_mozilla_ast",on.prototype.to_mozilla_ast),dn.DEFMETHOD("to_mozilla_ast",(function(){return null})),De.DEFMETHOD("to_mozilla_ast",ge.prototype.to_mozilla_ast),ke.DEFMETHOD("to_mozilla_ast",Le.prototype.to_mozilla_ast);var a=null;function s(e){a.push(e);var n=null!=e?t[e.type](e):null;return a.pop(),n}function u(e,t){e.DEFMETHOD("to_mozilla_ast",(function(e){return n=this,i=t(this,e),r=n.start,o=n.end,r&&o?(null!=r.pos&&null!=o.endpos&&(i.range=[r.pos,o.endpos]),r.line&&(i.loc={start:{line:r.line,column:r.col},end:o.endline?{line:o.endline,column:o.endcol}:null},r.file&&(i.loc.source=r.file)),i):i;var n,i,r,o}))}fe.from_mozilla_ast=function(e){var t=a;a=[];var n=s(e);return a=t,n};var c=null;function l(e){null===c&&(c=[]),c.push(e);var t=null!=e?e.to_mozilla_ast(c[c.length-2]):null;return c.pop(),0===c.length&&(c=null),t}function f(){for(var e=c.length;e--;)if(c[e]instanceof Be)return!0;return!1}function p(e){return{type:"BlockStatement",body:e.body.map(l)}}function _(e,t){var n=t.body.map(l);return t.body[0]instanceof me&&t.body[0].body instanceof an&&n.unshift(l(new Se(t.body[0]))),{type:e,body:n}}}(),e.AST_Accessor=Ie,e.AST_Array=yt,e.AST_Arrow=Ve,e.AST_Assign=Tt,e.AST_Atom=ln,e.AST_Await=gn,e.AST_Binary=At,e.AST_Block=De,e.AST_BlockStatement=ge,e.AST_Boolean=En,e.AST_Break=qe,e.AST_Call=pt,e.AST_Case=et,e.AST_Catch=nt,e.AST_Class=wt,e.AST_ClassExpression=kt,e.AST_ConciseMethod=Nt,e.AST_Conditional=vt,e.AST_Const=st,e.AST_Constant=on,e.AST_Continue=$e,e.AST_DWLoop=be,e.AST_Debugger=_e,e.AST_DefClass=xt,e.AST_Default=Qe,e.AST_DefaultAssign=bt,e.AST_Definitions=rt,e.AST_Defun=Pe,e.AST_Destructuring=Be,e.AST_Directive=de,e.AST_Do=ye,e.AST_Dot=Et,e.AST_EmptyStatement=Se,e.AST_Exit=Xe,e.AST_Expansion=xe,e.AST_Export=lt,e.AST_False=hn,e.AST_Finally=it,e.AST_For=Oe,e.AST_ForIn=Fe,e.AST_ForOf=Me,e.AST_Function=Le,e.AST_Hole=dn,e.AST_If=je,e.AST_Import=ct,e.AST_Infinity=mn,e.AST_IterationStatement=Te,e.AST_Jump=He,e.AST_Label=Zt,e.AST_LabelRef=tn,e.AST_LabeledStatement=ve,e.AST_Lambda=ke,e.AST_Let=at,e.AST_LoopControl=Ye,e.AST_NaN=pn,e.AST_NameMapping=ut,e.AST_New=_t,e.AST_NewTarget=Lt,e.AST_Node=fe,e.AST_Null=fn,e.AST_Number=sn,e.AST_Object=Ct,e.AST_ObjectGetter=Rt,e.AST_ObjectKeyVal=Ft,e.AST_ObjectProperty=Ot,e.AST_ObjectSetter=Mt,e.AST_PrefixedTemplateString=Ke,e.AST_PropAccess=mt,e.AST_RegExp=cn,e.AST_Return=ze,e.AST_Scope=Ne,e.AST_Sequence=dt,e.AST_SimpleStatement=me,e.AST_Statement=pe,e.AST_StatementWithBody=Ae,e.AST_String=an,e.AST_Sub=ht,e.AST_Super=rn,e.AST_Switch=Ze,e.AST_SwitchBranch=Je,e.AST_Symbol=It,e.AST_SymbolBlockDeclaration=Bt,e.AST_SymbolCatch=qt,e.AST_SymbolClass=Yt,e.AST_SymbolConst=Kt,e.AST_SymbolDeclaration=Vt,e.AST_SymbolDefClass=Wt,e.AST_SymbolDefun=Ht,e.AST_SymbolExport=Qt,e.AST_SymbolExportForeign=en,e.AST_SymbolFunarg=Gt,e.AST_SymbolImport=$t,e.AST_SymbolImportForeign=jt,e.AST_SymbolLambda=zt,e.AST_SymbolLet=Ut,e.AST_SymbolMethod=Xt,e.AST_SymbolRef=Jt,e.AST_SymbolVar=Pt,e.AST_TemplateSegment=Ge,e.AST_TemplateString=Ue,e.AST_This=nn,e.AST_Throw=We,e.AST_Token=le,e.AST_Toplevel=we,e.AST_True=Dn,e.AST_Try=tt,e.AST_Unary=Dt,e.AST_UnaryPostfix=St,e.AST_UnaryPrefix=gt,e.AST_Undefined=_n,e.AST_Var=ot,e.AST_VarDef=ft,e.AST_While=Ce,e.AST_With=Re,e.AST_Yield=Sn,e.Compressor=ei,e.OutputStream=In,e.TreeTransformer=vn,e.TreeWalker=An,e._INLINE=bn,e._JS_Parse_Error=J,e._NOINLINE=yn,e._PURE=Tn,e._has_annotation=T,e._tokenizer=ne,e.base54=Gn,e.default_options=function(){const e={};return Object.keys(pr({0:0})).forEach(t=>{const n=pr({[t]:{0:0}});n&&(e[t]=n)}),e},e.defaults=o,e.mangle_properties=or,e.minify=fr,e.parse=ue,e.push_uniq=p,e.reserve_quoted_keys=ir,e.string_template=_,e.to_ascii=ar}));
//# sourceMappingURL=bundle.min.js.map
