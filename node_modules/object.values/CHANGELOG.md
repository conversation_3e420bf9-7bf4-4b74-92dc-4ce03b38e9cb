1.1.0 / 2019-01-01
=================
  * [New] add `auto` entry point`
  * [Deps] update `define-properties`, `es-abstract`, `function-bind`, `has`
  * [Dev De<PERSON>] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape`
  * [Tests] up to `node` `v11.6`, `v10.15`, `v9.11`, `v8.15`, `v7.10`, `v6.16`, `v4.9`; use `nvm install-latest-npm`
  * [Tests] use `npm audit` instead of `nsp`
  * [Tests] remove `jscs`

1.0.4 / 2016-12-04
=================
  * [Docs] update to reflect ES2017 inclusion
  * [Deps] update `es-abstract`, `function-bind`, `define-properties`
  * [Dev <PERSON>] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`
  * [Tests] up to `node` `v7.2`, `v6.9`, `v4.6`; improve test matrix.

1.0.3 / 2015-10-06
=================
  * [Fix] Not-yet-visited keys made non-enumerable on a `[[Get]]` must not show up in the output (https://github.com/ljharb/proposal-object-values-entries/issues/5)

1.0.2 / 2015-09-25
=================
  * [Fix] Not-yet-visited keys deleted on a `[[Get]]` must not show up in the output (#1)

1.0.1 / 2015-09-21
=================
  * [Docs] update version badge URL
  * [Tests] on `io.js` `v3.3`, up to `node` `v4.1`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`

1.0.0 / 2015-09-02
=================
  * v1.0.0
