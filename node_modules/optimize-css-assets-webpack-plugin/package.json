{"name": "optimize-css-assets-webpack-plugin", "version": "5.0.3", "description": "A Webpack plugin to optimize \\ minimize CSS assets.", "keywords": ["CSS", "duplicate", "extract-text-webpack-plugin", "minimize", "optimize", "remove", "webpack"], "homepage": "http://github.com/NMFR/optimize-css-assets-webpack-plugin", "license": "MIT", "author": "<PERSON><PERSON>", "main": "src/index.js", "repository": {"type": "git", "url": "http://github.com/NMFR/optimize-css-assets-webpack-plugin.git"}, "scripts": {"test": "jest", "test:watch": "jest --watch"}, "jest": {"testEnvironment": "node", "watchPathIgnorePatterns": ["<rootDir>/test/js/*.*"]}, "dependencies": {"cssnano": "^4.1.10", "last-call-webpack-plugin": "^3.0.0"}, "devDependencies": {"babel-core": "^6.26.3", "babel-jest": "^22.1.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "css-loader": "^3.0.0", "extract-text-webpack-plugin": "next", "jest": "^24.8.0", "style-loader": "^0.20.1", "webpack": "^4.9.1"}, "peerDependencies": {"webpack": "^4.0.0"}}