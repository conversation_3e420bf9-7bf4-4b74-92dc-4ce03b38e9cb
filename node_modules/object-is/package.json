{"name": "object-is", "version": "1.0.1", "description": "ES6-compliant shim for Object.is - differentiates between -0 and +0", "author": "<PERSON>", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test.js && npm run coverage-quiet", "coverage": "covert test.js", "coverage-quiet": "covert test.js --quiet", "lint": "jscs *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-is.git"}, "bugs": {"url": "https://github.com/ljharb/object-is/issues"}, "homepage": "https://github.com/ljharb/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "shim", "polyfill"], "dependencies": {}, "devDependencies": {"tape": "~2.14.0", "covert": "~1.0.0", "jscs": "~1.5.9"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}