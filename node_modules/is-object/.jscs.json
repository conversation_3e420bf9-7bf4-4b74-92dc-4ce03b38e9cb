{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch", "function"], "disallowSpaceAfterKeywords": [], "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true, "beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "requireSpacesInFunctionDeclaration": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}, "disallowSpacesInsideParentheses": true, "disallowSpacesInsideArrayBrackets": true, "disallowQuotedKeysInObjects": "allButReserved", "disallowSpaceAfterObjectKeys": true, "requireCommaBeforeLineBreak": true, "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~", "!"], "requireSpaceAfterPrefixUnaryOperators": [], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "requireSpaceBeforePostfixUnaryOperators": [], "disallowSpaceBeforeBinaryOperators": [], "requireSpaceBeforeBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!=="], "requireSpaceAfterBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!=="], "disallowSpaceAfterBinaryOperators": [], "disallowImplicitTypeConversion": ["binary", "string"], "disallowKeywords": ["with", "eval"], "validateLineBreaks": "LF", "requireKeywordsOnNewLine": [], "disallowKeywordsOnNewLine": ["else"], "requireLineFeedAtFileEnd": true, "disallowTrailingWhitespace": true, "excludeFiles": ["node_modules/**", "vendor/**"], "disallowMultipleLineStrings": true, "additionalRules": []}