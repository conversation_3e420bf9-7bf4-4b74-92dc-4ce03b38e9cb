{"name": "is-retry-allowed", "version": "1.2.0", "description": "Is retry allowed for Error?", "license": "MIT", "repository": "floatdrop/is-retry-allowed", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": [""], "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "xo": "^0.12.1"}}