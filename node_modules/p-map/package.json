{"name": "p-map", "version": "2.1.0", "description": "Map over promises concurrently", "license": "MIT", "repository": "sindresorhus/p-map", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "map", "resolved", "wait", "collection", "iterable", "iterator", "race", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "in-range": "^1.0.0", "random-int": "^1.0.0", "time-span": "^3.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}